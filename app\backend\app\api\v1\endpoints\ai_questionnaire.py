"""
AI Questionnaire Generation and Evaluation API Endpoints
"""
from typing import Any, Dict, List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query, Body
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_

from app.api.deps import get_db, get_current_user
from app.models.user import User
from app.models.questionnaire import Questionnaire, QuestionnaireStatus
from app.models.evaluation_report import EvaluationReport
from app.models.audit_log import AuditLog
from app.services.generation_service import generation_service
from app.services.evaluation_service import evaluation_service
from app.services.llm_service import llm_service
from app.schemas.ai_questionnaire import (
    QuestionnaireGenerateRequest,
    QuestionnaireEvaluateRequest,
    EvaluationReportResponse,
    QuestionnairePublishRequest,
    QuestionnaireReviewRequest,
    CandidateComparisonRequest
)
from app.core.id_types import generate_snowflake_id
from datetime import datetime, timezone
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/ai-questionnaire", tags=["AI Questionnaire"])


@router.post("/generate")
async def generate_questionnaire(
    *,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    request: QuestionnaireGenerateRequest = Body(...)
) -> Any:
    """
    Generate an AI-powered questionnaire for a specific position type.
    
    Args:
        request: Generation request with position type and parameters
    
    Returns:
        Generated questionnaire with questions and evaluation criteria
    """
    try:
        # Log audit event
        audit_log = AuditLog(
            id=generate_snowflake_id(),
            user_id=current_user.id,
            action="GENERATE_QUESTIONNAIRE",
            resource_type="questionnaire",
            changes={
                "position_type": request.position_type,
                "question_count": request.question_count,
                "dimensions": request.dimensions
            }
        )
        db.add(audit_log)
        
        # Generate questionnaire using AI service
        generated_data = await generation_service.generate_questionnaire(
            position_type=request.position_type,
            dimensions=request.dimensions,
            question_count=request.question_count,
            industry=request.industry or "烟草",
            custom_requirements=request.custom_requirements
        )
        
        # Convert to questionnaire model
        questionnaire = await generation_service.convert_to_questionnaire_model(
            generated_data=generated_data,
            created_by=current_user.id,
            position_type=request.position_type,
            industry=request.industry or "烟草"
        )
        
        # Add questions from generated data
        if "questions" in generated_data:
            questionnaire.questions = generated_data["questions"]
        
        # Save to database
        db.add(questionnaire)
        await db.commit()
        await db.refresh(questionnaire)
        
        # Update audit log with resource ID
        audit_log.resource_id = questionnaire.id
        await db.commit()
        
        return questionnaire
        
    except Exception as e:
        logger.error(f"Failed to generate questionnaire: {str(e)}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate questionnaire: {str(e)}"
        )


@router.post("/evaluate/{questionnaire_id}", response_model=EvaluationReportResponse)
async def evaluate_responses(
    *,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    questionnaire_id: int,
    request: QuestionnaireEvaluateRequest = Body(...)
) -> Any:
    """
    Evaluate candidate responses using AI.
    
    Args:
        questionnaire_id: ID of the questionnaire
        request: Evaluation request with responses
    
    Returns:
        AI-generated evaluation report with scores and insights
    """
    try:
        # Check questionnaire exists
        questionnaire = await db.get(Questionnaire, questionnaire_id)
        if not questionnaire:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Questionnaire not found"
            )
        
        # Log audit event
        audit_log = AuditLog(
            id=generate_snowflake_id(),
            user_id=current_user.id,
            action="EVALUATE_RESPONSES",
            resource_type="evaluation_report",
            changes={
                "questionnaire_id": questionnaire_id,
                "candidate_id": request.candidate_id,
                "response_count": len(request.responses)
            }
        )
        db.add(audit_log)
        
        # Evaluate responses using AI service
        evaluation_report = await evaluation_service.evaluate_responses(
            questionnaire_id=questionnaire_id,
            candidate_id=request.candidate_id,
            submission_id=request.submission_id,
            responses=request.responses,
            db=db
        )
        
        # Save evaluation report
        db.add(evaluation_report)
        await db.commit()
        await db.refresh(evaluation_report)
        
        # Update audit log with resource ID
        audit_log.resource_id = evaluation_report.id
        await db.commit()
        
        return evaluation_report
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to evaluate responses: {str(e)}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to evaluate responses: {str(e)}"
        )


@router.get("/templates", response_model=List[Dict[str, Any]])
async def get_available_templates(
    *,
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    Get available AI questionnaire templates for different position types.
    
    Returns:
        List of available templates with descriptions
    """
    from app.services.generation_service import TobaccoIndustryTemplates
    
    templates = []
    for position_type, template_data in TobaccoIndustryTemplates.TEMPLATES.items():
        templates.append({
            "position_type": position_type,
            "description": template_data["description"],
            "dimensions": template_data["dimensions"],
            "question_types": template_data["question_types"],
            "focus_areas": template_data["focus_areas"]
        })
    
    return templates


@router.post("/{questionnaire_id}/publish")
async def publish_questionnaire(
    *,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    questionnaire_id: int,
    slug: str = Body(..., description="URL slug for public access"),
    valid_until: Optional[datetime] = Body(None, description="Expiration date"),
    access_type: str = Body("private", description="Access type: private, public, password"),
    max_submissions: Optional[int] = Body(None, description="Maximum number of submissions")
) -> Any:
    """
    Publish a questionnaire for candidates to access.
    
    Args:
        questionnaire_id: ID of the questionnaire to publish
        slug: URL-friendly identifier
        valid_until: Optional expiration date
        access_type: Access control type
        max_submissions: Optional submission limit
    
    Returns:
        Published questionnaire with access details
    """
    try:
        # Get questionnaire
        questionnaire = await db.get(Questionnaire, questionnaire_id)
        if not questionnaire:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Questionnaire not found"
            )
        
        # Check ownership
        if questionnaire.created_by != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You don't have permission to publish this questionnaire"
            )
        
        # Check if slug is unique
        existing = await db.execute(
            select(Questionnaire).where(
                and_(
                    Questionnaire.slug == slug,
                    Questionnaire.id != questionnaire_id
                )
            )
        )
        if existing.scalar_one_or_none():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="This slug is already in use"
            )
        
        # Update questionnaire with publishing details
        questionnaire.slug = slug
        questionnaire.valid_until = valid_until
        questionnaire.access_type = access_type
        questionnaire.max_submissions = max_submissions
        questionnaire.status = QuestionnaireStatus.PUBLISHED
        questionnaire.updated_at = datetime.now(timezone.utc)
        
        # Log audit event
        audit_log = AuditLog(
            id=generate_snowflake_id(),
            user_id=current_user.id,
            action="PUBLISH_QUESTIONNAIRE",
            resource_type="questionnaire",
            resource_id=questionnaire_id,
            changes={
                "slug": slug,
                "access_type": access_type,
                "valid_until": valid_until.isoformat() if valid_until else None,
                "max_submissions": max_submissions
            }
        )
        db.add(audit_log)
        
        await db.commit()
        await db.refresh(questionnaire)
        
        return {
            "id": questionnaire.id,
            "slug": questionnaire.slug,
            "public_url": f"/questionnaire/{slug}",
            "access_type": questionnaire.access_type,
            "valid_until": questionnaire.valid_until,
            "max_submissions": questionnaire.max_submissions,
            "status": questionnaire.status
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to publish questionnaire: {str(e)}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to publish questionnaire: {str(e)}"
        )


@router.post("/{questionnaire_id}/review")
async def review_questionnaire(
    *,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    questionnaire_id: int,
    approved: bool = Body(..., description="Approval decision"),
    comments: Optional[str] = Body(None, description="Review comments"),
    rejection_reason: Optional[str] = Body(None, description="Reason for rejection if not approved")
) -> Any:
    """
    Review and approve/reject an AI-generated questionnaire.
    
    Args:
        questionnaire_id: ID of the questionnaire to review
        approved: Whether to approve the questionnaire
        comments: Optional review comments
        rejection_reason: Required if not approved
    
    Returns:
        Updated questionnaire with review status
    """
    try:
        # Get questionnaire
        questionnaire = await db.get(Questionnaire, questionnaire_id)
        if not questionnaire:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Questionnaire not found"
            )
        
        # Check if user has review permissions (could check for admin role)
        # For now, we'll allow the creator to review their own questionnaire
        
        # Update review fields
        questionnaire.reviewed_by = current_user.id
        questionnaire.review_comments = comments
        
        if approved:
            questionnaire.status = QuestionnaireStatus.APPROVED
            questionnaire.approved_at = datetime.now(timezone.utc)
            questionnaire.rejection_reason = None
        else:
            if not rejection_reason:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Rejection reason is required when not approving"
                )
            questionnaire.status = QuestionnaireStatus.REJECTED
            questionnaire.rejection_reason = rejection_reason
            questionnaire.approved_at = None
        
        questionnaire.updated_at = datetime.now(timezone.utc)
        
        # Log audit event
        audit_log = AuditLog(
            id=generate_snowflake_id(),
            user_id=current_user.id,
            action="REVIEW_QUESTIONNAIRE",
            resource_type="questionnaire",
            resource_id=questionnaire_id,
            changes={
                "approved": approved,
                "comments": comments,
                "rejection_reason": rejection_reason
            }
        )
        db.add(audit_log)
        
        await db.commit()
        await db.refresh(questionnaire)
        
        return {
            "id": questionnaire.id,
            "status": questionnaire.status,
            "reviewed_by": questionnaire.reviewed_by,
            "review_comments": questionnaire.review_comments,
            "rejection_reason": questionnaire.rejection_reason,
            "approved_at": questionnaire.approved_at
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to review questionnaire: {str(e)}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to review questionnaire: {str(e)}"
        )


@router.post("/compare-candidates")
async def compare_candidates(
    *,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    questionnaire_id: int = Body(..., description="Questionnaire ID"),
    candidate_ids: List[int] = Body(..., description="List of candidate IDs to compare")
) -> Any:
    """
    Compare multiple candidates' evaluation results for the same questionnaire.
    
    Args:
        questionnaire_id: ID of the questionnaire
        candidate_ids: List of candidate IDs to compare
    
    Returns:
        Comparison data with rankings and insights
    """
    try:
        # Validate questionnaire exists
        questionnaire = await db.get(Questionnaire, questionnaire_id)
        if not questionnaire:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Questionnaire not found"
            )
        
        # Log audit event
        audit_log = AuditLog(
            id=generate_snowflake_id(),
            user_id=current_user.id,
            action="COMPARE_CANDIDATES",
            resource_type="evaluation_report",
            changes={
                "questionnaire_id": questionnaire_id,
                "candidate_ids": candidate_ids
            }
        )
        db.add(audit_log)
        await db.commit()
        
        # Compare candidates using evaluation service
        comparison = await evaluation_service.compare_candidates(
            questionnaire_id=questionnaire_id,
            candidate_ids=candidate_ids,
            db=db
        )
        
        return comparison
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to compare candidates: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to compare candidates: {str(e)}"
        )