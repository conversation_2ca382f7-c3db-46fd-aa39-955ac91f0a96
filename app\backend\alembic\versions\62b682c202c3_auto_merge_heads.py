"""auto_merge_heads

Revision ID: 62b682c202c3
Revises: 4a82c9f94ac5
Create Date: 2025-08-11 01:08:07.807191

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '62b682c202c3'
down_revision: Union[str, None] = '4a82c9f94ac5'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass