"""
Constants and mappings for the application
"""

# Education level mappings (bidirectional)
EDUCATION_CODE_TO_CHINESE = {
    "high_school": "高中",
    "associate": "专科",
    "bachelor": "本科",
    "master": "硕士",
    "doctorate": "博士",
    "other": "其他"
}

EDUCATION_CHINESE_TO_CODE = {v: k for k, v in EDUCATION_CODE_TO_CHINESE.items()}

# Also handle English variants that might come from parsed resumes
EDUCATION_ENGLISH_TO_CODE = {
    "High School": "high_school",
    "Associate": "associate",
    "Bachelor": "bachelor",
    "Master": "master",
    "Doctorate": "doctorate",
    "PhD": "doctorate",
    "MBA": "master",
    "Other": "other"
}

def normalize_education_level(value: str) -> str:
    """
    Normalize education level to Chinese for database storage
    
    Args:
        value: Education level in any format (code, Chinese, English)
    
    Returns:
        Chinese education level for database storage
    """
    if not value:
        return value
    
    # If it's already Chinese, return as is
    if value in EDUCATION_CHINESE_TO_CODE:
        return value
    
    # If it's a code, convert to Chinese
    if value in EDUCATION_CODE_TO_CHINESE:
        return EDUCATION_CODE_TO_CHINESE[value]
    
    # If it's English, convert to code first, then to Chinese
    if value in EDUCATION_ENGLISH_TO_CODE:
        code = EDUCATION_ENGLISH_TO_CODE[value]
        return EDUCATION_CODE_TO_CHINESE.get(code, value)
    
    # Default: return as is
    return value

def education_to_code(value: str) -> str:
    """
    Convert education level to code for API responses
    
    Args:
        value: Education level in Chinese or English
    
    Returns:
        Education level code
    """
    if not value:
        return value
    
    # If it's Chinese, convert to code
    if value in EDUCATION_CHINESE_TO_CODE:
        return EDUCATION_CHINESE_TO_CODE[value]
    
    # If it's already a code, return as is
    if value in EDUCATION_CODE_TO_CHINESE:
        return value
    
    # If it's English, convert to code
    if value in EDUCATION_ENGLISH_TO_CODE:
        return EDUCATION_ENGLISH_TO_CODE[value]
    
    # Default: return as is
    return value