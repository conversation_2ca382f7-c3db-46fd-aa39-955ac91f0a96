"""auto_merge_heads

Revision ID: 13ce126adc2e
Revises: 26245aed3962
Create Date: 2025-08-12 04:59:20.868861

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '13ce126adc2e'
down_revision: Union[str, None] = '26245aed3962'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass