"""
Redis cache utilities for the monitoring system
"""
import json
import redis
from typing import Optional, Dict, Any, Union
from datetime import datetime, timedelta
import logging
from app.core.config import settings

logger = logging.getLogger(__name__)


class MonitoringCache:
    """Redis cache manager specifically for monitoring system"""
    
    def __init__(self):
        """Initialize Redis connection for monitoring cache"""
        self.redis_client = redis.Redis(
            host=settings.REDIS_HOST,
            port=settings.REDIS_PORT,
            password=settings.REDIS_PASSWORD,
            db=settings.REDIS_MONITORING_DB or 2,  # Use dedicated DB for monitoring
            decode_responses=True,
            socket_timeout=5.0,
            socket_connect_timeout=5.0,
            retry_on_timeout=True,
            max_connections=10
        )
        
        # Cache configuration
        self.cache_ttl = 300  # 5 minutes default TTL
        self.key_prefix = "monitoring"
        self.cache_version = "v1"
        
    def _get_key(self, key_suffix: str) -> str:
        """Generate cache key with prefix and version"""
        return f"{self.key_prefix}:{key_suffix}:{self.cache_version}"
    
    def set_system_health(self, health_data: Dict[str, Any], ttl: Optional[int] = None) -> bool:
        """
        Store system health data in cache
        
        Args:
            health_data: System health data
            ttl: Time to live in seconds
            
        Returns:
            Success status
        """
        try:
            cache_key = self._get_key("system_health")
            ttl = ttl or self.cache_ttl
            
            # Wrap data with metadata
            cache_data = {
                "data": health_data,
                "timestamp": datetime.utcnow().isoformat(),
                "generated_by": "monitoring_service",
                "cache_version": self.cache_version,
                "ttl": ttl
            }
            
            success = self.redis_client.setex(
                cache_key,
                ttl,
                json.dumps(cache_data, default=str)
            )
            
            if success:
                logger.debug(f"✅ Cached system health data with TTL {ttl}s")
                # Also update last check timestamp
                self.set_last_check(datetime.utcnow().isoformat())
                return True
            else:
                logger.error("❌ Failed to cache system health data")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error caching system health: {e}")
            return False
    
    def get_system_health(self) -> Optional[Dict[str, Any]]:
        """
        Get system health data from cache
        
        Returns:
            Cached health data or None if not found/expired
        """
        try:
            cache_key = self._get_key("system_health")
            cached_data = self.redis_client.get(cache_key)
            
            if cached_data:
                cache_data = json.loads(cached_data)
                logger.debug(f"✅ Cache hit for system health (generated: {cache_data.get('timestamp')})")
                return cache_data["data"]
            else:
                logger.debug("⚠️ Cache miss for system health")
                return None
                
        except Exception as e:
            logger.error(f"❌ Error retrieving cached system health: {e}")
            return None
    
    def set_service_health(self, service_name: str, service_data: Dict[str, Any], ttl: Optional[int] = None) -> bool:
        """
        Store individual service health data
        
        Args:
            service_name: Name of the service
            service_data: Service health data
            ttl: Time to live in seconds
            
        Returns:
            Success status
        """
        try:
            cache_key = self._get_key(f"service:{service_name}")
            ttl = ttl or self.cache_ttl
            
            cache_data = {
                "data": service_data,
                "service_name": service_name,
                "timestamp": datetime.utcnow().isoformat(),
                "cache_version": self.cache_version
            }
            
            success = self.redis_client.setex(
                cache_key,
                ttl,
                json.dumps(cache_data, default=str)
            )
            
            if success:
                logger.debug(f"✅ Cached {service_name} health data")
                return True
            else:
                logger.error(f"❌ Failed to cache {service_name} health data")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error caching service health for {service_name}: {e}")
            return False
    
    def get_service_health(self, service_name: str) -> Optional[Dict[str, Any]]:
        """
        Get individual service health data from cache
        
        Args:
            service_name: Name of the service
            
        Returns:
            Cached service data or None if not found
        """
        try:
            cache_key = self._get_key(f"service:{service_name}")
            cached_data = self.redis_client.get(cache_key)
            
            if cached_data:
                cache_data = json.loads(cached_data)
                logger.debug(f"✅ Cache hit for {service_name} health")
                return cache_data["data"]
            else:
                logger.debug(f"⚠️ Cache miss for {service_name} health")
                return None
                
        except Exception as e:
            logger.error(f"❌ Error retrieving cached service health for {service_name}: {e}")
            return None
    
    def set_system_metrics(self, metrics_data: Dict[str, Any], ttl: Optional[int] = None) -> bool:
        """
        Store system metrics in cache
        
        Args:
            metrics_data: System metrics data
            ttl: Time to live in seconds
            
        Returns:
            Success status
        """
        try:
            cache_key = self._get_key("system_metrics")
            ttl = ttl or self.cache_ttl
            
            cache_data = {
                "data": metrics_data,
                "timestamp": datetime.utcnow().isoformat(),
                "cache_version": self.cache_version
            }
            
            success = self.redis_client.setex(
                cache_key,
                ttl,
                json.dumps(cache_data, default=str)
            )
            
            if success:
                logger.debug(f"✅ Cached system metrics with TTL {ttl}s")
                return True
            else:
                logger.error("❌ Failed to cache system metrics")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error caching system metrics: {e}")
            return False
    
    def get_system_metrics(self) -> Optional[Dict[str, Any]]:
        """
        Get system metrics from cache
        
        Returns:
            Cached metrics data or None if not found
        """
        try:
            cache_key = self._get_key("system_metrics")
            cached_data = self.redis_client.get(cache_key)
            
            if cached_data:
                cache_data = json.loads(cached_data)
                logger.debug(f"✅ Cache hit for system metrics")
                return cache_data["data"]
            else:
                logger.debug("⚠️ Cache miss for system metrics")
                return None
                
        except Exception as e:
            logger.error(f"❌ Error retrieving cached system metrics: {e}")
            return None
    
    def set_last_check(self, timestamp: str) -> bool:
        """
        Set the last monitoring check timestamp
        
        Args:
            timestamp: ISO format timestamp
            
        Returns:
            Success status
        """
        try:
            cache_key = self._get_key("last_check")
            success = self.redis_client.setex(
                cache_key,
                self.cache_ttl,
                timestamp
            )
            return bool(success)
        except Exception as e:
            logger.error(f"❌ Error setting last check timestamp: {e}")
            return False
    
    def get_last_check(self) -> Optional[str]:
        """
        Get the last monitoring check timestamp
        
        Returns:
            ISO format timestamp or None if not found
        """
        try:
            cache_key = self._get_key("last_check")
            timestamp = self.redis_client.get(cache_key)
            return timestamp
        except Exception as e:
            logger.error(f"❌ Error getting last check timestamp: {e}")
            return None
    
    def clear_all_cache(self) -> int:
        """
        Clear all monitoring-related cache entries
        
        Returns:
            Number of keys deleted
        """
        try:
            pattern = f"{self.key_prefix}:*"
            keys = self.redis_client.keys(pattern)
            
            if keys:
                deleted = self.redis_client.delete(*keys)
                logger.info(f"🧹 Cleared {deleted} monitoring cache entries")
                return deleted
            else:
                logger.debug("🧹 No monitoring cache entries to clear")
                return 0
                
        except Exception as e:
            logger.error(f"❌ Error clearing monitoring cache: {e}")
            return 0
    
    def get_cache_info(self) -> Dict[str, Any]:
        """
        Get information about current cache usage
        
        Returns:
            Cache information including key status and TTL
        """
        try:
            cache_keys = [
                "system_health",
                "system_metrics",
                "last_check"
            ]
            
            cache_info = {}
            
            for key_suffix in cache_keys:
                cache_key = self._get_key(key_suffix)
                exists = self.redis_client.exists(cache_key)
                ttl = self.redis_client.ttl(cache_key) if exists else -1
                
                cache_info[key_suffix] = {
                    "exists": bool(exists),
                    "ttl_seconds": ttl if ttl > 0 else 0,
                    "expires_at": (
                        datetime.utcnow() + timedelta(seconds=ttl)
                    ).isoformat() if ttl > 0 else None
                }
            
            # Add service-specific cache info
            service_pattern = f"{self.key_prefix}:service:*:{self.cache_version}"
            service_keys = self.redis_client.keys(service_pattern)
            
            cache_info["services"] = {}
            for key in service_keys:
                service_name = key.split(":")[2]  # Extract service name from key
                ttl = self.redis_client.ttl(key)
                cache_info["services"][service_name] = {
                    "exists": True,
                    "ttl_seconds": ttl if ttl > 0 else 0,
                    "expires_at": (
                        datetime.utcnow() + timedelta(seconds=ttl)
                    ).isoformat() if ttl > 0 else None
                }
            
            return cache_info
            
        except Exception as e:
            logger.error(f"❌ Error getting cache info: {e}")
            return {"error": str(e)}
    
    def health_check(self) -> Dict[str, Any]:
        """
        Perform health check on Redis connection
        
        Returns:
            Health check result
        """
        try:
            # Test basic operations
            test_key = f"{self.key_prefix}:health_test"
            test_value = f"test_{datetime.utcnow().timestamp()}"
            
            # Set and get test
            self.redis_client.setex(test_key, 5, test_value)
            retrieved = self.redis_client.get(test_key)
            self.redis_client.delete(test_key)
            
            if retrieved == test_value:
                return {
                    "status": "healthy",
                    "response_time_ms": 0,  # Could measure this
                    "details": "Redis connection and operations working normally"
                }
            else:
                return {
                    "status": "unhealthy",
                    "error": "Redis test operations failed",
                    "details": f"Expected {test_value}, got {retrieved}"
                }
                
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "details": "Failed to connect to Redis or perform operations"
            }


# Create singleton instance
monitoring_cache = MonitoringCache()