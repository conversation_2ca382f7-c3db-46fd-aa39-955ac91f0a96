"""
Custom exceptions for TalentForge Pro
"""
from typing import Optional, Any, Dict
from fastapi import HTTPException, status


class ServiceError(Exception):
    """Base service layer exception"""
    
    def __init__(
        self,
        message: str = "Service error occurred",
        *,
        code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.code = code or self.__class__.__name__
        self.details = details or {}
        super().__init__(self.message)


class NotFoundError(ServiceError):
    """Resource not found exception"""
    
    def __init__(self, resource: str, identifier: Any):
        super().__init__(
            message=f"{resource} not found: {identifier}",
            code="NOT_FOUND",
            details={"resource": resource, "identifier": str(identifier)}
        )


class PermissionError(ServiceError):
    """Permission denied exception"""
    
    def __init__(self, action: str, resource: Optional[str] = None):
        message = f"Permission denied for action: {action}"
        if resource:
            message += f" on resource: {resource}"
        super().__init__(
            message=message,
            code="PERMISSION_DENIED",
            details={"action": action, "resource": resource}
        )


class ValidationError(ServiceError):
    """Validation error exception"""
    
    def __init__(self, field: str, reason: str):
        super().__init__(
            message=f"Validation failed for {field}: {reason}",
            code="VALIDATION_ERROR",
            details={"field": field, "reason": reason}
        )


class DuplicateError(ServiceError):
    """Duplicate resource exception"""
    
    def __init__(self, resource: str, field: str, value: Any):
        super().__init__(
            message=f"{resource} already exists with {field}: {value}",
            code="DUPLICATE_ERROR",
            details={"resource": resource, "field": field, "value": str(value)}
        )


class ConflictError(ServiceError):
    """Resource conflict exception"""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            code="CONFLICT_ERROR",
            details=details or {}
        )


class AuthenticationError(ServiceError):
    """Authentication failed exception"""
    
    def __init__(self, message: str = "Authentication failed"):
        super().__init__(message=message, code="AUTHENTICATION_ERROR")


class TokenError(ServiceError):
    """Token related exception"""
    
    def __init__(self, message: str = "Token invalid or expired"):
        super().__init__(message=message, code="TOKEN_ERROR")


class RateLimitError(ServiceError):
    """Rate limit exceeded exception"""
    
    def __init__(self, limit: int, window: str):
        super().__init__(
            message=f"Rate limit exceeded: {limit} requests per {window}",
            code="RATE_LIMIT_EXCEEDED",
            details={"limit": limit, "window": window}
        )


class ExternalServiceError(ServiceError):
    """External service error exception"""
    
    def __init__(self, service: str, message: str):
        super().__init__(
            message=f"External service error ({service}): {message}",
            code="EXTERNAL_SERVICE_ERROR",
            details={"service": service}
        )


# HTTP Exception helpers
def not_found(
    error_code: str = "RESOURCE_NOT_FOUND",
    detail: Optional[str] = None,
    details: Optional[Dict[str, Any]] = None
) -> HTTPException:
    """Create 404 Not Found exception"""
    from datetime import datetime, timezone
    
    response_detail = {
        "error_code": error_code,
        "detail": detail or "Resource not found",
        "details": details or {},
        "timestamp": datetime.now(timezone.utc).isoformat()
    }
    return HTTPException(
        status_code=status.HTTP_404_NOT_FOUND,
        detail=response_detail
    )


def unauthorized(
    error_code: str = "AUTH_UNAUTHORIZED", 
    detail: Optional[str] = None,
    details: Optional[Dict[str, Any]] = None
) -> HTTPException:
    """Create 401 Unauthorized exception"""
    from datetime import datetime, timezone
    
    response_detail = {
        "error_code": error_code,
        "detail": detail or "Authentication failed",
        "details": details or {},
        "timestamp": datetime.now(timezone.utc).isoformat()
    }
    return HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail=response_detail,
        headers={"WWW-Authenticate": "Bearer"}
    )


def forbidden(
    error_code: str = "AUTH_FORBIDDEN",
    detail: Optional[str] = None,
    details: Optional[Dict[str, Any]] = None
) -> HTTPException:
    """Create 403 Forbidden exception"""
    from datetime import datetime, timezone
    
    response_detail = {
        "error_code": error_code,
        "detail": detail or "Access forbidden",
        "details": details or {},
        "timestamp": datetime.now(timezone.utc).isoformat()
    }
    return HTTPException(
        status_code=status.HTTP_403_FORBIDDEN,
        detail=response_detail
    )


def bad_request(
    error_code: str = "REQUEST_INVALID",
    detail: Optional[str] = None,
    details: Optional[Dict[str, Any]] = None
) -> HTTPException:
    """Create 400 Bad Request exception"""
    from datetime import datetime, timezone
    
    response_detail = {
        "error_code": error_code,
        "detail": detail or "Bad request",
        "details": details or {},
        "timestamp": datetime.now(timezone.utc).isoformat()
    }
    return HTTPException(
        status_code=status.HTTP_400_BAD_REQUEST,
        detail=response_detail
    )


def conflict(
    error_code: str = "RESOURCE_CONFLICT",
    detail: Optional[str] = None,
    details: Optional[Dict[str, Any]] = None
) -> HTTPException:
    """Create 409 Conflict exception"""
    from datetime import datetime, timezone
    
    response_detail = {
        "error_code": error_code,
        "detail": detail or "Resource conflict",
        "details": details or {},
        "timestamp": datetime.now(timezone.utc).isoformat()
    }
    return HTTPException(
        status_code=status.HTTP_409_CONFLICT,
        detail=response_detail
    )


def unprocessable_entity(
    error_code: str = "REQUEST_VALIDATION_FAILED",
    detail: Optional[str] = None,
    details: Optional[Dict[str, Any]] = None
) -> HTTPException:
    """Create 422 Unprocessable Entity exception"""
    from datetime import datetime, timezone
    
    response_detail = {
        "error_code": error_code,
        "detail": detail or "Validation failed",
        "details": details or {},
        "timestamp": datetime.now(timezone.utc).isoformat()
    }
    return HTTPException(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        detail=response_detail
    )


def internal_server_error(
    error_code: str = "INTERNAL_ERROR",
    detail: Optional[str] = None,
    details: Optional[Dict[str, Any]] = None
) -> HTTPException:
    """Create 500 Internal Server Error exception"""
    from datetime import datetime, timezone
    
    response_detail = {
        "error_code": error_code,
        "detail": detail or "Internal server error",
        "details": details or {},
        "timestamp": datetime.now(timezone.utc).isoformat()
    }
    return HTTPException(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        detail=response_detail
    )


def service_error(
    error_code: str = "SERVICE_ERROR",
    detail: Optional[str] = None,
    details: Optional[Dict[str, Any]] = None
) -> HTTPException:
    """Create 500 Service Error exception"""
    from datetime import datetime, timezone
    
    response_detail = {
        "error_code": error_code,
        "detail": detail or "Service error occurred",
        "details": details or {},
        "timestamp": datetime.now(timezone.utc).isoformat()
    }
    return HTTPException(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        detail=response_detail
    )


def too_many_requests(
    error_code: str = "RATE_LIMIT_EXCEEDED",
    detail: Optional[str] = None,
    details: Optional[Dict[str, Any]] = None
) -> HTTPException:
    """Create 429 Too Many Requests exception"""
    from datetime import datetime, timezone
    
    response_detail = {
        "error_code": error_code,
        "detail": detail or "Too many requests. Please slow down.",
        "details": details or {},
        "timestamp": datetime.now(timezone.utc).isoformat()
    }
    return HTTPException(
        status_code=status.HTTP_429_TOO_MANY_REQUESTS,
        detail=response_detail
    )