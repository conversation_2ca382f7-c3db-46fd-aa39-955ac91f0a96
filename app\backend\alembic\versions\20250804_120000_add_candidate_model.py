"""Add candidate model and related tables

Revision ID: 20250804_120000
Revises: 20250729_091225
Create Date: 2025-08-04 12:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '20250804_120000'
down_revision: Union[str, None] = '20250729_091225'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Get the database connection
    bind = op.get_bind()
    
    # Check if candidates table already exists
    result = bind.execute(
        sa.text("SELECT 1 FROM information_schema.tables WHERE table_name = 'candidates'")
    ).fetchone()
    
    if result:
        # Table already exists, skip creation
        return
    
    # Create enums with create_before_use=False to prevent automatic creation
    candidate_status_enum = postgresql.ENUM(
        'new', 'screening', 'interview', 'offer', 'hired', 'rejected', 'withdrawn',
        name='candidatestatus',
        create_type=False  # Don't auto-create when used in column
    )
    
    data_permission_enum = postgresql.ENUM(
        'PRIVATE', 'SHARED', 'TEAM', 'PUBLIC',
        name='datapermission',
        create_type=False  # Don't auto-create when used in column
    )
    
    # Manually create the enums if they don't exist
    # Check and create candidatestatus enum
    result = bind.execute(
        sa.text("SELECT 1 FROM pg_type WHERE typname = 'candidatestatus'")
    ).fetchone()
    
    if not result:
        bind.execute(sa.text("""
            CREATE TYPE candidatestatus AS ENUM ('new', 'screening', 'interview', 'offer', 'hired', 'rejected', 'withdrawn')
        """))
    
    # Check and create datapermission enum
    result = bind.execute(
        sa.text("SELECT 1 FROM pg_type WHERE typname = 'datapermission'")
    ).fetchone()
    
    if not result:
        bind.execute(sa.text("""
            CREATE TYPE datapermission AS ENUM ('PRIVATE', 'SHARED', 'TEAM', 'PUBLIC')
        """))
    
    # Create candidates table
    op.create_table(
        'candidates',
        sa.Column('id', sa.BigInteger(), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        
        # Basic information
        sa.Column('name', sa.String(length=100), nullable=False),
        sa.Column('email', sa.String(length=255), nullable=True),
        sa.Column('phone', sa.String(length=20), nullable=True),
        sa.Column('gender', sa.String(length=10), nullable=True),
        sa.Column('birth_date', sa.DateTime(), nullable=True),
        
        # Professional information
        sa.Column('current_position', sa.String(length=200), nullable=True),
        sa.Column('current_company', sa.String(length=200), nullable=True),
        sa.Column('years_of_experience', sa.Integer(), nullable=True, default=0),
        sa.Column('current_salary', sa.Integer(), nullable=True),
        sa.Column('expected_salary', sa.Integer(), nullable=True),
        
        # Structured data (JSONB)
        sa.Column('education', postgresql.JSONB(), nullable=True, default=sa.text('\'[]\'::jsonb')),
        sa.Column('work_experience', postgresql.JSONB(), nullable=True, default=sa.text('\'[]\'::jsonb')),
        sa.Column('skills', postgresql.JSONB(), nullable=True, default=sa.text('\'[]\'::jsonb')),
        
        # Resume information
        sa.Column('resume_url', sa.String(length=500), nullable=True),
        sa.Column('resume_text', sa.Text(), nullable=True),
        sa.Column('resume_parsed_at', sa.DateTime(timezone=True), nullable=True),
        
        # Assessment information
        sa.Column('dci_score', sa.Float(), nullable=True),
        sa.Column('jfs_score', sa.Float(), nullable=True),
        sa.Column('assessment_data', postgresql.JSONB(), nullable=True),
        
        # Status and permissions
        sa.Column('status', candidate_status_enum, nullable=False, default='new'),
        sa.Column('data_permission', data_permission_enum, nullable=False, default='PRIVATE'),
        sa.Column('shared_with', postgresql.JSONB(), nullable=True, default=sa.text('\'[]\'::jsonb')),
        
        # Source information
        sa.Column('source', sa.String(length=50), nullable=True),
        sa.Column('referrer_id', sa.BigInteger(), sa.ForeignKey('users.id'), nullable=True),
        
        # Notes and tags
        sa.Column('notes', sa.Text(), nullable=True),
        sa.Column('tags', postgresql.JSONB(), nullable=True, default=sa.text('\'[]\'::jsonb')),
        
        # Metadata
        sa.Column('created_by', sa.BigInteger(), sa.ForeignKey('users.id'), nullable=False),
        sa.Column('is_deleted', sa.Boolean(), nullable=False, default=False),
        sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
        
        sa.PrimaryKeyConstraint('id')
    )

    # Create indexes for performance
    op.create_index('idx_candidate_email_phone', 'candidates', ['email', 'phone'])
    op.create_index('idx_candidate_created_by', 'candidates', ['created_by'])
    op.create_index('idx_candidate_status', 'candidates', ['status'])
    op.create_index('idx_candidate_permission', 'candidates', ['data_permission'])
    op.create_index('idx_candidate_deleted', 'candidates', ['is_deleted'])
    op.create_index('idx_candidate_name', 'candidates', ['name'])
    
    # Create GIN indexes for JSONB fields
    op.create_index('idx_candidate_skills', 'candidates', ['skills'], postgresql_using='gin')
    op.create_index('idx_candidate_tags', 'candidates', ['tags'], postgresql_using='gin')
    op.create_index('idx_candidate_shared_with', 'candidates', ['shared_with'], postgresql_using='gin')


def downgrade() -> None:
    # Drop indexes
    op.drop_index('idx_candidate_shared_with', table_name='candidates')
    op.drop_index('idx_candidate_tags', table_name='candidates')
    op.drop_index('idx_candidate_skills', table_name='candidates')
    op.drop_index('idx_candidate_name', table_name='candidates')
    op.drop_index('idx_candidate_deleted', table_name='candidates')
    op.drop_index('idx_candidate_permission', table_name='candidates')
    op.drop_index('idx_candidate_status', table_name='candidates')
    op.drop_index('idx_candidate_created_by', table_name='candidates')
    op.drop_index('idx_candidate_email_phone', table_name='candidates')
    
    # Drop table
    op.drop_table('candidates')
    
    # Drop enums
    op.execute('DROP TYPE IF EXISTS datapermission')
    op.execute('DROP TYPE IF EXISTS candidatestatus')