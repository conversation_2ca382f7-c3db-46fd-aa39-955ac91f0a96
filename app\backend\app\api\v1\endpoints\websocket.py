"""
WebSocket API Endpoints for Sprint 4
Handles real-time notifications and batch task progress updates
"""
import logging
import json
from typing import Dict, Any, Optional
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Query, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
import asyncio

from app.core.database import get_db
from app.core.exceptions import service_error
from app.core.id_types import SnowflakeID
from app.services.cache_service import cache_service
from app.models.user import User
from app.core.security import decode_token
from app.schemas.token import TokenPayload
from app.crud import user as user_crud
import time

logger = logging.getLogger(__name__)

router = APIRouter()


class ConnectionManager:
    """WebSocket connection manager"""
    
    def __init__(self):
        # Store active connections by user_id
        self.active_connections: Dict[str, WebSocket] = {}
        # Store user info for each connection
        self.connection_users: Dict[str, Dict[str, Any]] = {}
    
    async def connect(self, websocket: WebSocket, user_id: str, user_info: Dict[str, Any]):
        """Accept and register a WebSocket connection"""
        await websocket.accept()
        
        # Disconnect existing connection for same user
        if user_id in self.active_connections:
            try:
                await self.active_connections[user_id].close()
            except:
                pass
        
        self.active_connections[user_id] = websocket
        self.connection_users[user_id] = user_info
        
        logger.info(f"WebSocket connected for user {user_id}")
        
        # Send welcome message
        await self.send_personal_message({
            "type": "connection_established",
            "message": "WebSocket connected successfully",
            "timestamp": time.time(),
            "user_id": user_id
        }, user_id)
    
    def disconnect(self, user_id: str):
        """Remove a WebSocket connection"""
        if user_id in self.active_connections:
            del self.active_connections[user_id]
        if user_id in self.connection_users:
            del self.connection_users[user_id]
        logger.info(f"WebSocket disconnected for user {user_id}")
    
    async def send_personal_message(self, message: Dict[str, Any], user_id: str):
        """Send message to specific user"""
        if user_id in self.active_connections:
            try:
                await self.active_connections[user_id].send_text(json.dumps(message))
                return True
            except Exception as e:
                logger.error(f"Failed to send message to user {user_id}: {str(e)}")
                self.disconnect(user_id)
                return False
        return False
    
    async def broadcast(self, message: Dict[str, Any]):
        """Broadcast message to all connected users"""
        disconnected = []
        for user_id, websocket in self.active_connections.items():
            try:
                await websocket.send_text(json.dumps(message))
            except Exception as e:
                logger.error(f"Failed to broadcast to user {user_id}: {str(e)}")
                disconnected.append(user_id)
        
        # Remove disconnected users
        for user_id in disconnected:
            self.disconnect(user_id)
    
    def get_connected_users(self) -> Dict[str, Dict[str, Any]]:
        """Get list of connected users"""
        return self.connection_users.copy()


# Global connection manager
manager = ConnectionManager()


async def validate_ws_token(token: str, db: AsyncSession) -> Optional[User]:
    """Validate WebSocket token and return user"""
    try:
        # Remove "Bearer " prefix if present
        if token.startswith("Bearer "):
            token = token[7:]
        
        # Decode token
        payload = decode_token(token)
        token_data = TokenPayload(**payload)
        
        if token_data.type != "access":
            return None
        
        # Get user from database
        user_id = int(token_data.sub) if token_data.sub else None
        if not user_id:
            return None
            
        user = await user_crud.get(db, id=user_id)
        if not user or not user.is_active:
            return None
            
        return user
    except Exception as e:
        logger.error(f"WebSocket token validation failed: {str(e)}")
        return None


@router.websocket("/recruitment/{user_id}")
async def recruitment_websocket(
    websocket: WebSocket,
    user_id: SnowflakeID,
    token: str = Query(..., description="JWT access token")
):
    """
    WebSocket endpoint for recruitment system real-time updates
    
    **Authentication:** JWT token required via query parameter
    
    **Path Parameters:**
    - `user_id`: User ID to connect as
    
    **Query Parameters:**
    - `token`: JWT access token for authentication
    
    **Message Types:**
    - `batch_progress`: Batch task progress updates
    - `assessment_completed`: Assessment completion notifications
    - `matching_completed`: Matching process completion
    - `candidate_added`: New candidate notifications
    - `system_notification`: System-wide announcements
    
    **Usage:**
    ```javascript
    const ws = new WebSocket(`ws://localhost:8088/api/v1/ws/recruitment/${userId}?token=${accessToken}`);
    
    ws.onmessage = (event) => {
        const data = JSON.parse(event.data);
        console.log('Notification:', data);
    };
    ```
    """
    # Get database session
    async with get_db() as db:
        user = await validate_ws_token(token, db)
    
    if not user:
        await websocket.close(code=1008, reason="Invalid token")
        return
    
    if str(user.id) != str(user_id):
        await websocket.close(code=1008, reason="User ID mismatch")
        return
    
    try:
        # Connect user
        await manager.connect(websocket, str(user_id), {
            "user_id": str(user.id),
            "username": user.username,
            "email": user.email,
            "connected_at": time.time()
        })
        
        # Start background task for periodic updates
        asyncio.create_task(send_periodic_updates(str(user_id)))
        
        # Listen for messages
        while True:
            try:
                # Receive message from client
                data = await websocket.receive_text()
                message = json.loads(data)
                
                # Handle different message types
                await handle_websocket_message(message, str(user_id))
                
            except WebSocketDisconnect:
                break
            except json.JSONDecodeError:
                await manager.send_personal_message({
                    "type": "error",
                    "message": "Invalid JSON format",
                    "timestamp": time.time()
                }, str(user_id))
            except Exception as e:
                logger.error(f"WebSocket message handling error: {str(e)}")
                await manager.send_personal_message({
                    "type": "error",
                    "message": "Message processing failed",
                    "timestamp": time.time()
                }, str(user_id))
                
    except Exception as e:
        logger.error(f"WebSocket connection error: {str(e)}")
    finally:
        manager.disconnect(str(user_id))


async def handle_websocket_message(message: Dict[str, Any], user_id: str):
    """Handle incoming WebSocket messages from clients"""
    message_type = message.get("type")
    
    if message_type == "ping":
        # Respond to ping with pong
        await manager.send_personal_message({
            "type": "pong",
            "timestamp": time.time()
        }, user_id)
        
    elif message_type == "subscribe":
        # Subscribe to specific event types
        channels = message.get("channels", [])
        await manager.send_personal_message({
            "type": "subscription_confirmed",
            "channels": channels,
            "timestamp": time.time()
        }, user_id)
        
    elif message_type == "request_status":
        # Request current status
        await send_status_update(user_id)
        
    else:
        logger.warning(f"Unknown WebSocket message type: {message_type}")


async def send_periodic_updates(user_id: str):
    """Send periodic updates to connected user"""
    try:
        while user_id in manager.active_connections:
            # Check for batch task updates
            await check_batch_task_updates(user_id)
            
            # Send heartbeat every 30 seconds
            await manager.send_personal_message({
                "type": "heartbeat",
                "timestamp": time.time(),
                "connected_users": len(manager.active_connections)
            }, user_id)
            
            # Wait 30 seconds
            await asyncio.sleep(30)
            
    except Exception as e:
        logger.error(f"Periodic updates error for user {user_id}: {str(e)}")


async def check_batch_task_updates(user_id: str):
    """Check for batch task progress updates"""
    try:
        # Look for user's batch tasks in cache
        from app.services.batch_service import batch_service
        
        # Get user's recent tasks (simplified check)
        for task_id, task_data in batch_service.tasks.items():
            if task_data.get("created_by") == user_id:
                status = task_data.get("status")
                progress = task_data.get("progress", 0)
                
                # Send progress update
                await manager.send_personal_message({
                    "type": "batch_progress",
                    "task_id": task_id,
                    "status": status,
                    "progress": progress,
                    "timestamp": time.time()
                }, user_id)
                
    except Exception as e:
        logger.error(f"Batch task check error: {str(e)}")


async def send_status_update(user_id: str):
    """Send current status to user"""
    try:
        # Get recruitment stats (simplified)
        status = {
            "type": "status_update",
            "data": {
                "active_tasks": 0,
                "completed_today": 0,
                "system_health": "healthy"
            },
            "timestamp": time.time()
        }
        
        await manager.send_personal_message(status, user_id)
        
    except Exception as e:
        logger.error(f"Status update error: {str(e)}")


# Utility functions for sending notifications
async def notify_batch_progress(task_id: str, user_id: str, progress: float, status: str):
    """Send batch progress notification"""
    message = {
        "type": "batch_progress",
        "task_id": task_id,
        "progress": progress,
        "status": status,
        "timestamp": time.time()
    }
    await manager.send_personal_message(message, user_id)


async def notify_assessment_completed(candidate_id: str, user_id: str, dci_score: float):
    """Send assessment completion notification"""
    message = {
        "type": "assessment_completed",
        "candidate_id": candidate_id,
        "dci_score": dci_score,
        "timestamp": time.time()
    }
    await manager.send_personal_message(message, user_id)


async def notify_matching_completed(candidate_id: str, user_id: str, matches_count: int):
    """Send matching completion notification"""
    message = {
        "type": "matching_completed",
        "candidate_id": candidate_id,
        "matches_found": matches_count,
        "timestamp": time.time()
    }
    await manager.send_personal_message(message, user_id)


async def broadcast_system_notification(message: str, notification_type: str = "info"):
    """Broadcast system notification to all connected users"""
    notification = {
        "type": "system_notification",
        "notification_type": notification_type,
        "message": message,
        "timestamp": time.time()
    }
    await manager.broadcast(notification)


@router.get("/connections/")
async def get_active_connections():
    """
    Get list of active WebSocket connections (admin endpoint)
    
    **Returns:**
    - List of connected users
    - Connection statistics
    """
    try:
        connections = manager.get_connected_users()
        
        return {
            "total_connections": len(connections),
            "connected_users": list(connections.values()),
            "timestamp": time.time()
        }
        
    except Exception as e:
        logger.error(f"Failed to get connections: {str(e)}")
        raise service_error("WEBSOCKET_CONNECTIONS_ERROR", "Failed to retrieve connection info")


@router.post("/broadcast/")
async def broadcast_notification(
    message: str,
    notification_type: str = "info"
):
    """
    Broadcast notification to all connected users (admin endpoint)
    
    **Request Body:**
    - `message`: Notification message
    - `notification_type`: Type of notification (info, warning, error)
    
    **Returns:**
    - Number of users notified
    """
    try:
        await broadcast_system_notification(message, notification_type)
        
        return {
            "success": True,
            "users_notified": len(manager.active_connections),
            "message": message,
            "timestamp": time.time()
        }
        
    except Exception as e:
        logger.error(f"Failed to broadcast notification: {str(e)}")
        raise service_error("WEBSOCKET_BROADCAST_ERROR", "Failed to broadcast notification")