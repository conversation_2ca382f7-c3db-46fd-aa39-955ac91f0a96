"""
Statistics API endpoints for sidebar statistics
Following existing API patterns and error handling
"""
import logging
from typing import Any
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.redis import get_redis
from app.core.exceptions import service_error, not_found
from app.core.permissions import check_permission
from app.core.enums import Permission
from app.models.user import User
from app.schemas.statistics import (
    SidebarStatsResponse, 
    StatsPreferencesUpdate, 
    StatsPreferencesResponse
)
from app.services.statistics_service import StatisticsService
from app.services.user_preference_service import UserPreferenceService
from app.services.cache.statistics_cache import StatisticsCacheService

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/sidebar/", response_model=SidebarStatsResponse, tags=["Statistics"])
async def get_sidebar_stats(
    current_user: User = Depends(check_permission(Permission.CANDIDATES_READ)),
    db: AsyncSession = Depends(get_db),
    redis = Depends(get_redis)
) -> Any:
    """
    Get sidebar statistics data for the authenticated user
    
    **Required Permission**: `candidates:read` (since stats include candidate data)
    
    **Returns**:
    - Dashboard statistics (notifications, new items, etc.)
    - Candidate statistics (counts, pending reviews, etc.)
    - User management statistics (admin only)
    - Metadata with cache information
    
    **Notes**:
    - Data is cached for performance
    - Results are filtered based on user permissions and preferences
    - Admin users get additional user management statistics
    """
    try:
        # Initialize services
        cache_service = StatisticsCacheService(redis)
        stats_service = StatisticsService(db, cache_service)
        
        # Get statistics
        stats = await stats_service.get_sidebar_stats(current_user)
        
        logger.info(f"Sidebar stats delivered to user {current_user.id}")
        return stats
        
    except Exception as e:
        logger.error(f"Failed to get sidebar stats for user {current_user.id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="STATS_FETCH_ERROR"
        )


@router.get("/preferences/", response_model=StatsPreferencesResponse, tags=["Statistics"])
async def get_stats_preferences(
    current_user: User = Depends(check_permission(Permission.USERS_READ)),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """
    Get user's statistics display preferences
    
    **Required Permission**: `users:read` (accessing own preferences)
    
    **Returns**:
    - Statistics display settings (show/hide different sections)
    - Refresh interval configuration
    - Display mode preferences
    """
    try:
        preference_service = UserPreferenceService(db)
        preferences = await preference_service.get_stats_preferences_response(current_user)
        
        logger.debug(f"Statistics preferences delivered to user {current_user.id}")
        return preferences
        
    except Exception as e:
        logger.error(f"Failed to get stats preferences for user {current_user.id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="STATS_PREFERENCES_GET_ERROR"
        )


@router.put("/preferences/", response_model=StatsPreferencesResponse, tags=["Statistics"])
async def update_stats_preferences(
    preferences_update: StatsPreferencesUpdate,
    current_user: User = Depends(check_permission(Permission.USERS_WRITE)),
    db: AsyncSession = Depends(get_db),
    redis = Depends(get_redis)
) -> Any:
    """
    Update user's statistics display preferences
    
    **Required Permission**: `users:update` (updating own preferences)
    
    **Request Body**:
    - `show_dashboard_stats`: Toggle dashboard statistics display
    - `show_candidate_stats`: Toggle candidate statistics display
    - `show_position_stats`: Toggle position statistics display  
    - `show_user_management_stats`: Toggle user management stats (admin only)
    - `show_notification_badges`: Toggle notification badges
    - `stats_refresh_interval`: Refresh interval in seconds (60-3600)
    - `stats_display_mode`: Display mode (auto, compact, detailed)
    
    **Returns**:
    - Updated preferences
    
    **Side Effects**:
    - Invalidates cached statistics for the user
    """
    try:
        # Update preferences
        preference_service = UserPreferenceService(db)
        updated_prefs = await preference_service.update_stats_preferences(
            current_user.id, 
            preferences_update
        )
        
        # Invalidate stats cache since preferences changed
        cache_service = StatisticsCacheService(redis)
        await cache_service.invalidate_stats_cache(current_user.id)
        
        # Return updated preferences
        response = StatsPreferencesResponse(
            show_dashboard_stats=updated_prefs.show_dashboard_stats,
            show_candidate_stats=updated_prefs.show_candidate_stats,
            show_position_stats=updated_prefs.show_position_stats,
            show_user_management_stats=updated_prefs.show_user_management_stats,
            show_notification_badges=updated_prefs.show_notification_badges,
            stats_refresh_interval=updated_prefs.stats_refresh_interval,
            stats_display_mode=updated_prefs.stats_display_mode,
            updated_at=updated_prefs.updated_at
        )
        
        logger.info(f"Updated statistics preferences for user {current_user.id}")
        return response
        
    except service_error as e:
        logger.error(f"Service error updating preferences for user {current_user.id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Failed to update stats preferences for user {current_user.id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="STATS_PREFERENCES_UPDATE_ERROR"
        )


@router.post("/cache/invalidate/", tags=["Statistics"])
async def invalidate_stats_cache(
    current_user: User = Depends(check_permission(Permission.CANDIDATES_READ)),
    redis = Depends(get_redis)
) -> Any:
    """
    Manually invalidate statistics cache for the current user
    
    **Required Permission**: `candidates:read`
    
    **Purpose**:
    - Force refresh of cached statistics
    - Useful after bulk data operations
    - Development and troubleshooting
    
    **Returns**:
    - Success confirmation
    """
    try:
        cache_service = StatisticsCacheService(redis)
        await cache_service.invalidate_stats_cache(current_user.id)
        
        logger.info(f"Statistics cache invalidated for user {current_user.id}")
        return {"message": "Cache invalidated successfully"}
        
    except Exception as e:
        logger.error(f"Failed to invalidate cache for user {current_user.id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="CACHE_INVALIDATE_ERROR"
        )


@router.get("/cache/info/", tags=["Statistics"])
async def get_cache_info(
    current_user: User = Depends(check_permission(Permission.SYSTEM_CONFIG)),
    redis = Depends(get_redis)
) -> Any:
    """
    Get statistics cache information and performance metrics
    
    **Required Permission**: `admin:system` (admin only)
    
    **Returns**:
    - Cache hit/miss statistics
    - Memory usage information
    - Configuration details
    """
    try:
        cache_service = StatisticsCacheService(redis)
        cache_stats = await cache_service.get_cache_stats()
        
        logger.debug(f"Cache info requested by admin user {current_user.id}")
        return cache_stats
        
    except Exception as e:
        logger.error(f"Failed to get cache info: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="CACHE_INFO_ERROR"
        )