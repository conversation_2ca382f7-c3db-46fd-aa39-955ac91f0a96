"""
Enhanced questionnaire API endpoints with scoring and analytics
"""
from typing import Any, List, Optional, Dict
from datetime import datetime, timezone, timedelta
from fastapi import APIRouter, Depends, HTTPException, status, Query, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession
from app.core.database import get_db
from app.api.deps import get_current_user
from app.core.exceptions import not_found, bad_request, forbidden
from app.models.user import User
from app.crud import questionnaire as crud
from app.schemas.questionnaire import (
    QuestionnaireCreate, QuestionnaireUpdate, QuestionnaireResponse,
    QuestionnaireListResponse, QuestionCreate, QuestionUpdate,
    QuestionResponse, SectionCreate, SectionUpdate, SectionResponse,
    ResponseSubmit, ResponseComplete, QuestionnaireAnalytics,
    ScoringResult
)
from app.services.scoring_engine import ScoringEngine
# from app.services.analytics_service import AnalyticsService  # TODO: Implement
import logging

logger = logging.getLogger(__name__)
router = APIRouter()

# Initialize services
scoring_engine = ScoringEngine()
# analytics_service = AnalyticsService()  # TODO: Implement


@router.post("/", response_model=QuestionnaireResponse)
async def create_questionnaire(
    *,
    db: AsyncSession = Depends(get_db),
    questionnaire_in: QuestionnaireCreate,
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    Create new questionnaire with questions and sections
    """
    questionnaire = await crud.questionnaire.create_with_questions(
        db, obj_in=questionnaire_in, created_by=current_user.id
    )
    
    logger.info(f"Questionnaire created: {questionnaire.id} by user {current_user.id}")
    
    return questionnaire


@router.get("/", response_model=QuestionnaireListResponse)
async def list_questionnaires(
    db: AsyncSession = Depends(get_db),
    skip: int = Query(0, ge=0),
    limit: int = Query(10, ge=1, le=100),
    status: Optional[str] = None,
    category: Optional[str] = None,
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    List questionnaires with optional filtering
    """
    # Build query filters
    filters = []
    if status:
        filters.append({"status": status})
    if category:
        filters.append({"category": category})
    
    # Get questionnaires (simplified for now - TODO: implement filtering)
    questionnaires = await crud.questionnaire.get_multi(
        db, skip=skip, limit=limit
    )
    
    # Get total count (simplified for now - TODO: implement filtering)
    total = len(questionnaires) + skip  # Rough estimate
    
    return QuestionnaireListResponse(
        items=questionnaires,
        total=total,
        skip=skip,
        limit=limit
    )


@router.get("/{questionnaire_id}", response_model=QuestionnaireResponse)
async def get_questionnaire(
    *,
    db: AsyncSession = Depends(get_db),
    questionnaire_id: str,
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    Get questionnaire with full details (including scoring for admin)
    """
    questionnaire = await crud.questionnaire.get_with_questions(
        db, id=int(questionnaire_id)
    )
    
    if not questionnaire:
        raise not_found("Questionnaire not found")
    
    # Check permissions
    if questionnaire.created_by != current_user.id and not current_user.is_superuser:
        # Remove scoring information for non-owners
        for question in questionnaire.questions:
            question.scoring_enabled = False
            question.max_score = 0
            question.weight = 1
            
            # Remove scoring from options
            if question.options:
                for option in question.options:
                    if isinstance(option, dict):
                        option.pop('score', None)
                        option.pop('is_correct', None)
    
    return questionnaire


@router.put("/{questionnaire_id}", response_model=QuestionnaireResponse)
async def update_questionnaire(
    *,
    db: AsyncSession = Depends(get_db),
    questionnaire_id: str,
    questionnaire_in: QuestionnaireUpdate,
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    Update questionnaire
    """
    questionnaire = await crud.questionnaire.get(db, id=int(questionnaire_id))
    
    if not questionnaire:
        raise not_found("Questionnaire not found")
    
    # Check permissions
    if questionnaire.created_by != current_user.id and not current_user.is_superuser:
        raise forbidden("You don't have permission to update this questionnaire")
    
    questionnaire = await crud.questionnaire.update(
        db, db_obj=questionnaire, obj_in=questionnaire_in
    )
    
    return await crud.questionnaire.get_with_questions(db, id=questionnaire.id)


@router.delete("/{questionnaire_id}")
async def delete_questionnaire(
    *,
    db: AsyncSession = Depends(get_db),
    questionnaire_id: str,
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    Delete questionnaire
    """
    questionnaire = await crud.questionnaire.get(db, id=int(questionnaire_id))
    
    if not questionnaire:
        raise not_found("Questionnaire not found")
    
    # Check permissions
    if questionnaire.created_by != current_user.id and not current_user.is_superuser:
        raise forbidden("You don't have permission to delete this questionnaire")
    
    await crud.questionnaire.remove(db, id=int(questionnaire_id))
    
    return {"message": "Questionnaire deleted successfully"}


@router.post("/{questionnaire_id}/publish", response_model=QuestionnaireResponse)
async def publish_questionnaire(
    *,
    db: AsyncSession = Depends(get_db),
    questionnaire_id: str,
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    Publish a questionnaire (make it active)
    """
    questionnaire = await crud.questionnaire.publish(
        db, id=int(questionnaire_id), updated_by=current_user.id
    )
    
    logger.info(f"Questionnaire published: {questionnaire_id} by user {current_user.id}")
    
    return questionnaire


@router.post("/{questionnaire_id}/archive", response_model=QuestionnaireResponse)
async def archive_questionnaire(
    *,
    db: AsyncSession = Depends(get_db),
    questionnaire_id: str,
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    Archive a questionnaire
    """
    questionnaire = await crud.questionnaire.archive(
        db, id=int(questionnaire_id), updated_by=current_user.id
    )
    
    return questionnaire


@router.post("/{questionnaire_id}/duplicate", response_model=QuestionnaireResponse)
async def duplicate_questionnaire(
    *,
    db: AsyncSession = Depends(get_db),
    questionnaire_id: str,
    new_title: Optional[str] = None,
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    Duplicate a questionnaire
    """
    questionnaire = await crud.questionnaire.duplicate(
        db, id=int(questionnaire_id), created_by=current_user.id, new_title=new_title
    )
    
    logger.info(f"Questionnaire duplicated: {questionnaire_id} -> {questionnaire.id}")
    
    return questionnaire


@router.put("/{questionnaire_id}/questions", response_model=QuestionnaireResponse)
async def update_questions(
    *,
    db: AsyncSession = Depends(get_db),
    questionnaire_id: str,
    questions: List[QuestionCreate],
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    Update all questions for a questionnaire
    """
    questionnaire = await crud.questionnaire.get(db, id=int(questionnaire_id))
    
    if not questionnaire:
        raise not_found("Questionnaire not found")
    
    # Check permissions
    if questionnaire.created_by != current_user.id and not current_user.is_superuser:
        raise forbidden("You don't have permission to update this questionnaire")
    
    questionnaire = await crud.questionnaire.update_questions(
        db, questionnaire_id=int(questionnaire_id), questions=questions, updated_by=current_user.id
    )
    
    return questionnaire


@router.post("/{questionnaire_id}/questions/reorder")
async def reorder_questions(
    *,
    db: AsyncSession = Depends(get_db),
    questionnaire_id: str,
    question_orders: Dict[str, int],  # {question_id: new_order}
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    Reorder questions within a questionnaire
    """
    # Convert string IDs to integers
    orders = {int(k): v for k, v in question_orders.items()}
    
    questions = await crud.question.reorder(
        db, questionnaire_id=int(questionnaire_id), question_orders=orders
    )
    
    return {"message": "Questions reordered successfully", "count": len(questions)}


# Public endpoints (no authentication required)
@router.get("/public/{questionnaire_id}", response_model=QuestionnaireResponse)
async def get_public_questionnaire(
    *,
    db: AsyncSession = Depends(get_db),
    questionnaire_id: str
) -> Any:
    """
    Get public view of questionnaire (no scoring information)
    """
    questionnaire = await crud.questionnaire.get_public(
        db, id=int(questionnaire_id)
    )
    
    if not questionnaire:
        raise not_found("Questionnaire not found")
    
    if questionnaire.status != "active":
        raise bad_request("Questionnaire is not active")
    
    # Check schedule
    now = datetime.now(timezone.utc)
    if questionnaire.scheduled_start and now < questionnaire.scheduled_start:
        raise bad_request("Questionnaire has not started yet")
    if questionnaire.scheduled_end and now > questionnaire.scheduled_end:
        raise bad_request("Questionnaire has ended")
    
    return questionnaire


@router.post("/public/{questionnaire_id}/submit", response_model=ResponseComplete)
async def submit_response(
    *,
    db: AsyncSession = Depends(get_db),
    background_tasks: BackgroundTasks,
    questionnaire_id: str,
    response_in: ResponseSubmit,
    candidate_id: Optional[str] = None,
    ip_address: Optional[str] = None,
    user_agent: Optional[str] = None
) -> Any:
    """
    Submit response to a questionnaire (public endpoint)
    """
    # Check if questionnaire exists and is active
    questionnaire = await crud.questionnaire.get(db, id=int(questionnaire_id))
    
    if not questionnaire:
        raise not_found("Questionnaire not found")
    
    if questionnaire.status != "active":
        raise bad_request("Questionnaire is not accepting responses")
    
    # Check response limit
    if questionnaire.max_responses and questionnaire.total_responses >= questionnaire.max_responses:
        raise bad_request("Questionnaire has reached maximum responses")
    
    # Submit response
    response = await crud.questionnaire_response.submit_response(
        db,
        questionnaire_id=int(questionnaire_id),
        response_data=response_in,
        candidate_id=int(candidate_id) if candidate_id else None,
        ip_address=ip_address,
        user_agent=user_agent
    )
    
    # Send notifications in background
    if questionnaire.notify_on_submission:
        background_tasks.add_task(
            send_submission_notification,
            questionnaire,
            response
        )
    
    logger.info(f"Response submitted for questionnaire {questionnaire_id}: {response.id}")
    
    # Prepare response
    result = ResponseComplete(
        id=str(response.id),
        questionnaire_id=str(response.questionnaire_id),
        candidate_id=str(response.candidate_id) if response.candidate_id else None,
        respondent_email=response.respondent_email,
        respondent_name=response.respondent_name,
        started_at=response.started_at,
        completed_at=response.completed_at,
        time_spent_seconds=response.time_spent_seconds,
        status=response.status,
        progress=response.progress,
        answers=response.answers
    )
    
    # Add scoring if enabled and allowed
    if questionnaire.scoring_enabled and questionnaire.show_score_on_completion:
        result.scoring = ScoringResult(
            total_score=response.total_score or 0,
            max_possible_score=response.max_possible_score or 0,
            percentage=response.percentage_score or 0,
            passed=response.passed,
            breakdown=response.score_breakdown or []
        )
    
    return result


@router.get("/{questionnaire_id}/responses", response_model=List[ResponseComplete])
async def get_responses(
    *,
    db: AsyncSession = Depends(get_db),
    questionnaire_id: str,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=500),
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    Get responses for a questionnaire
    """
    questionnaire = await crud.questionnaire.get(db, id=int(questionnaire_id))
    
    if not questionnaire:
        raise not_found("Questionnaire not found")
    
    # Check permissions
    if questionnaire.created_by != current_user.id and not current_user.is_superuser:
        raise forbidden("You don't have permission to view responses")
    
    responses = await crud.questionnaire_response.get_responses(
        db, questionnaire_id=int(questionnaire_id), skip=skip, limit=limit
    )
    
    return responses


@router.get("/{questionnaire_id}/analytics", response_model=QuestionnaireAnalytics)
async def get_analytics(
    *,
    db: AsyncSession = Depends(get_db),
    questionnaire_id: str,
    date_from: Optional[datetime] = None,
    date_to: Optional[datetime] = None,
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    Get analytics for a questionnaire
    """
    questionnaire = await crud.questionnaire.get(db, id=int(questionnaire_id))
    
    if not questionnaire:
        raise not_found("Questionnaire not found")
    
    # Check permissions
    if questionnaire.created_by != current_user.id and not current_user.is_superuser:
        raise forbidden("You don't have permission to view analytics")
    
    # TODO: Implement analytics service
    # Generate analytics
    # analytics = await analytics_service.generate_questionnaire_analytics(
    #     db,
    #     questionnaire_id=int(questionnaire_id),
    #     date_from=date_from or datetime.now(timezone.utc) - timedelta(days=30),
    #     date_to=date_to or datetime.now(timezone.utc)
    # )
    
    # return analytics
    return {"message": "Analytics service not implemented yet"}


@router.post("/{questionnaire_id}/export-responses")
async def export_responses(
    *,
    db: AsyncSession = Depends(get_db),
    questionnaire_id: str,
    format: str = Query("csv", regex="^(csv|excel|json)$"),
    current_user: User = Depends(get_current_user)
) -> Any:
    """
    Export questionnaire responses
    """
    questionnaire = await crud.questionnaire.get(db, id=int(questionnaire_id))
    
    if not questionnaire:
        raise not_found("Questionnaire not found")
    
    # Check permissions
    if questionnaire.created_by != current_user.id and not current_user.is_superuser:
        raise forbidden("You don't have permission to export responses")
    
    # TODO: Implement export service
    # Generate export
    # export_data = await analytics_service.export_responses(
    #     db,
    #     questionnaire_id=int(questionnaire_id),
    #     format=format
    # )
    
    # return export_data
    return {"message": f"Export service not implemented yet (requested format: {format})"}


async def send_submission_notification(questionnaire, response):
    """Send notification emails for new submission"""
    # Implementation for sending emails
    pass