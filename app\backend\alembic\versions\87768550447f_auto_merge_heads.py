"""auto_merge_heads

Revision ID: 87768550447f
Revises: ab4d42e07b55, c45e8b9f1a2d
Create Date: 2025-08-07 12:31:26.740403

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '87768550447f'
down_revision: Union[str, None] = ('ab4d42e07b55', 'c45e8b9f1a2d')
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass