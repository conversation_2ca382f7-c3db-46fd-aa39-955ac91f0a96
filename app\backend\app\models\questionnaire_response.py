"""
Questionnaire Response model for storing candidate submissions
"""

from sqlalchemy import Column, <PERSON>, JSO<PERSON>, Integer, DateTime, ForeignKey, <PERSON><PERSON>an, Text
from sqlalchemy.orm import relationship
from datetime import datetime, timezone

from app.core.database import Base
from app.core.id_generator import generate_id


class QuestionnaireResponse(Base):
    __tablename__ = "questionnaire_responses"
    
    id = Column(String, primary_key=True, default=generate_id)
    questionnaire_id = Column(String, ForeignKey("questionnaires.id"), nullable=False)
    session_id = Column(String, index=True)  # For tracking incomplete sessions
    
    # Candidate information
    candidate_id = Column(String, ForeignKey("candidates.id"), nullable=True)  # If logged in
    candidate_name = Column(String(100))
    candidate_email = Column(String(255), index=True)
    candidate_phone = Column(String(20))
    candidate_position = Column(String(100))
    
    # Response data
    responses = Column(JSON)  # {question_id: answer_value}
    response_times = Column(JSON)  # {question_id: seconds_spent}
    
    # Submission metadata
    submitted_at = Column(DateTime(timezone=True))
    completion_time = Column(Integer)  # Total seconds to complete
    current_page = Column(Integer, default=0)  # For multi-page questionnaires
    is_complete = Column(Boolean, default=False)
    
    # Progress tracking
    last_saved_at = Column(DateTime(timezone=True))
    save_count = Column(Integer, default=0)
    
    # Scoring and evaluation
    total_score = Column(Integer)
    dimension_scores = Column(JSON)  # {dimension: score}
    ai_evaluation_id = Column(String, ForeignKey("evaluation_reports.id"))
    is_qualified = Column(Boolean)
    
    # Technical metadata
    ip_address = Column(String(45))
    user_agent = Column(Text)
    browser_info = Column(JSON)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))
    
    # Relationships
    questionnaire = relationship("Questionnaire", back_populates="responses")
    candidate = relationship("Candidate", back_populates="questionnaire_responses", foreign_keys=[candidate_id])
    ai_evaluation = relationship("EvaluationReport", back_populates="questionnaire_response", foreign_keys=[ai_evaluation_id])
    
    def __repr__(self):
        return f"<QuestionnaireResponse {self.id} - {self.candidate_email}>"