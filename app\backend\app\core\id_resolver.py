"""
ID解析器 - 支持Snowflake ID和UUID格式
解决前后端ID格式不一致问题
"""
from typing import Union, Optional
from uuid import UUID
import re
import logging

logger = logging.getLogger(__name__)


class IDResolver:
    """
    统一的ID解析器，支持多种ID格式
    
    支持格式：
    - Snowflake ID: "609922849930612736" -> 609922849930612736
    - UUID: "550e8400-e29b-41d4-a716-************"
    - Integer: 12345
    """
    
    # Snowflake ID范围（根据实际使用调整）
    SNOWFLAKE_MIN = 1000000000000000
    SNOWFLAKE_MAX = 9999999999999999999
    
    @classmethod
    def resolve(cls, id_value: Union[str, int, UUID, None]) -> Optional[Union[int, str]]:
        """
        解析不同格式的ID
        
        Args:
            id_value: ID值，可以是字符串、整数或UUID对象
            
        Returns:
            解析后的ID（整数或字符串形式的UUID），无法解析返回None
        """
        if id_value is None:
            return None
            
        # 处理整数类型
        if isinstance(id_value, int):
            return id_value
        
        # 处理UUID对象
        if isinstance(id_value, UUID):
            return str(id_value)
        
        # 处理字符串类型
        if isinstance(id_value, str):
            id_value = id_value.strip()
            
            if not id_value:
                return None
            
            # 尝试解析为Snowflake ID（纯数字字符串）
            if re.match(r'^\d+$', id_value):
                try:
                    numeric_id = int(id_value)
                    # 验证是否在Snowflake ID范围内
                    if cls.is_snowflake_id(numeric_id):
                        return numeric_id
                    # 即使不在Snowflake范围内，仍然返回数字ID
                    return numeric_id
                except (ValueError, OverflowError):
                    logger.warning(f"无法将字符串转换为整数: {id_value}")
            
            # 尝试解析为UUID
            try:
                uuid_obj = UUID(id_value)
                return str(uuid_obj)
            except (ValueError, AttributeError):
                logger.warning(f"无法解析为UUID: {id_value}")
        
        logger.error(f"无法解析ID: {id_value} (类型: {type(id_value)})")
        return None
    
    @classmethod
    def is_snowflake_id(cls, id_value: Union[str, int]) -> bool:
        """
        判断是否为Snowflake ID
        
        Args:
            id_value: ID值
            
        Returns:
            是否为Snowflake ID
        """
        try:
            if isinstance(id_value, str):
                if not re.match(r'^\d+$', id_value):
                    return False
                id_value = int(id_value)
            
            if isinstance(id_value, int):
                return cls.SNOWFLAKE_MIN <= id_value <= cls.SNOWFLAKE_MAX
                
        except (ValueError, OverflowError):
            pass
        
        return False
    
    @classmethod
    def is_uuid(cls, id_value: Union[str, UUID]) -> bool:
        """
        判断是否为UUID格式
        
        Args:
            id_value: ID值
            
        Returns:
            是否为UUID
        """
        if isinstance(id_value, UUID):
            return True
            
        if isinstance(id_value, str):
            try:
                UUID(id_value)
                return True
            except (ValueError, AttributeError):
                pass
        
        return False
    
    @classmethod
    def to_string(cls, id_value: Union[str, int, UUID, None]) -> Optional[str]:
        """
        将ID转换为字符串格式
        
        Args:
            id_value: ID值
            
        Returns:
            字符串格式的ID
        """
        resolved = cls.resolve(id_value)
        if resolved is not None:
            return str(resolved)
        return None
    
    @classmethod
    def validate_candidate_id(cls, id_value: Union[str, int, None]) -> Optional[int]:
        """
        验证并解析候选人ID（候选人ID使用Snowflake ID）
        
        Args:
            id_value: 候选人ID
            
        Returns:
            解析后的候选人ID（整数），无效返回None
        """
        if id_value is None:
            return None
            
        resolved = cls.resolve(id_value)
        
        # 候选人ID应该是整数（Snowflake ID）
        if isinstance(resolved, int):
            return resolved
        
        # 如果是字符串形式的数字，尝试转换
        if isinstance(resolved, str) and re.match(r'^\d+$', resolved):
            try:
                return int(resolved)
            except (ValueError, OverflowError):
                pass
        
        logger.error(f"无效的候选人ID格式: {id_value}")
        return None
    
    @classmethod
    def validate_user_id(cls, id_value: Union[str, int, None]) -> Optional[int]:
        """
        验证并解析用户ID（用户ID使用Snowflake ID）
        
        Args:
            id_value: 用户ID
            
        Returns:
            解析后的用户ID（整数），无效返回None
        """
        # 用户ID的验证逻辑与候选人ID相同
        return cls.validate_candidate_id(id_value)


# 便捷函数
def resolve_id(id_value: Union[str, int, UUID, None]) -> Optional[Union[int, str]]:
    """解析ID的便捷函数"""
    return IDResolver.resolve(id_value)


def validate_candidate_id(id_value: Union[str, int, None]) -> Optional[int]:
    """验证候选人ID的便捷函数"""
    return IDResolver.validate_candidate_id(id_value)


def validate_user_id(id_value: Union[str, int, None]) -> Optional[int]:
    """验证用户ID的便捷函数"""
    return IDResolver.validate_user_id(id_value)