"""auto_merge_heads

Revision ID: 84975b4cf260
Revises: 05e30b49ff35
Create Date: 2025-08-15 07:52:56.660522

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '84975b4cf260'
down_revision: Union[str, None] = '05e30b49ff35'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass