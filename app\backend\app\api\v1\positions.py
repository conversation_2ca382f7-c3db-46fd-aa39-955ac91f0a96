"""
Position management API endpoints
"""
from typing import Any, List, Optional, Dict
from fastapi import APIRouter, Depends, HTTPException, Query, status, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession
from app.api import deps
from app.api.deps import get_current_active_user
from app.core.database import get_db
from app.core.exceptions import not_found, conflict, forbidden, bad_request, service_error
from app.core.permissions import check_permission, check_any_permission
from app.core.enums import Permission, DataPermission
from app.crud.position import position as position_crud
from app.models.user import User
from app.models.position import Position, PositionStatus, PositionUrgency
from app.schemas.position import (
    PositionCreate,
    PositionResponse,
    PositionUpdate,
    PositionListResponse,
    PositionSearch,
    PositionMatchRequest,
    PositionMatchResponse
)
from app.services.matching_service import matching_service
from datetime import datetime, timezone, timedelta
import logging

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/", response_model=PositionResponse)
async def create_position(
    *,
    db: AsyncSession = Depends(get_db),
    position_in: PositionCreate,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(check_permission(Permission.POSITIONS_WRITE))
) -> Any:
    """
    Create a new position
    """
    # Check for duplicates
    existing = await position_crud.check_duplicate(
        db,
        title=position_in.title,
        department=position_in.department,
        location=position_in.location
    )
    
    if existing:
        if existing.created_by == current_user.id:
            raise conflict("POSITION_DUPLICATE_SAME_USER")
        # Different users can create similar positions (different perspectives)
    
    # Create position
    position = await position_crud.create_with_permission(
        db,
        obj_in=position_in,
        created_by=current_user.id
    )
    
    # Schedule embedding generation in background
    # Note: Background task will generate embeddings asynchronously
    background_tasks.add_task(
        generate_position_embeddings_background,
        position_id=position.id
    )
    
    logger.info(f"Created position {position.id} by user {current_user.id}")
    return position


@router.get("/", response_model=PositionListResponse)
async def list_positions(
    db: AsyncSession = Depends(get_db),
    skip: int = Query(0, ge=0, description="Skip records"),
    limit: int = Query(20, ge=1, le=100, description="Limit records"),
    search: Optional[str] = Query(None, description="Search keyword"),
    department: Optional[str] = Query(None, description="Department filter"),
    location: Optional[str] = Query(None, description="Location filter"),
    status: Optional[PositionStatus] = Query(None, description="Position status"),
    urgency: Optional[PositionUrgency] = Query(None, description="Urgency level"),
    job_type: Optional[str] = Query(None, description="Job type"),
    job_level: Optional[str] = Query(None, description="Job level"),
    min_salary: Optional[int] = Query(None, ge=0, description="Minimum salary"),
    max_salary: Optional[int] = Query(None, ge=0, description="Maximum salary"),
    required_skills: Optional[List[str]] = Query(None, description="Required skills"),
    data_permission: Optional[DataPermission] = Query(None, description="Data permission"),
    order_by: Optional[str] = Query("created_at", pattern="^(title|created_at|updated_at|deadline|salary_min|salary_max)$", description="Sort field"),
    order_desc: bool = Query(True, description="Sort descending"),
    current_user: User = Depends(check_permission(Permission.POSITIONS_READ))
) -> Any:
    """
    Get positions list with filtering and pagination
    """
    # Get positions with permission filtering
    positions = await position_crud.get_multi_with_permission(
        db,
        user_id=current_user.id,
        user_role=current_user.role,
        skip=skip,
        limit=limit,
        search=search,
        department=department,
        location=location,
        status=status,
        urgency=urgency,
        job_type=job_type,
        job_level=job_level,
        min_salary=min_salary,
        max_salary=max_salary,
        required_skills=required_skills,
        data_permission=data_permission,
        order_by=order_by,
        order_desc=order_desc
    )
    
    # Get total count
    total = await position_crud.count_with_permission(
        db,
        user_id=current_user.id,
        user_role=current_user.role,
        search=search,
        department=department,
        location=location,
        status=status,
        urgency=urgency,
        job_type=job_type,
        job_level=job_level,
        min_salary=min_salary,
        max_salary=max_salary,
        required_skills=required_skills,
        data_permission=data_permission
    )
    
    return PositionListResponse(
        items=positions,
        total=total,
        skip=skip,
        limit=limit
    )


@router.get("/stats")
async def get_positions_stats(
    *,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.POSITIONS_READ)),
    force_refresh: bool = Query(False, description="Force cache refresh")
) -> Dict[str, Any]:
    """
    Get position statistics with Redis caching for performance
    
    Returns comprehensive statistics including:
    - Total positions count
    - Active positions (Open status)
    - New this week/month  
    - Status distribution
    - Department distribution (top 10)
    - Urgency distribution
    - Recent activity trend
    
    **Caching**: Results cached for 10 minutes per user for optimal performance
    """
    import hashlib
    import json
    
    start_time = datetime.now(timezone.utc)
    
    # Generate cache key based on user permissions and role
    cache_key = f"position_stats:user_{current_user.id}:role_{current_user.role}"
    cache_ttl = 600  # 10 minutes
    
    try:
        from app.core.redis import get_redis
        
        # Check cache first (unless force_refresh)
        if not force_refresh:
            try:
                redis_client = await get_redis()
                cached_stats = await redis_client.get(cache_key)
                if cached_stats:
                    cached_data = json.loads(cached_stats)
                    cached_data["cache_info"]["cached"] = True
                    logger.info(
                        "position_stats_cache_hit", 
                        extra={"user_id": current_user.id, "cache_key": cache_key}
                    )
                    return cached_data
            except Exception as cache_error:
                logger.warning(f"Cache read failed, proceeding with fresh data: {cache_error}")

        # If no cache, generate fresh stats
        from sqlalchemy import func, select, text
        from app.models.position import Position, PositionStatus, PositionUrgency
        
        # Calculate time periods
        now = datetime.now(timezone.utc)
        week_ago = now - timedelta(days=7)
        month_ago = now - timedelta(days=30)
        
        # Use direct database queries for better performance
        try:
            # Get database connection directly for complex queries
            from app.core.database import engine
            async with engine.begin() as conn:
                # Get total positions accessible to user
                total_positions = 0
                new_week = 0
                new_month = 0
                open_positions = 0
                status_dist = {}
                dept_dist = {}
                urgency_dist = {}
                
                # Build permission-based WHERE clause - Fixed PostgreSQL array syntax
                permission_clause = ""
                if current_user.role != "admin":
                    permission_clause = f"""
                    AND (
                        created_by = {current_user.id}
                        OR data_permission = 'PUBLIC'
                        OR data_permission = 'TEAM'
                        OR (data_permission = 'SHARED' AND shared_with @> '[{current_user.id}]'::jsonb)
                    )
                    """
                
                # Total positions
                result = await conn.execute(text(f"""
                    SELECT COUNT(*) as total
                    FROM positions 
                    WHERE is_deleted = false {permission_clause}
                """))
                row = result.fetchone()
                total_positions = row.total if row else 0
                
                # New positions this week
                result = await conn.execute(text(f"""
                    SELECT COUNT(*) as count
                    FROM positions 
                    WHERE is_deleted = false 
                    AND created_at >= :week_ago {permission_clause}
                """), {"week_ago": week_ago})
                row = result.fetchone()
                new_week = row.count if row else 0
                
                # New positions this month
                result = await conn.execute(text(f"""
                    SELECT COUNT(*) as count
                    FROM positions 
                    WHERE is_deleted = false 
                    AND created_at >= :month_ago {permission_clause}
                """), {"month_ago": month_ago})
                row = result.fetchone()
                new_month = row.count if row else 0
                
                # Status distribution
                result = await conn.execute(text(f"""
                    SELECT status, COUNT(*) as count
                    FROM positions 
                    WHERE is_deleted = false {permission_clause}
                    GROUP BY status
                    ORDER BY count DESC
                """))
                status_rows = result.fetchall()
                status_total = sum(row.count for row in status_rows)
                
                for row in status_rows:
                    status_dist[row.status] = {
                        "count": row.count,
                        "percentage": round(row.count/status_total*100, 1) if status_total else 0
                    }
                    if row.status == 'open':
                        open_positions = row.count
                
                # Department distribution (top 10)
                result = await conn.execute(text(f"""
                    SELECT department, COUNT(*) as count
                    FROM positions 
                    WHERE is_deleted = false 
                    AND department IS NOT NULL {permission_clause}
                    GROUP BY department 
                    ORDER BY count DESC 
                    LIMIT 10
                """))
                dept_rows = result.fetchall()
                dept_total = sum(row.count for row in dept_rows)
                
                for row in dept_rows:
                    dept_dist[row.department] = {
                        "count": row.count,
                        "percentage": round(row.count/dept_total*100, 1) if dept_total else 0
                    }
                
                # Urgency distribution
                result = await conn.execute(text(f"""
                    SELECT urgency, COUNT(*) as count
                    FROM positions 
                    WHERE is_deleted = false {permission_clause}
                    GROUP BY urgency
                    ORDER BY count DESC
                """))
                urgency_rows = result.fetchall()
                urgency_total = sum(row.count for row in urgency_rows)
                
                for row in urgency_rows:
                    urgency_dist[row.urgency] = {
                        "count": row.count,
                        "percentage": round(row.count/urgency_total*100, 1) if urgency_total else 0
                    }
                
                # Activity trend (last 30 days)
                result = await conn.execute(text(f"""
                    SELECT 
                        DATE(created_at) as date,
                        COUNT(*) as new_positions
                    FROM positions 
                    WHERE is_deleted = false 
                    AND created_at >= :month_ago {permission_clause}
                    GROUP BY DATE(created_at)
                    ORDER BY date
                """), {"month_ago": month_ago})
                trend_rows = result.fetchall()
                trend = [
                    {"date": str(row.date), "new_positions": row.new_positions}
                    for row in trend_rows
                ]
                
        except Exception as e:
            logger.error(f"Direct database stats failed: {e}")
            # Fallback to ORM-based queries
            return await _get_basic_position_stats(db, current_user, start_time, cache_ttl)
        
        # Build comprehensive response
        response_data = {
            "overview": {
                "total_positions": total_positions,
                "open_positions": open_positions,
                "new_this_week": new_week,
                "new_this_month": new_month,
            },
            "status_distribution": status_dist,
            "department_distribution": dept_dist,
            "urgency_distribution": urgency_dist,
            "activity_trend": trend,
            "last_updated": now.isoformat(),
            "cache_info": {
                "cached": False,
                "generation_time_ms": int((datetime.now(timezone.utc) - start_time).total_seconds() * 1000),
                "ttl_seconds": cache_ttl
            }
        }
        
        # Cache the results
        try:
            redis_client = await get_redis()
            await redis_client.setex(
                cache_key,
                cache_ttl,
                json.dumps(response_data, default=str, ensure_ascii=False)
            )
            logger.info(
                "position_stats_cached",
                extra={
                    "user_id": current_user.id,
                    "cache_key": cache_key,
                    "generation_time_ms": response_data["cache_info"]["generation_time_ms"]
                }
            )
        except Exception as cache_error:
            logger.warning(f"Failed to cache statistics: {cache_error}")
        
        return response_data
        
    except Exception as e:
        import traceback
        error_traceback = traceback.format_exc()
        logger.error(
            f"position_stats_error: {str(e)}",
            extra={
                "user_id": current_user.id,
                "error": str(e),
                "error_type": type(e).__name__,
                "traceback": error_traceback
            }
        )
        
        # Return empty stats with error indicator
        return {
            "overview": {
                "total_positions": 0,
                "open_positions": 0,
                "new_this_week": 0,
                "new_this_month": 0,
            },
            "status_distribution": {},
            "department_distribution": {},
            "urgency_distribution": {},
            "activity_trend": [],
            "last_updated": datetime.now(timezone.utc).isoformat(),
            "cache_info": {
                "cached": False,
                "generation_time_ms": 0,
                "ttl_seconds": cache_ttl,
                "error": True
            }
        }


async def _get_basic_position_stats(db: AsyncSession, current_user: User, start_time: datetime, cache_ttl: int) -> Dict[str, Any]:
    """
    Fallback function for basic position statistics using existing CRUD methods
    """
    try:
        # Get total count with permission check
        total_count = await position_crud.count_with_permission(
            db,
            user_id=current_user.id,
            user_role=current_user.role
        )
        
        # Get counts by status using the list method and filtering
        status_counts = {}
        open_positions = 0
        
        for status in PositionStatus:
            count = await position_crud.count_with_permission(
                db,
                user_id=current_user.id,
                user_role=current_user.role,
                status=status
            )
            if count > 0:
                status_counts[status.value] = {"count": count, "percentage": 0}
                if status == PositionStatus.OPEN:
                    open_positions = count
        
        # Get counts by urgency
        urgency_counts = {}
        for urgency in PositionUrgency:
            count = await position_crud.count_with_permission(
                db,
                user_id=current_user.id,
                user_role=current_user.role,
                urgency=urgency
            )
            if count > 0:
                urgency_counts[urgency.value] = {"count": count, "percentage": 0}
        
        # Calculate percentages for status distribution
        if status_counts:
            status_total = sum(item["count"] for item in status_counts.values())
            for key in status_counts:
                status_counts[key]["percentage"] = round(status_counts[key]["count"]/status_total*100, 1)
        
        # Calculate percentages for urgency distribution
        if urgency_counts:
            urgency_total = sum(item["count"] for item in urgency_counts.values())
            for key in urgency_counts:
                urgency_counts[key]["percentage"] = round(urgency_counts[key]["count"]/urgency_total*100, 1)
        
        return {
            "overview": {
                "total_positions": total_count,
                "open_positions": open_positions,
                "new_this_week": 0,  # Simplified in fallback
                "new_this_month": 0,  # Simplified in fallback
            },
            "status_distribution": status_counts,
            "department_distribution": {},  # Simplified in fallback
            "urgency_distribution": urgency_counts,
            "activity_trend": [],
            "last_updated": datetime.now(timezone.utc).isoformat(),
            "cache_info": {
                "cached": False,
                "generation_time_ms": int((datetime.now(timezone.utc) - start_time).total_seconds() * 1000),
                "ttl_seconds": cache_ttl,
                "fallback": True
            }
        }
        
    except Exception as e:
        logger.error(f"Failed to get basic position statistics: {str(e)}")
        return {
            "overview": {
                "total_positions": 0,
                "open_positions": 0,
                "new_this_week": 0,
                "new_this_month": 0,
            },
            "status_distribution": {},
            "department_distribution": {},
            "urgency_distribution": {},
            "activity_trend": [],
            "last_updated": datetime.now(timezone.utc).isoformat(),
            "cache_info": {
                "cached": False,
                "generation_time_ms": 0,
                "ttl_seconds": cache_ttl,
                "error": True
            }
        }


@router.get("/{position_id}", response_model=PositionResponse)
async def get_position(
    position_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.POSITIONS_READ))
) -> Any:
    """
    Get position by ID (with permission check)
    """
    position = await position_crud.get_with_permission(
        db,
        id=position_id,
        user_id=current_user.id,
        user_role=current_user.role
    )
    
    if not position:
        raise not_found("POSITION_NOT_FOUND_OR_NO_ACCESS")
    
    return position


@router.put("/{position_id}", response_model=PositionResponse)
async def update_position(
    position_id: int,
    position_in: PositionUpdate,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.POSITIONS_WRITE))
) -> Any:
    """
    Update position information
    """
    # Get position with permission check
    position = await position_crud.get_with_permission(
        db,
        id=position_id,
        user_id=current_user.id,
        user_role=current_user.role
    )
    
    if not position:
        raise not_found("POSITION_NOT_FOUND_OR_NO_ACCESS")
    
    try:
        updated_position = await position_crud.update_with_permission(
            db,
            db_obj=position,
            obj_in=position_in,
            user_id=current_user.id
        )
        
        # Check if content changed and regenerate embeddings if needed
        content_fields = ['title', 'requirements', 'responsibilities', 'required_skills', 'preferred_skills']
        if any(getattr(position_in, field, None) is not None for field in content_fields):
            # Schedule background task to regenerate embeddings
            background_tasks.add_task(
                generate_position_embeddings_background,
                position_id=position_id,
                force_regenerate=True
            )
        
        logger.info(f"Updated position {position_id} by user {current_user.id}")
        return updated_position
        
    except PermissionError as e:
        raise forbidden(str(e))


@router.delete("/{position_id}")
async def delete_position(
    position_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.POSITIONS_DELETE))
) -> Any:
    """
    Delete position (soft delete)
    """
    try:
        success = await position_crud.soft_delete_with_permission(
            db,
            id=position_id,
            user_id=current_user.id
        )
        
        if not success:
            raise not_found("POSITION_NOT_FOUND")
        
        # TODO: Also soft-delete associated vectors
        # This could be done in background or as cascade
        
        logger.info(f"Deleted position {position_id} by user {current_user.id}")
        return {"success": True, "message": "POSITION_DELETED_SUCCESS"}
        
    except PermissionError as e:
        raise forbidden(str(e))


@router.post("/{position_id}/share")
async def share_position(
    position_id: int,
    share_with_user_ids: List[int],
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.POSITIONS_WRITE))
) -> Any:
    """
    Share position with specific users
    """
    try:
        position = await position_crud.share_position(
            db,
            position_id=position_id,
            user_id=current_user.id,
            share_with_user_ids=share_with_user_ids
        )
        
        logger.info(f"Shared position {position_id} with users {share_with_user_ids}")
        return {
            "success": True,
            "message": "POSITION_SHARED_SUCCESS",
            "shared_with": position.shared_with
        }
        
    except ValueError as e:
        raise not_found(str(e))
    except PermissionError as e:
        raise forbidden(str(e))


@router.post("/{position_id}/generate-embeddings")
async def generate_position_embeddings(
    position_id: int,
    force_regenerate: bool = Query(False, description="Force regeneration of existing embeddings"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.POSITIONS_WRITE))
) -> Any:
    """
    Generate embeddings for position (for AI matching)
    """
    # Check if user has access to the position
    position = await position_crud.get_with_permission(
        db,
        id=position_id,
        user_id=current_user.id,
        user_role=current_user.role
    )
    
    if not position:
        raise not_found("POSITION_NOT_FOUND_OR_NO_ACCESS")
    
    try:
        result = await matching_service.generate_position_embeddings(
            db, position_id, force_regenerate
        )
        
        logger.info(f"Position embeddings generated for {position_id}: {result['status']}")
        return result
        
    except Exception as e:
        logger.error(f"Embedding generation failed for position {position_id}: {str(e)}")
        if "not_found" in str(e).lower():
            raise not_found(str(e))
        else:
            raise service_error(str(e))


@router.post("/{position_id}/match-candidates", response_model=PositionMatchResponse)
async def match_candidates_for_position(
    position_id: int,
    match_request: PositionMatchRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.POSITIONS_READ))
) -> Any:
    """
    Find matching candidates for a position using AI
    """
    # Validate position_id consistency
    if str(position_id) != match_request.position_id:
        raise bad_request("POSITION_ID_MISMATCH")
    
    # Check if user has access to the position
    position = await position_crud.get_with_permission(
        db,
        id=position_id,
        user_id=current_user.id,
        user_role=current_user.role
    )
    
    if not position:
        raise not_found("POSITION_NOT_FOUND_OR_NO_ACCESS")
    
    try:
        # Convert candidate_ids from strings to integers if provided
        candidate_ids = None
        if match_request.candidate_ids:
            try:
                candidate_ids = [int(cid) for cid in match_request.candidate_ids]
            except ValueError:
                raise bad_request("INVALID_CANDIDATE_IDS_FORMAT")
        
        # Perform matching
        match_result = await matching_service.match_candidates_for_position(
            db,
            position_id=position_id,
            user_id=current_user.id,
            user_role=current_user.role,
            candidate_ids=candidate_ids,
            limit=match_request.limit,
            min_score=match_request.min_score
        )
        
        logger.info(f"Candidate matching completed for position {position_id}: {len(match_result['matches'])} matches")
        return PositionMatchResponse(**match_result)
        
    except Exception as e:
        logger.error(f"Candidate matching failed for position {position_id}: {str(e)}")
        if "not_found" in str(e).lower():
            raise not_found(str(e))
        else:
            raise service_error(str(e))


@router.get("/{position_id}/similar")
async def find_similar_positions(
    position_id: int,
    limit: int = Query(10, ge=1, le=50, description="Limit similar positions"),
    min_similarity: float = Query(0.7, ge=0, le=1, description="Minimum similarity score"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.POSITIONS_READ))
) -> Any:
    """
    Find similar positions using vector similarity
    """
    # Check if user has access to the position
    position = await position_crud.get_with_permission(
        db,
        id=position_id,
        user_id=current_user.id,
        user_role=current_user.role
    )
    
    if not position:
        raise not_found("POSITION_NOT_FOUND_OR_NO_ACCESS")
    
    try:
        similar_positions = await matching_service.find_similar_positions(
            db,
            position_id=position_id,
            user_id=current_user.id,
            user_role=current_user.role,
            limit=limit,
            min_similarity=min_similarity
        )
        
        logger.info(f"Found {len(similar_positions)} similar positions for {position_id}")
        return {
            "position_id": str(position_id),
            "similar_positions": similar_positions,
            "total_found": len(similar_positions),
            "search_config": {
                "limit": limit,
                "min_similarity": min_similarity
            }
        }
        
    except Exception as e:
        logger.error(f"Similar position search failed for {position_id}: {str(e)}")
        if "not_found" in str(e).lower():
            raise not_found(str(e))
        else:
            raise service_error(str(e))


@router.get("/{position_id}/analytics")
async def get_position_analytics(
    position_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.POSITIONS_READ))
) -> Any:
    """
    Get position analytics and statistics
    """
    # Check if user has access to the position
    position = await position_crud.get_with_permission(
        db,
        id=position_id,
        user_id=current_user.id,
        user_role=current_user.role
    )
    
    if not position:
        raise not_found("POSITION_NOT_FOUND_OR_NO_ACCESS")
    
    try:
        # Get vector statistics if available
        from app.crud.job_vector import job_vector as job_vector_crud
        vector_stats = await job_vector_crud.get_vector_stats(db, [position_id])
        
        # Basic position analytics
        analytics = {
            "position_id": str(position_id),
            "basic_info": {
                "title": position.title,
                "status": position.status.value,
                "urgency": position.urgency.value if position.urgency else None,
                "created_at": position.created_at,
                "days_open": (position.created_at - position.created_at).days if position.deadline else None
            },
            "requirements_analysis": {
                "required_skills_count": len(position.required_skills or []),
                "preferred_skills_count": len(position.preferred_skills or []),
                "responsibilities_count": len(position.responsibilities or []),
                "qualifications_count": len(position.qualifications or [])
            },
            "vector_info": vector_stats,
            "matching_potential": {
                "has_embeddings": vector_stats.get("total_vectors", 0) > 0,
                "ready_for_ai_matching": vector_stats.get("total_vectors", 0) > 0
            }
        }
        
        return analytics
        
    except Exception as e:
        logger.error(f"Position analytics failed for {position_id}: {str(e)}")
        raise service_error(f"POSITION_ANALYTICS_FAILED: {str(e)}")


async def generate_position_embeddings_background(position_id: int, force_regenerate: bool = False):
    """Background task for generating position embeddings"""
    try:
        from app.core.database import AsyncSessionLocal
        
        async with AsyncSessionLocal() as db:
            result = await matching_service.generate_position_embeddings(
                db, position_id, force_regenerate
            )
            logger.info(f"Background embedding generation completed for position {position_id}: {result['status']}")
            
    except Exception as e:
        logger.error(f"Background embedding generation failed for position {position_id}: {str(e)}")