"""auto_merge_heads

Revision ID: 591aec21aa13
Revises: 84975b4cf260
Create Date: 2025-08-15 07:55:38.920500

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '591aec21aa13'
down_revision: Union[str, None] = '84975b4cf260'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass