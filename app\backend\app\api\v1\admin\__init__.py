"""
Admin API routes
"""
from fastapi import APIRouter

from app.api.v1.admin import monitoring, permissions, roles, forms, submissions

api_router = APIRouter()

api_router.include_router(permissions.router, prefix="/permissions", tags=["permissions"])
api_router.include_router(roles.router, prefix="/roles", tags=["roles"])
api_router.include_router(monitoring.router, prefix="/monitoring", tags=["monitoring"])
api_router.include_router(forms.router, prefix="/forms", tags=["application-forms"])
# api_router.include_router(questionnaires.router, prefix="/questionnaires", tags=["questionnaires"])  # File not found
api_router.include_router(submissions.router, prefix="/submissions", tags=["submissions"])
