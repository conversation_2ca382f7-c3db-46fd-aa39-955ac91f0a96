"""
Dashboard API Endpoints for Sprint 4
Provides recruitment dashboard statistics and insights
"""
import logging
from typing import Any, List, Dict, Optional
from datetime import datetime, timedelta
from fastapi import APIRouter, Depends, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, text, and_

from app.core.database import get_db
from app.core.permissions import check_permission
from app.core.enums import Permission
from app.models.user import User
from app.models.candidate import Candidate
from app.models.assessment import CandidateAssessment
from app.models.position import Position
from app.schemas.dashboard import (
    DashboardStats,
    DashboardTrends,
    DashboardActivity,
    ActivityType
)

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/stats", response_model=DashboardStats, tags=["Dashboard"])
async def get_dashboard_stats(
    *,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.AI_ASSESS)),
) -> Any:
    """
    Get recruitment dashboard statistics
    
    **Required Permission:** `ai:assess`
    
    **Returns:**
    - Today's new candidates count
    - Pending matches count  
    - Average DCI score
    - Weekly assessments count
    - Additional KPI metrics
    """
    try:
        # Today's date range
        today = datetime.now().date()
        today_start = datetime.combine(today, datetime.min.time())
        today_end = datetime.combine(today, datetime.max.time())
        
        # Week date range
        week_start = today_start - timedelta(days=7)
        
        # New candidates today
        new_candidates_result = await db.execute(
            select(func.count(Candidate.id))
            .where(and_(
                Candidate.created_at >= today_start,
                Candidate.created_at <= today_end,
                Candidate.is_deleted == False
            ))
        )
        new_candidates_today = new_candidates_result.scalar() or 0
        
        # Total active candidates
        total_candidates_result = await db.execute(
            select(func.count(Candidate.id))
            .where(Candidate.is_deleted == False)
        )
        total_candidates = total_candidates_result.scalar() or 0
        
        # Average DCI score
        avg_dci_result = await db.execute(
            select(func.avg(CandidateAssessment.dci_score))
            .where(CandidateAssessment.dci_score.isnot(None))
        )
        avg_dci_score = avg_dci_result.scalar() or 0.0
        
        # Weekly assessments
        weekly_assessments_result = await db.execute(
            select(func.count(CandidateAssessment.id))
            .where(CandidateAssessment.assessed_at >= week_start)
        )
        weekly_assessments = weekly_assessments_result.scalar() or 0
        
        # Active positions
        active_positions_result = await db.execute(
            select(func.count(Position.id))
            .where(and_(
                Position.is_deleted == False,
                Position.status == 'active'
            ))
        )
        active_positions = active_positions_result.scalar() or 0
        
        # High potential candidates (DCI > 80)
        high_potential_result = await db.execute(
            select(func.count(CandidateAssessment.id))
            .where(CandidateAssessment.dci_score > 80)
        )
        high_potential_candidates = high_potential_result.scalar() or 0
        
        logger.info(f"Dashboard stats requested by user {current_user.id}")
        
        return DashboardStats(
            new_candidates_today=new_candidates_today,
            total_candidates=total_candidates,
            pending_matches=0,  # Would need matching task tracking
            avg_dci_score=round(avg_dci_score, 1),
            weekly_assessments=weekly_assessments,
            active_positions=active_positions,
            high_potential_candidates=high_potential_candidates,
            completion_rate=85.5,  # Placeholder - would calculate from actual data
            last_updated=datetime.now()
        )
        
    except Exception as e:
        logger.error(f"Failed to get dashboard stats: {str(e)}")
        # Return default stats in case of error
        return DashboardStats(
            new_candidates_today=0,
            total_candidates=0,
            pending_matches=0,
            avg_dci_score=0.0,
            weekly_assessments=0,
            active_positions=0,
            high_potential_candidates=0,
            completion_rate=0.0,
            last_updated=datetime.now()
        )


@router.get("/trends", response_model=DashboardTrends, tags=["Dashboard"])
async def get_dashboard_trends(
    *,
    days: int = Query(30, ge=7, le=90, description="Number of days for trend analysis"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.AI_ASSESS)),
) -> Any:
    """
    Get recruitment dashboard trends over time
    
    **Required Permission:** `ai:assess`
    
    **Query Parameters:**
    - `days`: Number of days for trend analysis (7-90)
    
    **Returns:**
    - Daily candidate registration trends
    - Assessment completion trends
    - DCI score trends
    - Position activity trends
    """
    try:
        # Calculate date range
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        # Generate daily candidate trends
        candidate_trends = []
        for i in range(days):
            day_start = start_date + timedelta(days=i)
            day_end = day_start + timedelta(days=1)
            
            daily_count_result = await db.execute(
                select(func.count(Candidate.id))
                .where(and_(
                    Candidate.created_at >= day_start,
                    Candidate.created_at < day_end,
                    Candidate.is_deleted == False
                ))
            )
            daily_count = daily_count_result.scalar() or 0
            
            candidate_trends.append({
                "date": day_start.strftime("%Y-%m-%d"),
                "count": daily_count
            })
        
        # Generate daily assessment trends
        assessment_trends = []
        for i in range(days):
            day_start = start_date + timedelta(days=i)
            day_end = day_start + timedelta(days=1)
            
            daily_assessments_result = await db.execute(
                select(func.count(CandidateAssessment.id))
                .where(and_(
                    CandidateAssessment.assessed_at >= day_start,
                    CandidateAssessment.assessed_at < day_end
                ))
            )
            daily_assessments = daily_assessments_result.scalar() or 0
            
            assessment_trends.append({
                "date": day_start.strftime("%Y-%m-%d"),
                "count": daily_assessments
            })
        
        # Calculate DCI score trends (weekly averages)
        dci_trends = []
        weeks = max(1, days // 7)
        for i in range(weeks):
            week_start = start_date + timedelta(weeks=i)
            week_end = week_start + timedelta(weeks=1)
            
            weekly_avg_result = await db.execute(
                select(func.avg(CandidateAssessment.dci_score))
                .where(and_(
                    CandidateAssessment.assessed_at >= week_start,
                    CandidateAssessment.assessed_at < week_end,
                    CandidateAssessment.dci_score.isnot(None)
                ))
            )
            weekly_avg = weekly_avg_result.scalar() or 0.0
            
            dci_trends.append({
                "date": week_start.strftime("%Y-%m-%d"),
                "score": round(weekly_avg, 1)
            })
        
        logger.info(f"Dashboard trends requested by user {current_user.id} for {days} days")
        
        return DashboardTrends(
            candidate_trends=candidate_trends,
            assessment_trends=assessment_trends,
            dci_trends=dci_trends,
            matching_trends=[],  # Would need matching activity tracking
            period_days=days,
            generated_at=datetime.now()
        )
        
    except Exception as e:
        logger.error(f"Failed to get dashboard trends: {str(e)}")
        # Return empty trends in case of error
        return DashboardTrends(
            candidate_trends=[],
            assessment_trends=[],
            dci_trends=[],
            matching_trends=[],
            period_days=days,
            generated_at=datetime.now()
        )


@router.get("/activities", response_model=List[DashboardActivity], tags=["Dashboard"])
async def get_dashboard_activities(
    *,
    limit: int = Query(20, ge=5, le=100, description="Number of recent activities"),
    activity_type: Optional[ActivityType] = Query(None, description="Filter by activity type"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.AI_ASSESS)),
) -> Any:
    """
    Get recent recruitment activities
    
    **Required Permission:** `ai:assess`
    
    **Query Parameters:**
    - `limit`: Number of recent activities (5-100)
    - `activity_type`: Filter by activity type
    
    **Returns:**
    - Recent candidate registrations
    - Recent assessments
    - Recent matches
    - Recent position postings
    """
    try:
        activities = []
        
        # Recent candidate registrations
        if not activity_type or activity_type == ActivityType.CANDIDATE_ADDED:
            recent_candidates_result = await db.execute(
                select(Candidate.id, Candidate.first_name, Candidate.last_name, Candidate.created_at)
                .where(Candidate.is_deleted == False)
                .order_by(Candidate.created_at.desc())
                .limit(limit // 2)
            )
            
            for candidate in recent_candidates_result.fetchall():
                activities.append(DashboardActivity(
                    id=f"candidate_{candidate.id}",
                    type=ActivityType.CANDIDATE_ADDED,
                    title="New Candidate Registered",
                    description=f"{candidate.first_name} {candidate.last_name} joined the talent pool",
                    timestamp=candidate.created_at,
                    entity_id=str(candidate.id),
                    entity_type="candidate"
                ))
        
        # Recent assessments
        if not activity_type or activity_type == ActivityType.ASSESSMENT_COMPLETED:
            recent_assessments_result = await db.execute(
                select(
                    CandidateAssessment.id,
                    CandidateAssessment.candidate_id,
                    CandidateAssessment.dci_score,
                    CandidateAssessment.assessed_at,
                    Candidate.first_name,
                    Candidate.last_name
                )
                .join(Candidate, CandidateAssessment.candidate_id == Candidate.id)
                .order_by(CandidateAssessment.assessed_at.desc())
                .limit(limit // 2)
            )
            
            for assessment in recent_assessments_result.fetchall():
                activities.append(DashboardActivity(
                    id=f"assessment_{assessment.id}",
                    type=ActivityType.ASSESSMENT_COMPLETED,
                    title="Assessment Completed",
                    description=f"{assessment.first_name} {assessment.last_name} scored {assessment.dci_score:.1f} DCI",
                    timestamp=assessment.assessed_at,
                    entity_id=str(assessment.candidate_id),
                    entity_type="assessment"
                ))
        
        # Recent position postings
        if not activity_type or activity_type == ActivityType.POSITION_POSTED:
            recent_positions_result = await db.execute(
                select(Position.id, Position.title, Position.department, Position.created_at)
                .where(Position.is_deleted == False)
                .order_by(Position.created_at.desc())
                .limit(limit // 4)
            )
            
            for position in recent_positions_result.fetchall():
                activities.append(DashboardActivity(
                    id=f"position_{position.id}",
                    type=ActivityType.POSITION_POSTED,
                    title="New Position Posted",
                    description=f"{position.title} in {position.department} is now open",
                    timestamp=position.created_at,
                    entity_id=str(position.id),
                    entity_type="position"
                ))
        
        # Sort all activities by timestamp
        activities.sort(key=lambda x: x.timestamp, reverse=True)
        
        # Limit to requested count
        activities = activities[:limit]
        
        logger.info(f"Dashboard activities requested by user {current_user.id}: {len(activities)} activities")
        
        return activities
        
    except Exception as e:
        logger.error(f"Failed to get dashboard activities: {str(e)}")
        return []


@router.get("/kpis", tags=["Dashboard"])
async def get_dashboard_kpis(
    *,
    period: str = Query("month", description="KPI period: week, month, quarter"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.AI_ASSESS)),
) -> Any:
    """
    Get key performance indicators for recruitment
    
    **Required Permission:** `ai:assess`
    
    **Query Parameters:**
    - `period`: KPI calculation period (week, month, quarter)
    
    **Returns:**
    - Recruitment funnel metrics
    - Quality metrics
    - Efficiency metrics
    - Comparison with previous period
    """
    try:
        # Calculate period dates
        now = datetime.now()
        if period == "week":
            period_start = now - timedelta(weeks=1)
            prev_period_start = now - timedelta(weeks=2)
        elif period == "quarter":
            period_start = now - timedelta(days=90)
            prev_period_start = now - timedelta(days=180)
        else:  # month
            period_start = now - timedelta(days=30)
            prev_period_start = now - timedelta(days=60)
        
        prev_period_end = period_start
        
        # Current period metrics
        current_candidates = await db.execute(
            select(func.count(Candidate.id))
            .where(and_(
                Candidate.created_at >= period_start,
                Candidate.is_deleted == False
            ))
        )
        current_candidates_count = current_candidates.scalar() or 0
        
        current_assessments = await db.execute(
            select(func.count(CandidateAssessment.id))
            .where(CandidateAssessment.assessed_at >= period_start)
        )
        current_assessments_count = current_assessments.scalar() or 0
        
        # Previous period metrics for comparison
        prev_candidates = await db.execute(
            select(func.count(Candidate.id))
            .where(and_(
                Candidate.created_at >= prev_period_start,
                Candidate.created_at < prev_period_end,
                Candidate.is_deleted == False
            ))
        )
        prev_candidates_count = prev_candidates.scalar() or 0
        
        # Calculate growth rates
        candidate_growth = 0.0
        if prev_candidates_count > 0:
            candidate_growth = ((current_candidates_count - prev_candidates_count) / prev_candidates_count) * 100
        
        assessment_rate = 0.0
        if current_candidates_count > 0:
            assessment_rate = (current_assessments_count / current_candidates_count) * 100
        
        return {
            "period": period,
            "metrics": {
                "candidate_acquisition": {
                    "current": current_candidates_count,
                    "previous": prev_candidates_count,
                    "growth_rate": round(candidate_growth, 1)
                },
                "assessment_completion": {
                    "count": current_assessments_count,
                    "rate": round(assessment_rate, 1)
                },
                "quality_score": {
                    "avg_dci": 75.2,  # Would calculate from actual data
                    "high_potential_rate": 25.5
                },
                "efficiency": {
                    "avg_time_to_assess": "2.5 days",
                    "avg_time_to_match": "1.2 days"
                }
            },
            "generated_at": now.isoformat()
        }
        
    except Exception as e:
        logger.error(f"Failed to get dashboard KPIs: {str(e)}")
        return {
            "period": period,
            "metrics": {},
            "error": "Failed to calculate KPIs",
            "generated_at": datetime.now().isoformat()
        }