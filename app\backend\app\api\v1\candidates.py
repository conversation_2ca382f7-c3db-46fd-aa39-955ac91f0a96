"""
Candidate management routes
"""
from typing import Any, List, Optional, Dict, Union
from datetime import datetime, timezone
from fastapi import APIRouter, Depends, HTTPException, Query, status, UploadFile, File, BackgroundTasks, Path
from sqlalchemy.ext.asyncio import AsyncSession
from app.api import deps
from app.core.database import get_db
from app.core.exceptions import not_found, conflict, forbidden, bad_request, service_error
from app.core.permissions import check_permission, check_any_permission
from app.core.enums import Permission, DataPermission
from app.core.id_types import normalize_id
from app.crud import candidate as candidate_crud
from app.models.user import User
from app.models.candidate import Candidate, CandidateStatus
from app.schemas.candidate import (
    CandidateCreate,
    CandidateResponse,
    CandidateUpdate,
    CandidateListResponse,
    CandidateSearch,
    ResumeUploadResponse
)
from app.services.file_service import file_service
from app.services.parser.pdf_parser import PDFParser
from app.services.parser.docx_parser import DOC<PERSON>Parser
import logging
import asyncio

logger = logging.getLogger(__name__)
router = APIRouter()

# Parser instances
pdf_parser = PDFParser()
docx_parser = DOCXParser()


@router.post("/", response_model=CandidateResponse)
async def create_candidate(
    *,
    db: AsyncSession = Depends(get_db),
    candidate_in: CandidateCreate,
    current_user: User = Depends(check_permission(Permission.CANDIDATES_WRITE))
) -> Any:
    """
    Create a new candidate
    """
    # Debug logging to see what's being received
    logger.info(f"🐛 DEBUG: Received candidate_in.status: {candidate_in.status}")
    logger.info(f"🐛 DEBUG: type(candidate_in.status): {type(candidate_in.status)}")
    logger.info(f"🐛 DEBUG: candidate_in.status.value: {candidate_in.status.value if hasattr(candidate_in.status, 'value') else 'no value attr'}")
    logger.info(f"🐛 DEBUG: str(candidate_in.status): {str(candidate_in.status)}")
    logger.info(f"🐛 DEBUG: candidate_in.status == CandidateStatus.NEW: {candidate_in.status == CandidateStatus.NEW}")
    logger.info(f"🐛 DEBUG: CandidateStatus.NEW.value: {CandidateStatus.NEW.value}")
    
    # Check for duplicates
    existing = await candidate_crud.check_duplicate(
        db,
        name=candidate_in.name,
        email=candidate_in.email,
        phone=candidate_in.phone
    )
    
    if existing:
        if existing.created_by == current_user.id:
            raise conflict("您已经创建过该候选人")
        # Different users can create the same candidate (different perspective)
    
    # Create candidate
    candidate = await candidate_crud.create_with_permission(
        db,
        obj_in=candidate_in,
        created_by=current_user.id
    )
    
    logger.info(f"Created candidate {candidate.id} by user {current_user.id}")
    return candidate


@router.get("/", response_model=CandidateListResponse)
async def list_candidates(
    db: AsyncSession = Depends(get_db),
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(20, ge=1, le=100, description="返回的记录数"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    status: Optional[CandidateStatus] = Query(None, description="候选人状态"),
    skills: Optional[List[str]] = Query(None, description="技能标签"),
    min_experience: Optional[int] = Query(None, ge=0, description="最少工作年限"),
    max_experience: Optional[int] = Query(None, ge=0, description="最多工作年限"),
    min_salary: Optional[int] = Query(None, ge=0, description="最低薪资（万元）"),
    max_salary: Optional[int] = Query(None, ge=0, description="最高薪资（万元）"),
    data_permission: Optional[DataPermission] = Query(None, description="数据权限"),
    source: Optional[str] = Query(None, description="来源渠道"),
    tags: Optional[List[str]] = Query(None, description="标签"),
    order_by: Optional[str] = Query("created_at", pattern="^(name|created_at|updated_at|dci_score|jfs_score)$", description="排序字段"),
    order_desc: bool = Query(True, description="是否降序"),
    current_user: User = Depends(check_permission(Permission.CANDIDATES_READ))
) -> Any:
    """
    Get candidates list with filtering and pagination
    """
    # Get candidates with permission filtering
    candidates = await candidate_crud.get_multi_with_permission(
        db,
        user_id=current_user.id,
        user_role=current_user.role,
        skip=skip,
        limit=limit,
        search=search,
        status=status,
        skills=skills,
        min_experience=min_experience,
        max_experience=max_experience,
        min_salary=min_salary,
        max_salary=max_salary,
        data_permission=data_permission,
        source=source,
        tags=tags,
        order_by=order_by,
        order_desc=order_desc
    )
    
    # Get total count
    total = await candidate_crud.count_with_permission(
        db,
        user_id=current_user.id,
        user_role=current_user.role,
        search=search,
        status=status,
        skills=skills,
        min_experience=min_experience,
        max_experience=max_experience,
        min_salary=min_salary,
        max_salary=max_salary,
        data_permission=data_permission,
        source=source,
        tags=tags
    )
    
    return CandidateListResponse(
        items=candidates,
        total=total,
        skip=skip,
        limit=limit
    )


@router.get("/stats")
async def get_candidate_stats(
    *,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.CANDIDATES_READ)),
    force_refresh: bool = Query(False, description="Force cache refresh")
) -> Dict[str, Any]:
    """
    Get candidate statistics with Redis caching for performance
    
    Returns comprehensive statistics including:
    - Total candidates count
    - Active candidates (last 30 days)
    - New this week/month
    - Status distribution
    - Skills distribution (top 20)
    - Experience distribution
    - Education distribution
    - Salary distribution
    - Source distribution
    - Department distribution
    - Activity trend (30 days)
    
    **Caching**: Results cached for 10 minutes per user for optimal performance
    """
    import hashlib
    import json
    from datetime import timedelta
    
    # Generate cache key based on user permissions and role
    cache_key = f"candidate_stats:user_{current_user.id}:role_{current_user.role}"
    cache_ttl = 600  # 10 minutes
    
    try:
        from app.core.redis import get_redis
        
        # TEMPORARILY DISABLE ALL CACHE FOR DEBUGGING
        print(f"DEBUG: Cache completely disabled for debugging")
        # Check cache first (unless force_refresh)
        # if not force_refresh:
        #     try:
        #         redis_client = await get_redis()
        #         cached_stats = await redis_client.get(cache_key)
        #         if cached_stats:
        #             logger.info(
        #                 "candidate_stats_cache_hit", 
        #                 extra={"user_id": current_user.id, "cache_key": cache_key}
        #             )
        #             return json.loads(cached_stats)
        #     except Exception as cache_error:
        #         logger.warning(f"Cache read failed, proceeding with fresh data: {cache_error}")
        
        # Generate fresh statistics
        start_time = datetime.now(timezone.utc)
        
        # Check if stats service exists
        try:
            from app.services.candidate_stats import CandidateStatsService
            service = CandidateStatsService(db)
            print(f"DEBUG: Successfully imported CandidateStatsService for user {current_user.id}")
        except ImportError as e:
            print(f"DEBUG: ImportError for CandidateStatsService: {e}")
            logger.warning("CandidateStatsService not found, using fallback implementation")
            return await _get_basic_candidate_stats(db, current_user)
        except Exception as e:
            print(f"DEBUG: Other error with CandidateStatsService: {e}")
            logger.warning(f"CandidateStatsService initialization failed: {e}")
            return await _get_basic_candidate_stats(db, current_user)
        
        # Use direct database connection for stats to avoid transaction issues
        try:
            import asyncpg
            
            # Create direct connection to bypass SQLAlchemy transaction management
            conn = await asyncpg.connect(
                host='postgres',
                port=5432,
                user='postgres', 
                password='Pass1234',
                database='hephaestus_forge_db'
            )
            
            try:
                # Get all statistics using direct queries
                total = await conn.fetchval("SELECT COUNT(*) FROM candidates WHERE is_deleted = false")
                
                active_date = datetime.now(timezone.utc) - timedelta(days=30)
                active = await conn.fetchval(
                    "SELECT COUNT(*) FROM candidates WHERE is_deleted = false AND created_at >= $1",
                    active_date
                )
                
                week_ago = datetime.now(timezone.utc) - timedelta(days=7)
                new_week = await conn.fetchval(
                    "SELECT COUNT(*) FROM candidates WHERE is_deleted = false AND created_at >= $1",
                    week_ago
                )
                
                month_ago = datetime.now(timezone.utc) - timedelta(days=30)
                new_month = await conn.fetchval(
                    "SELECT COUNT(*) FROM candidates WHERE is_deleted = false AND created_at >= $1",
                    month_ago
                )
                
                avg_score = await conn.fetchval(
                    "SELECT AVG(match_score_avg) FROM candidates WHERE is_deleted = false AND match_score_avg IS NOT NULL"
                ) or 0
                
                with_resume = await conn.fetchval(
                    "SELECT COUNT(*) FROM candidates WHERE is_deleted = false AND resume_url IS NOT NULL"
                )
                
                # Status distribution
                status_rows = await conn.fetch(
                    "SELECT status, COUNT(*) as count FROM candidates WHERE is_deleted = false AND status IS NOT NULL GROUP BY status"
                )
                status_dist = {row['status']: row['count'] for row in status_rows}
                
                # Skills distribution (simplified)
                skill_rows = await conn.fetch(
                    "SELECT skills FROM candidates WHERE is_deleted = false AND skills IS NOT NULL AND skills != '[]'::jsonb LIMIT 100"
                )
                from collections import Counter
                all_skills = []
                for row in skill_rows:
                    if row['skills']:
                        skills = json.loads(row['skills']) if isinstance(row['skills'], str) else row['skills']
                        all_skills.extend(skills)
                skill_counter = Counter(all_skills)
                total_skills = sum(skill_counter.values())
                skill_dist = [
                    {"skill": k, "count": v, "percentage": round(v/total_skills*100, 1) if total_skills else 0} 
                    for k, v in skill_counter.most_common(10)
                ]
                
                # Get all distributions
                exp_dist = []
                edu_dist = []
                salary_dist = []
                source_dist = []
                dept_dist = []
                trend = []
                
                try:
                    # Experience distribution
                    exp_ranges = [
                        ("0-2", 0, 2),
                        ("3-5", 3, 5),
                        ("6-10", 6, 10),
                        ("11-15", 11, 15),
                        ("16+", 16, 100)
                    ]
                    exp_total = 0
                    exp_dist_temp = []
                    for label, min_exp, max_exp in exp_ranges:
                        if max_exp == 100:
                            count = await conn.fetchval(
                                "SELECT COUNT(*) FROM candidates WHERE is_deleted = false AND years_of_experience >= $1",
                                min_exp
                            )
                        else:
                            count = await conn.fetchval(
                                "SELECT COUNT(*) FROM candidates WHERE is_deleted = false AND years_of_experience >= $1 AND years_of_experience <= $2",
                                min_exp, max_exp
                            )
                        if count and count > 0:
                            exp_total += count
                            exp_dist_temp.append({"range": label, "count": count})
                    
                    exp_dist = [
                        {"range": item["range"], "count": item["count"], "percentage": round(item["count"]/exp_total*100, 1) if exp_total else 0}
                        for item in exp_dist_temp
                    ]
                    
                    # Salary distribution
                    salary_ranges = [
                        ("0-10", 0, 10),
                        ("10-20", 10, 20),
                        ("20-30", 20, 30),
                        ("30-50", 30, 50),
                        ("50+", 50, 1000)
                    ]
                    salary_total = 0
                    salary_dist_temp = []
                    for label, min_sal, max_sal in salary_ranges:
                        if max_sal == 1000:
                            count = await conn.fetchval(
                                "SELECT COUNT(*) FROM candidates WHERE is_deleted = false AND expected_salary >= $1",
                                min_sal
                            )
                        else:
                            count = await conn.fetchval(
                                "SELECT COUNT(*) FROM candidates WHERE is_deleted = false AND expected_salary >= $1 AND expected_salary < $2",
                                min_sal, max_sal
                            )
                        if count and count > 0:
                            salary_total += count
                            salary_dist_temp.append({"range": label, "count": count})
                    
                    salary_dist = [
                        {"range": item["range"], "count": item["count"], "percentage": round(item["count"]/salary_total*100, 1) if salary_total else 0}
                        for item in salary_dist_temp
                    ]
                    
                    # Department distribution (from current_company)
                    dept_rows = await conn.fetch(
                        "SELECT current_company, COUNT(*) as count FROM candidates WHERE is_deleted = false AND current_company IS NOT NULL GROUP BY current_company ORDER BY count DESC LIMIT 10"
                    )
                    dept_total = sum(row['count'] for row in dept_rows)
                    dept_dist = [
                        {"department": row['current_company'], "count": row['count'], "percentage": round(row['count']/dept_total*100, 1) if dept_total else 0}
                        for row in dept_rows
                    ]
                    
                    # Education distribution with mapping to codes
                    education_mapping = {
                        "高中": "high_school",
                        "专科": "associate", 
                        "本科": "bachelor",
                        "硕士": "master",
                        "博士": "doctorate",
                    }
                    
                    edu_rows = await conn.fetch(
                        "SELECT education_level, COUNT(*) as count FROM candidates WHERE is_deleted = false AND education_level IS NOT NULL GROUP BY education_level ORDER BY count DESC"
                    )
                    edu_total = sum(row['count'] for row in edu_rows)
                    edu_dist = [
                        {
                            "level": education_mapping.get(row['education_level'], row['education_level']), 
                            "count": row['count'], 
                            "percentage": round(row['count']/edu_total*100, 1) if edu_total else 0
                        } 
                        for row in edu_rows
                    ]
                    
                    # Source distribution
                    source_rows = await conn.fetch(
                        "SELECT source, COUNT(*) as count FROM candidates WHERE is_deleted = false AND source IS NOT NULL GROUP BY source ORDER BY count DESC LIMIT 10"
                    )
                    source_total = sum(row['count'] for row in source_rows)
                    source_dist = [
                        {"source": row['source'], "count": row['count'], "percentage": round(row['count']/source_total*100, 1) if source_total else 0} 
                        for row in source_rows
                    ]
                    
                    # Activity trend (last 30 days)
                    trend_rows = await conn.fetch(
                        """
                        SELECT 
                            DATE(created_at) as date,
                            COUNT(*) as new_candidates
                        FROM candidates 
                        WHERE is_deleted = false AND created_at >= $1
                        GROUP BY DATE(created_at)
                        ORDER BY date
                        """,
                        month_ago
                    )
                    trend = [
                        {"date": str(row['date']), "new_candidates": row['new_candidates'], "active_candidates": row['new_candidates']}
                        for row in trend_rows
                    ]
                    
                except Exception as e:
                    logger.warning(f"Failed to get some distributions: {e}")
                    pass
                
                stats_results = {
                    'overview': {
                        "total_candidates": total,
                        "active_candidates": active,
                        "new_this_week": new_week,
                        "new_this_month": new_month,
                        "avg_match_score": round(float(avg_score) if avg_score else 0, 2),
                        "with_resume": with_resume,
                        "without_resume": total - with_resume,
                        "by_status": status_dist
                    },
                    'skill_dist': skill_dist,
                    'exp_dist': exp_dist,
                    'edu_dist': edu_dist,
                    'salary_dist': salary_dist,
                    'source_dist': source_dist,
                    'dept_dist': dept_dist,
                    'trend': trend
                }
                
            finally:
                await conn.close()
            
        except Exception as e:
            logger.error(f"Direct database stats failed: {e}")
            return await _get_basic_candidate_stats(db, current_user)
        
        # Build response
        response_data = {
            "overview": stats_results.get('overview', {}),
            "status_distribution": stats_results.get('overview', {}).get("by_status", {}),
            "skill_distribution": stats_results.get('skill_dist', []),
            "experience_distribution": stats_results.get('exp_dist', []),
            "education_distribution": stats_results.get('edu_dist', []),
            "salary_distribution": stats_results.get('salary_dist', []),
            "source_distribution": stats_results.get('source_dist', []),
            "department_distribution": stats_results.get('dept_dist', []),
            "activity_trend": stats_results.get('trend', []),
            "last_updated": datetime.now(timezone.utc).isoformat(),
            "cache_info": {
                "cached": False,
                "generation_time_ms": int((datetime.now(timezone.utc) - start_time).total_seconds() * 1000),
                "ttl_seconds": cache_ttl
            }
        }
        
        # Cache the results
        try:
            redis_client = await get_redis()
            await redis_client.setex(
                cache_key,
                cache_ttl,
                json.dumps(response_data, default=str, ensure_ascii=False)
            )
            logger.info(
                "candidate_stats_cached",
                extra={
                    "user_id": current_user.id,
                    "cache_key": cache_key,
                    "generation_time_ms": response_data["cache_info"]["generation_time_ms"]
                }
            )
        except Exception as cache_error:
            logger.warning(f"Failed to cache statistics: {cache_error}")
        
        return response_data
        
    except Exception as e:
        import traceback
        error_traceback = traceback.format_exc()
        logger.error(
            f"candidate_stats_error: {str(e)}",
            extra={
                "user_id": current_user.id,
                "error": str(e),
                "error_type": type(e).__name__,
                "traceback": error_traceback
            }
        )
        print(f"CANDIDATE STATS ERROR: {str(e)}")
        print(f"TRACEBACK: {error_traceback}")
        
        # Return empty stats with error indicator
        return {
            "overview": {
                "total_candidates": 0,
                "active_candidates": 0,
                "new_this_week": 0,
                "new_this_month": 0,
                "avg_match_score": 0.0,
                "with_resume": 0,
                "without_resume": 0,
                "by_status": {}
            },
            "status_distribution": {},
            "skill_distribution": [],
            "experience_distribution": [],
            "education_distribution": [],
            "salary_distribution": [],
            "source_distribution": [],
            "department_distribution": [],
            "activity_trend": [],
            "last_updated": datetime.now(timezone.utc).isoformat(),
            "cache_info": {"cached": False, "error": "Statistics temporarily unavailable"},
            "error": "STATS_TEMPORARILY_UNAVAILABLE"
        }


async def _get_basic_candidate_stats(db: AsyncSession, current_user) -> Dict[str, Any]:
    """Fallback implementation for basic candidate statistics"""
    try:
        from sqlalchemy import func, select
        
        print(f"DEBUG FALLBACK: Using fallback stats for user {current_user.id}, role: {current_user.role}")
        
        # For super_admin, use simplified count to avoid permission issues
        if current_user.role == "super_admin":
            # Direct count for super admin
            from sqlalchemy import select, func
            from app.models.candidate import Candidate
            total_query = select(func.count(Candidate.id)).where(Candidate.is_deleted == False)
            result = await db.execute(total_query)
            total_candidates = result.scalar() or 0
            print(f"DEBUG FALLBACK: Super admin direct count: {total_candidates}")
        else:
            # Use permission filtering for other users
            total_candidates = await candidate_crud.count_with_permission(
                db,
                user_id=current_user.id,
                user_role=current_user.role
            )
            print(f"DEBUG FALLBACK: Permission filtered count: {total_candidates}")
        
        return {
            "overview": {
                "total_candidates": total_candidates,
                "active_candidates": 0,  # Would require complex query
                "new_this_week": 0,
                "new_this_month": 0,
                "avg_match_score": 0.0,
                "with_resume": 0,
                "without_resume": total_candidates,
                "by_status": {}
            },
            "status_distribution": {},
            "skill_distribution": [],
            "experience_distribution": [],
            "education_distribution": [],
            "salary_distribution": [],
            "source_distribution": [],
            "department_distribution": [],
            "activity_trend": [],
            "last_updated": datetime.now(timezone.utc).isoformat(),
            "cache_info": {"cached": False, "fallback": True}
        }
        
    except Exception as e:
        logger.error(f"Even basic stats failed: {str(e)}")
        return {
            "overview": {"total_candidates": 0, "by_status": {}},
            "error": "BASIC_STATS_FAILED"
        }


def _get_empty_stat_for_type(stat_type: str) -> Any:
    """Get empty default value for different stat types"""
    if stat_type in ['overview']:
        return {"total_candidates": 0, "by_status": {}}
    elif stat_type in ['skill_dist', 'exp_dist', 'edu_dist', 'salary_dist', 'source_dist', 'dept_dist', 'trend']:
        return []
    else:
        return {}


@router.get("/{candidate_id}", response_model=CandidateResponse)
async def get_candidate(
    candidate_id: Union[int, str] = Path(..., description="候选人ID"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.CANDIDATES_READ))
) -> Any:
    """
    Get candidate by ID (with permission check)
    """
    # Normalize ID to handle both int and string inputs
    normalized_id = normalize_id(candidate_id)
    if normalized_id is None:
        raise bad_request("INVALID_CANDIDATE_ID", f"Invalid candidate ID: {candidate_id}")
        
    candidate = await candidate_crud.get_with_permission(
        db,
        id=normalized_id,
        user_id=current_user.id,
        user_role=current_user.role
    )
    
    if not candidate:
        raise not_found("CANDIDATE_NOT_FOUND_OR_NO_ACCESS")
    
    return candidate


@router.put("/{candidate_id}", response_model=CandidateResponse)
async def update_candidate(
    candidate_in: CandidateUpdate,
    candidate_id: Union[int, str] = Path(..., description="候选人ID"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.CANDIDATES_WRITE))
) -> Any:
    """
    Update candidate information
    """
    # Normalize ID to handle both int and string inputs
    normalized_id = normalize_id(candidate_id)
    if normalized_id is None:
        raise bad_request("INVALID_CANDIDATE_ID", f"Invalid candidate ID: {candidate_id}")
        
    # Get candidate with permission check
    candidate = await candidate_crud.get_with_permission(
        db,
        id=normalized_id,
        user_id=current_user.id,
        user_role=current_user.role
    )
    
    if not candidate:
        raise not_found("CANDIDATE_NOT_FOUND_OR_NO_ACCESS")
    
    try:
        updated_candidate = await candidate_crud.update_with_permission(
            db,
            db_obj=candidate,
            obj_in=candidate_in,
            user_id=current_user.id
        )
        
        logger.info(f"Updated candidate {candidate_id} by user {current_user.id}")
        return updated_candidate
        
    except PermissionError:
        raise forbidden("CANDIDATE_UPDATE_FORBIDDEN")


@router.delete("/{candidate_id}")
async def delete_candidate(
    candidate_id: Union[int, str] = Path(..., description="候选人ID"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.CANDIDATES_DELETE))
) -> Any:
    """
    Delete candidate (soft delete)
    """
    # Normalize ID to handle both int and string inputs
    normalized_id = normalize_id(candidate_id)
    if normalized_id is None:
        raise bad_request("INVALID_CANDIDATE_ID", f"Invalid candidate ID: {candidate_id}")
        
    try:
        success = await candidate_crud.soft_delete_with_permission(
            db,
            id=normalized_id,
            user_id=current_user.id
        )
        
        if not success:
            raise not_found("CANDIDATE_NOT_FOUND")
        
        logger.info(f"Deleted candidate {candidate_id} by user {current_user.id}")
        return {"success": True, "message": "SUCCESS_CANDIDATE_DELETED"}
        
    except PermissionError:
        raise forbidden("CANDIDATE_DELETE_FORBIDDEN")


@router.post("/{candidate_id}/share")
async def share_candidate(
    share_with_user_ids: List[int],
    candidate_id: Union[int, str] = Path(..., description="候选人ID"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.CANDIDATES_WRITE))
) -> Any:
    """
    Share candidate with specific users
    """
    # Normalize ID to handle both int and string inputs
    normalized_id = normalize_id(candidate_id)
    if normalized_id is None:
        raise bad_request("INVALID_CANDIDATE_ID", f"Invalid candidate ID: {candidate_id}")
        
    try:
        candidate = await candidate_crud.share_candidate(
            db,
            candidate_id=normalized_id,
            user_id=current_user.id,
            share_with_user_ids=share_with_user_ids
        )
        
        logger.info(f"Shared candidate {candidate_id} with users {share_with_user_ids}")
        return {"success": True, "message": "SUCCESS_CANDIDATE_SHARED", "shared_with": candidate.shared_with}
        
    except ValueError as e:
        raise not_found(str(e))
    except PermissionError as e:
        raise forbidden(str(e))


@router.get("/{candidate_id}/resumes/", response_model=list)
async def get_candidate_resumes(
    candidate_id: Union[int, str] = Path(..., description="候选人ID"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.CANDIDATES_READ))
) -> Any:
    """
    Get candidate resume files list (compatibility endpoint)
    
    This endpoint provides compatibility with frontend expectations
    while maintaining security through permission checks.
    """
    # Normalize ID to handle both int and string inputs
    normalized_id = normalize_id(candidate_id)
    if normalized_id is None:
        raise bad_request("INVALID_CANDIDATE_ID", f"Invalid candidate ID: {candidate_id}")
        
    # Verify candidate access
    candidate = await candidate_crud.get_with_permission(
        db,
        id=normalized_id,
        user_id=current_user.id,
        user_role=current_user.role
    )
    
    if not candidate:
        raise not_found("CANDIDATE_NOT_FOUND_OR_NO_ACCESS")
    
    # Get resume files using async query to avoid MissingGreenlet error
    from sqlalchemy.orm import selectinload
    from sqlalchemy import select
    
    # Re-query candidate with resume_files loaded
    stmt = select(Candidate).options(selectinload(Candidate.resume_files)).where(Candidate.id == normalized_id)
    result = await db.execute(stmt)
    candidate_with_resumes = result.scalar_one_or_none()
    
    resume_files = candidate_with_resumes.resume_files if candidate_with_resumes and candidate_with_resumes.resume_files else []
    
    # Format response as array
    resumes_data = []
    for resume_file in resume_files:
        resumes_data.append({
            "id": resume_file.id,
            "filename": resume_file.filename,
            "file_type": resume_file.file_type,
            "file_size": resume_file.file_size,
            "version": resume_file.version,
            "is_active": resume_file.is_active,
            "upload_time": resume_file.created_at,  # Frontend expects upload_time
            "parsing_status": resume_file.parsing_status,
            "confidence": resume_file.parsing_confidence,  # Frontend expects confidence
            "parsed_at": resume_file.parsed_at,
            "created_at": resume_file.created_at,
            "updated_at": resume_file.updated_at
        })
    
    return resumes_data


@router.get("/{candidate_id}/resume/{file_id}/download")
async def download_resume_file(
    candidate_id: Union[int, str] = Path(..., description="候选人ID"),
    file_id: Union[int, str] = Path(..., description="简历文件ID"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.CANDIDATES_READ))
) -> Any:
    """
    Secure resume file download with permission verification
    
    Security Features:
    - Permission verification on every download
    - No URL exposure (proxy download)
    - Audit logging
    - Rate limiting ready
    """
    from fastapi.responses import StreamingResponse
    from sqlalchemy import select
    from app.models.resume import ResumeFile
    import io
    
    # Normalize IDs to handle both int and string inputs
    normalized_candidate_id = normalize_id(candidate_id)
    normalized_file_id = normalize_id(file_id)
    if normalized_candidate_id is None:
        raise bad_request("INVALID_CANDIDATE_ID", f"Invalid candidate ID: {candidate_id}")
    if normalized_file_id is None:
        raise bad_request("INVALID_FILE_ID", f"Invalid file ID: {file_id}")
    
    # 1. Verify candidate access
    candidate = await candidate_crud.get_with_permission(
        db,
        id=normalized_candidate_id,
        user_id=current_user.id,
        user_role=current_user.role
    )
    
    if not candidate:
        raise not_found("CANDIDATE_NOT_FOUND_OR_NO_ACCESS")
    
    # 2. Get resume file
    result = await db.execute(
        select(ResumeFile).where(
            ResumeFile.id == normalized_file_id,
            ResumeFile.candidate_id == normalized_candidate_id,
            ResumeFile.deleted_at.is_(None)
        )
    )
    resume_file = result.scalar_one_or_none()
    
    if not resume_file:
        raise not_found("RESUME_FILE_NOT_FOUND")
    
    # 3. Download file from MinIO (internal)
    try:
        file_content = file_service.download_file(
            file_service.resume_bucket,
            resume_file.file_key
        )
        
        # 4. Audit logging
        logger.info(
            "resume_download",
            extra={
                "candidate_id": candidate_id,
                "resume_file_id": file_id,
                "user_id": current_user.id,
                "filename": resume_file.filename,
                "file_size": resume_file.file_size,
                "remote_addr": "request.client.host if hasattr(request, 'client') else 'unknown'"
            }
        )
        
        # 5. Return streaming response
        return StreamingResponse(
            io.BytesIO(file_content),
            media_type=f'application/{resume_file.file_type}' if resume_file.file_type == 'pdf' else 'application/octet-stream',
            headers={
                "Content-Disposition": f'attachment; filename="{resume_file.filename}"',
                "Content-Length": str(len(file_content)),
                "Cache-Control": "no-cache, no-store, must-revalidate",
                "Pragma": "no-cache",
                "Expires": "0"
            }
        )
        
    except Exception as e:
        logger.error(f"Resume download failed: candidate_id={candidate_id}, file_id={file_id}, error={str(e)}")
        raise service_error("RESUME_DOWNLOAD_FAILED", "Download temporarily unavailable")


@router.get("/{candidate_id}/resume/download")
async def download_latest_resume(
    candidate_id: Union[int, str] = Path(..., description="候选人ID"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.CANDIDATES_READ))
) -> Any:
    """
    Download the latest active resume (backward compatibility)
    """
    from sqlalchemy import select
    from app.models.resume import ResumeFile
    
    # Normalize ID to handle both int and string inputs
    normalized_id = normalize_id(candidate_id)
    if normalized_id is None:
        raise bad_request("INVALID_CANDIDATE_ID", f"Invalid candidate ID: {candidate_id}")
    
    # Get latest active resume
    result = await db.execute(
        select(ResumeFile).where(
            ResumeFile.candidate_id == normalized_id,
            ResumeFile.is_active == True,
            ResumeFile.deleted_at.is_(None)
        ).order_by(ResumeFile.version.desc()).limit(1)
    )
    latest_resume = result.scalar_one_or_none()
    
    if not latest_resume:
        raise not_found("CANDIDATE_NO_ACTIVE_RESUME")
    
    # Redirect to specific file download
    return await download_resume_file(candidate_id, latest_resume.id, db, current_user)


@router.post("/{candidate_id}/resume", response_model=ResumeUploadResponse)
async def upload_resume(
    background_tasks: BackgroundTasks,
    candidate_id: Union[int, str] = Path(..., description="候选人ID"),
    db: AsyncSession = Depends(get_db),
    file: UploadFile = File(...),
    current_user: User = Depends(check_permission(Permission.CANDIDATES_WRITE))
) -> Any:
    """
    Upload resume file for candidate
    """
    # Normalize ID to handle both int and string inputs
    normalized_id = normalize_id(candidate_id)
    if normalized_id is None:
        raise bad_request("INVALID_CANDIDATE_ID", f"Invalid candidate ID: {candidate_id}")
        
    # Check if candidate exists and user has permission
    candidate = await candidate_crud.get_with_permission(
        db,
        id=normalized_id,
        user_id=current_user.id,
        user_role=current_user.role
    )
    
    if not candidate:
        raise not_found("CANDIDATE_NOT_FOUND_OR_NO_ACCESS")
    
    # Check update permission
    if not candidate_crud._can_update(candidate, current_user.id):
        raise forbidden("CANDIDATE_RESUME_UPLOAD_FORBIDDEN")
    
    try:
        # Upload file to MinIO
        upload_result = await file_service.upload_resume(
            file,
            current_user.id,
            normalized_id
        )
        
        # Update candidate with resume URL
        candidate.resume_url = upload_result["presigned_url"]
        db.add(candidate)
        await db.commit()
        
        # Schedule resume parsing (async)
        background_tasks.add_task(
            parse_resume_background,
            normalized_id,
            upload_result["object_name"],
            file.content_type
        )
        
        logger.info(f"Uploaded resume for candidate {candidate_id}")
        
        return ResumeUploadResponse(
            resume_url=upload_result["presigned_url"],
            file_size=upload_result["file_size"],
            content_type=upload_result["content_type"],
            upload_time=upload_result["upload_time"],
            parsing_status="pending"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Resume upload failed: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={"error_code": "CANDIDATE_RESUME_UPLOAD_FAILED", "details": {"error": str(e)}}
        )


@router.post("/bulk-import")
async def bulk_import_candidates(
    *,
    db: AsyncSession = Depends(get_db),
    file: UploadFile = File(...),
    skip_duplicates: bool = Query(True, description="跳过重复记录"),
    update_existing: bool = Query(False, description="更新已存在记录"),
    current_user: User = Depends(check_permission(Permission.CANDIDATES_WRITE))
) -> Any:
    """
    Bulk import candidates from CSV/Excel file
    
    **Supported formats:** CSV, Excel (XLSX, XLS)
    **Maximum file size:** 10MB
    **Maximum records:** 1000 per file
    
    **CSV Format Example:**
    ```
    name,email,phone,current_position,current_company,skills
    John Doe,<EMAIL>,+1234567890,Software Engineer,Tech Corp,"Python,Java,React"
    ```
    """
    import pandas as pd
    import io
    from app.services.vector_service import VectorService
    
    # Validate file type
    if not file.filename or not file.filename.lower().endswith(('.csv', '.xlsx', '.xls')):
        raise bad_request("FILE_FORMAT_INVALID_CSV_EXCEL")
    
    # Validate file size (10MB limit)
    if file.size and file.size > 10 * 1024 * 1024:
        raise bad_request("FILE_TOO_LARGE", "File size must be less than 10MB")
    
    try:
        # Read file content
        content = await file.read()
        
        # Parse file based on type
        if file.filename.lower().endswith('.csv'):
            df = pd.read_csv(io.BytesIO(content))
        else:
            df = pd.read_excel(io.BytesIO(content))
        
        # Validate maximum records
        if len(df) > 1000:
            raise bad_request("TOO_MANY_RECORDS", "Maximum 1000 records per import")
        
        # Process import results
        created = 0
        skipped = 0
        updated = 0
        errors = []
        created_ids = []
        
        # Field mapping (CSV column -> database field)
        field_mapping = {
            'name': 'name',
            '姓名': 'name',
            'email': 'email',
            '邮箱': 'email',
            'phone': 'phone',
            '电话': 'phone',
            'current_position': 'current_position',
            '当前职位': 'current_position',
            'current_company': 'current_company',
            '当前公司': 'current_company',
            'years_of_experience': 'years_of_experience',
            '工作年限': 'years_of_experience',
            'skills': 'skills',
            '技能': 'skills',
            'expected_salary': 'expected_salary',
            '期望薪资': 'expected_salary',
            'current_salary': 'current_salary',
            '当前薪资': 'current_salary'
        }
        
        # Process each row
        for index, row in df.iterrows():
            try:
                # Map fields
                candidate_data = {}
                for csv_field, db_field in field_mapping.items():
                    if csv_field in row and pd.notna(row[csv_field]):
                        value = row[csv_field]
                        
                        # Handle skills as list
                        if db_field == 'skills' and isinstance(value, str):
                            value = [s.strip() for s in value.split(',')]
                        
                        # Handle numeric fields
                        if db_field in ['years_of_experience', 'expected_salary', 'current_salary']:
                            try:
                                value = int(value)
                            except:
                                continue
                        
                        candidate_data[db_field] = value
                
                # Ensure required fields
                if 'name' not in candidate_data:
                    errors.append({
                        'row': index + 2,  # +2 for header and 0-index
                        'error': 'Name is required'
                    })
                    continue
                
                # Check for duplicates
                if skip_duplicates and 'email' in candidate_data:
                    existing = await candidate_crud.check_duplicate(
                        db,
                        email=candidate_data.get('email'),
                        phone=candidate_data.get('phone')
                    )
                    
                    if existing:
                        if update_existing:
                            # Update existing candidate
                            for key, value in candidate_data.items():
                                if hasattr(existing, key):
                                    setattr(existing, key, value)
                            existing.updated_at = datetime.now(timezone.utc)
                            await db.commit()
                            updated += 1
                        else:
                            skipped += 1
                        continue
                
                # Create candidate
                candidate_in = CandidateCreate(**candidate_data)
                candidate = await candidate_crud.create_with_permission(
                    db,
                    obj_in=candidate_in,
                    created_by=current_user.id
                )
                created += 1
                created_ids.append(candidate.id)
                
            except Exception as e:
                errors.append({
                    'row': index + 2,
                    'error': str(e)
                })
        
        # Trigger batch vectorization for created candidates
        if created_ids:
            try:
                vector_service = VectorService()
                asyncio.create_task(
                    vector_service.batch_vectorize(db, created_ids)
                )
            except Exception as e:
                logger.warning(f"Failed to trigger batch vectorization: {e}")
        
        logger.info(f"Bulk import completed: created={created}, updated={updated}, skipped={skipped}, errors={len(errors)}")
        
        return {
            "success": True,
            "created": created,
            "updated": updated,
            "skipped": skipped,
            "errors": errors[:10],  # Return max 10 errors
            "total": len(df),
            "file_name": file.filename
        }
        
    except pd.errors.EmptyDataError:
        raise bad_request("FILE_EMPTY", "The uploaded file is empty")
    except Exception as e:
        logger.error(f"Bulk import failed: {str(e)}")
        raise service_error("BULK_IMPORT_FAILED", f"Failed to process import: {str(e)}")


@router.post("/export")
async def export_candidates(
    candidate_ids: List[Union[int, str]],
    format: str = Query("excel", pattern="^(excel|csv)$"),
    include_resume_text: bool = Query(False, description="包含简历文本"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.CANDIDATES_EXPORT))
) -> Any:
    """
    Export candidates data
    """
    # Validate candidate IDs and permissions
    accessible_candidates = []
    for candidate_id in candidate_ids:
        # Normalize ID to handle both int and string inputs
        normalized_id = normalize_id(candidate_id)
        if normalized_id is None:
            logger.warning(f"Invalid candidate ID in export: {candidate_id}")
            continue
            
        candidate = await candidate_crud.get_with_permission(
            db,
            id=normalized_id,
            user_id=current_user.id,
            user_role=current_user.role
        )
        if candidate:
            accessible_candidates.append(candidate)
    
    if not accessible_candidates:
        raise not_found("CANDIDATE_NO_DATA_TO_EXPORT")
    
    # TODO: Implement export logic
    # This would involve:
    # 1. Generate Excel/CSV file
    # 2. Upload to MinIO with temporary access
    # 3. Return download URL
    
    return {
        "message": "FEATURE_EXPORT_UNDER_DEVELOPMENT",
        "candidate_count": len(accessible_candidates),
        "format": format,
        "download_url": None
    }


async def parse_resume_background(
    candidate_id: int,
    object_name: str,
    content_type: str
):
    """Background task for resume parsing"""
    try:
        from app.core.database import AsyncSessionLocal
        
        # Download file from MinIO
        file_content = file_service.download_file(
            file_service.resume_bucket,
            object_name
        )
        
        # Choose parser based on content type
        if content_type == "application/pdf":
            parser = pdf_parser
        elif content_type in ["application/vnd.openxmlformats-officedocument.wordprocessingml.document", "application/msword"]:
            parser = docx_parser
        else:
            logger.warning(f"Unsupported content type for parsing: {content_type}")
            return
        
        # Parse resume
        parsed_result = await parser.parse(file_content, object_name)
        
        # Update candidate with parsed data
        async with AsyncSessionLocal() as db:
            candidate = await candidate_crud.get(db, id=candidate_id)
            if candidate:
                candidate.resume_text = parsed_result.raw_text
                candidate.resume_parsed_at = parsed_result.parsed_at
                
                # Update extracted information if not already present
                if not candidate.email and parsed_result.email:
                    candidate.email = parsed_result.email
                if not candidate.phone and parsed_result.phone:
                    candidate.phone = parsed_result.phone
                if not candidate.skills and parsed_result.skills:
                    candidate.skills = parsed_result.skills
                if not candidate.education and parsed_result.education:
                    candidate.education = parsed_result.education
                if not candidate.work_experience and parsed_result.work_experience:
                    candidate.work_experience = parsed_result.work_experience
                if not candidate.years_of_experience and parsed_result.years_of_experience:
                    candidate.years_of_experience = parsed_result.years_of_experience
                
                db.add(candidate)
                await db.commit()
                
                logger.info(f"Resume parsing completed for candidate {candidate_id}, confidence: {parsed_result.confidence_score}")
            else:
                logger.error(f"Candidate {candidate_id} not found during resume parsing")
                
    except Exception as e:
        logger.error(f"Resume parsing failed for candidate {candidate_id}: {str(e)}")


@router.post("/search/vector")
async def search_candidates_by_vector(
    *,
    db: AsyncSession = Depends(get_db),
    query: str = Query(..., description="Search query text"),
    limit: int = Query(20, ge=1, le=100, description="Maximum results"),
    threshold: float = Query(0.6, ge=0.0, le=1.0, description="Similarity threshold"),
    current_user: User = Depends(check_permission(Permission.CANDIDATES_READ))
) -> Any:
    """
    Search candidates using vector similarity
    
    Uses embeddings to find semantically similar candidates.
    Better for finding candidates with similar skills/experience even if keywords don't match exactly.
    
    **Parameters:**
    - `query`: Natural language search query
    - `limit`: Maximum number of results
    - `threshold`: Minimum similarity score (0-1)
    
    **Returns:**
    - List of candidates with similarity scores
    """
    from app.services.vector_service import VectorService
    
    try:
        vector_service = VectorService()
        
        # Perform vector search
        results = await vector_service.search_similar_candidates(
            db,
            query=query,
            limit=limit,
            threshold=threshold,
            user_id=current_user.id
        )
        
        # Format response
        candidates_with_scores = []
        for candidate, score in results:
            # Check permissions
            if candidate.created_by == current_user.id or candidate.data_permission in ["PUBLIC", "TEAM"]:
                candidate_dict = CandidateResponse.model_validate(candidate).model_dump()
                candidate_dict["similarity_score"] = round(score * 100, 2)
                candidates_with_scores.append(candidate_dict)
        
        logger.info(f"Vector search returned {len(candidates_with_scores)} candidates for query: {query[:50]}...")
        
        return {
            "items": candidates_with_scores,
            "total": len(candidates_with_scores),
            "query": query,
            "search_type": "vector",
            "threshold": threshold
        }
        
    except Exception as e:
        logger.error(f"Vector search failed: {str(e)}")
        # Fallback to empty results instead of error
        return {
            "items": [],
            "total": 0,
            "query": query,
            "search_type": "vector",
            "error": "Vector search temporarily unavailable"
        }



