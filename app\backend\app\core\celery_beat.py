"""
Celery Beat schedule configuration for TalentForge Pro
"""
from celery.schedules import crontab
from app.worker import celery_app

# Configure Celery Beat schedule
celery_app.conf.beat_schedule = {
    # Monitoring cache refresh - every 5 minutes
    'refresh-monitoring-cache': {
        'task': 'app.tasks.refresh_monitoring_cache',
        'schedule': crontab(minute='*/5'),  # Every 5 minutes
        'options': {
            'queue': 'monitoring',
            'priority': 10,  # Highest priority for monitoring
            'expires': 300   # Task expires in 5 minutes if not executed
        }
    },
    
    # Cleanup old monitoring records - daily at 2 AM
    'cleanup-monitoring-records': {
        'task': 'app.tasks.cleanup_old_monitoring_records',
        'schedule': crontab(hour=2, minute=0),  # Daily at 2:00 AM
        'options': {
            'queue': 'general',
            'priority': 5
        }
    },
    
    # System health snapshot - every hour for long-term tracking
    'hourly-health-snapshot': {
        'task': 'app.tasks.create_health_snapshot',
        'schedule': crontab(minute=0),  # Every hour at minute 0
        'options': {
            'queue': 'monitoring',
            'priority': 8
        }
    }
}

# Set timezone for scheduled tasks
celery_app.conf.timezone = 'UTC'
celery_app.conf.enable_utc = True

# Beat specific settings
celery_app.conf.beat_max_loop_interval = 5  # Check for new tasks every 5 seconds
celery_app.conf.beat_schedule_filename = 'celerybeat-schedule'

print("📅 Celery Beat schedule configured:")
print("  - Monitoring cache refresh: every 5 minutes")
print("  - Cleanup old records: daily at 2:00 AM")
print("  - Hourly health snapshot: every hour")