"""
Global exception handlers for TalentForge Pro backend
"""
import logging
from typing import Any, Dict
from fastapi import Request, status
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from sqlalchemy.exc import IntegrityError, DataError
from pydantic import ValidationError as PydanticValidationError
from datetime import datetime, timezone

from app.core.exceptions import (
    ServiceError,
    NotFoundError,
    PermissionError,
    ValidationError as ServiceValidationError,
    DuplicateError,
    ConflictError,
    AuthenticationError,
    TokenError,
    RateLimitError,
    ExternalServiceError,
)

logger = logging.getLogger(__name__)


async def service_exception_handler(request: Request, exc: ServiceError) -> JSONResponse:
    """
    Handle all ServiceError exceptions
    """
    logger.error(f"Service error on {request.url.path}: {exc.message}")
    
    # Map ServiceError types to HTTP status codes
    status_code_map = {
        NotFoundError: status.HTTP_404_NOT_FOUND,
        PermissionError: status.HTTP_403_FORBIDDEN,
        ServiceValidationError: status.HTTP_422_UNPROCESSABLE_ENTITY,
        DuplicateError: status.HTTP_409_CONFLICT,
        ConflictError: status.HTTP_409_CONFLICT,
        AuthenticationError: status.HTTP_401_UNAUTHORIZED,
        TokenError: status.HTTP_401_UNAUTHORIZED,
        RateLimitError: status.HTTP_429_TOO_MANY_REQUESTS,
        ExternalServiceError: status.HTTP_503_SERVICE_UNAVAILABLE,
    }
    
    status_code = status_code_map.get(type(exc), status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    # Create standardized error response
    error_response = {
        "error_code": exc.code,
        "detail": exc.message,
        "details": exc.details,
        "timestamp": datetime.now(timezone.utc).isoformat()
    }
    
    # Add WWW-Authenticate header for 401s
    headers = {}
    if status_code == status.HTTP_401_UNAUTHORIZED:
        headers["WWW-Authenticate"] = "Bearer"
    
    return JSONResponse(
        status_code=status_code,
        content=error_response,
        headers=headers
    )


async def validation_exception_handler(request: Request, exc: RequestValidationError) -> JSONResponse:
    """
    Handle FastAPI validation errors (422 Unprocessable Entity)
    """
    logger.error(f"Validation error on {request.url.path}: {exc.errors()}")
    
    # Transform validation errors into user-friendly format
    error_details = {}
    for error in exc.errors():
        field_path = " -> ".join([str(loc) for loc in error["loc"] if str(loc) != "body"])
        error_details[field_path] = {
            "message": error["msg"],
            "type": error["type"],
            "input": error.get("input")
        }
    
    error_response = {
        "error_code": "REQUEST_VALIDATION_FAILED",
        "detail": "Request validation failed",
        "details": {
            "validation_errors": error_details,
            "error_count": len(exc.errors())
        },
        "timestamp": datetime.now(timezone.utc).isoformat()
    }
    
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content=error_response
    )


async def pydantic_validation_exception_handler(request: Request, exc: PydanticValidationError) -> JSONResponse:
    """
    Handle Pydantic validation errors
    """
    logger.error(f"Pydantic validation error on {request.url.path}: {exc.errors()}")
    
    # Transform Pydantic errors similar to FastAPI validation errors
    error_details = {}
    for error in exc.errors():
        field_path = " -> ".join([str(loc) for loc in error["loc"]])
        error_details[field_path] = {
            "message": error["msg"],
            "type": error["type"],
            "input": error.get("input")
        }
    
    error_response = {
        "error_code": "DATA_VALIDATION_FAILED",
        "detail": "Data validation failed",
        "details": {
            "validation_errors": error_details,
            "error_count": len(exc.errors())
        },
        "timestamp": datetime.now(timezone.utc).isoformat()
    }
    
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content=error_response
    )


async def sqlalchemy_integrity_exception_handler(request: Request, exc: IntegrityError) -> JSONResponse:
    """
    Handle SQLAlchemy integrity constraint errors
    """
    logger.error(f"Database integrity error on {request.url.path}: {str(exc.orig)}")
    
    # Parse common constraint violations
    error_message = str(exc.orig)
    error_code = "DATABASE_INTEGRITY_ERROR"
    
    # Common integrity constraint patterns
    if "UNIQUE constraint failed" in error_message or "duplicate key value" in error_message:
        error_code = "DUPLICATE_RECORD_ERROR"
        detail = "A record with this information already exists"
    elif "NOT NULL constraint failed" in error_message:
        error_code = "REQUIRED_FIELD_MISSING"
        detail = "Required field is missing"
    elif "FOREIGN KEY constraint failed" in error_message:
        error_code = "INVALID_REFERENCE"
        detail = "Referenced record does not exist"
    else:
        detail = "Database constraint violation"
    
    error_response = {
        "error_code": error_code,
        "detail": detail,
        "details": {
            "constraint_error": True,
            "database_error": error_message
        },
        "timestamp": datetime.now(timezone.utc).isoformat()
    }
    
    return JSONResponse(
        status_code=status.HTTP_409_CONFLICT,
        content=error_response
    )


async def sqlalchemy_data_exception_handler(request: Request, exc: DataError) -> JSONResponse:
    """
    Handle SQLAlchemy data errors (invalid data format, etc.)
    """
    logger.error(f"Database data error on {request.url.path}: {str(exc.orig)}")
    
    error_response = {
        "error_code": "INVALID_DATA_FORMAT",
        "detail": "Invalid data format for database operation",
        "details": {
            "data_error": True,
            "database_error": str(exc.orig)
        },
        "timestamp": datetime.now(timezone.utc).isoformat()
    }
    
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content=error_response
    )


async def generic_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """
    Handle all other exceptions
    """
    logger.exception(f"Unhandled exception on {request.url.path}: {type(exc).__name__}: {str(exc)}")
    
    # Don't expose internal error details in production
    error_response = {
        "error_code": "INTERNAL_SERVER_ERROR",
        "detail": "An unexpected error occurred",
        "details": {
            "exception_type": type(exc).__name__,
            # Only include exception message in development
            "exception_message": str(exc) if logger.isEnabledFor(logging.DEBUG) else None
        },
        "timestamp": datetime.now(timezone.utc).isoformat()
    }
    
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content=error_response
    )


def register_exception_handlers(app: Any) -> None:
    """
    Register all exception handlers with the FastAPI app
    """
    # Service layer exceptions
    app.add_exception_handler(ServiceError, service_exception_handler)
    
    # Validation exceptions
    app.add_exception_handler(RequestValidationError, validation_exception_handler)
    app.add_exception_handler(PydanticValidationError, pydantic_validation_exception_handler)
    
    # Database exceptions
    app.add_exception_handler(IntegrityError, sqlalchemy_integrity_exception_handler)
    app.add_exception_handler(DataError, sqlalchemy_data_exception_handler)
    
    # Generic fallback for all other exceptions
    app.add_exception_handler(Exception, generic_exception_handler)
    
    logger.info("Exception handlers registered successfully")