"""
Monitoring System Configuration
"""
from pydantic_settings import BaseSettings


class MonitoringSettings(BaseSettings):
    """Settings for monitoring system"""
    
    # Refresh intervals (in seconds)
    METRICS_CACHE_TTL: int = 30  # System metrics cache TTL, aligned with frontend refresh
    HEALTH_CHECK_INTERVAL: int = 120  # Health check interval for services
    CONTAINER_STATS_CACHE_TTL: int = 10  # Container stats cache TTL
    
    # Data retention (in days)
    HEALTH_CHECK_RETENTION: int = 7  # Keep health check records for 7 days
    METRICS_RETENTION: int = 7  # Keep raw metrics for 7 days
    HOURLY_AGGREGATES_RETENTION: int = 30  # Keep hourly aggregates for 30 days
    DAILY_AGGREGATES_RETENTION: int = 365  # Keep daily aggregates for 1 year
    
    # Performance settings
    MAX_CONCURRENT_HEALTH_CHECKS: int = 10  # Maximum concurrent health checks
    HEALTH_CHECK_TIMEOUT: int = 10  # Health check timeout in seconds
    DOCKER_API_TIMEOUT: int = 5  # Docker API timeout in seconds
    
    # Frontend settings (for reference)
    FRONTEND_REFRESH_INTERVAL: int = 30  # Frontend auto-refresh interval in seconds
    
    # Alert thresholds
    CPU_ALERT_THRESHOLD: float = 80.0  # CPU usage alert threshold (%)
    MEMORY_ALERT_THRESHOLD: float = 85.0  # Memory usage alert threshold (%)
    DISK_ALERT_THRESHOLD: float = 90.0  # Disk usage alert threshold (%)
    
    class Config:
        env_prefix = "MONITORING_"
        case_sensitive = False


# Create settings instance
monitoring_settings = MonitoringSettings()