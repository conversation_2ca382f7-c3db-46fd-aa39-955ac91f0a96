name: "LLM-Driven Intelligent Questionnaire Assessment System"
description: |
  Transform the existing questionnaire system into an AI-powered talent assessment platform 
  specifically designed for the tobacco industry, leveraging DeepSeek R1 for intelligent 
  question generation and evaluation.

## 📋 PRP Enhancement Summary (Based on SRS Requirements)

### Added to Meet SRS Requirements:
1. **Task 4.5**: Complete publishing workflow (FR-PUB-001-003)
   - Status workflow: draft → reviewing → published
   - Unique slug generation for public links
   - Email notification system
   - Approval process with comments

2. **Task 5 Expansion**: Full editing capabilities (FR-EDIT-001-004)
   - Question text, options, and weight editing
   - Add/delete questions with drag-drop reordering
   - Conditional logic configuration
   - Version management with history

3. **Task 8 Expansion**: Statistics and export (FR-DASH-003-004)
   - Statistical analysis (mean, median, std deviation)
   - Score distribution histogram
   - Excel and PDF export functionality

4. **Task 10**: Security implementation (NFR-SEC-001-003)
   - Role-based access control (RBAC)
   - Audit logging for all operations
   - SQL injection prevention
   - Session timeout management

5. **Database Enhancements**:
   - Publishing fields (slug, valid_until, access_type)
   - Review tracking fields (review_comments, rejection_reason)
   - Additional indexes for performance

6. **Timeline Adjustment**: 7 days → 8-9 days to accommodate all SRS requirements

## Goal
Build a complete LLM-driven questionnaire assessment system that enables HR managers to:
1. Generate industry-specific assessment questionnaires using AI
2. Dynamically render questionnaires with scoring logic
3. Intelligently evaluate candidate responses with multi-dimensional analysis
4. Provide comprehensive analytics dashboard for talent screening

## Why
- **Business Value**: Transform manual assessment creation into AI-powered generation, reducing creation time from hours to minutes
- **User Impact**: HR managers can create sophisticated, industry-specific assessments without assessment design expertise
- **Integration**: Enhances existing TalentForge Pro platform with advanced AI capabilities
- **Problems Solved**: 
  - Manual questionnaire creation is time-consuming and inconsistent
  - Lack of tobacco industry-specific assessment templates
  - Subjective scoring leads to bias in candidate evaluation
  - No systematic way to analyze assessment results across candidates

## What
### User-Visible Behavior
1. **HR Manager Flow**:
   - Input assessment requirements (position type, skills to evaluate)
   - AI generates complete questionnaire with scoring criteria
   - Review and edit generated questions
   - Publish assessment for candidates

2. **Candidate Flow**:
   - Access public assessment link
   - Complete dynamic questionnaire with conditional logic
   - Submit responses for automatic evaluation

3. **Analytics Flow**:
   - View candidate comparison dashboard
   - Filter candidates by score thresholds
   - Analyze multi-dimensional competency radar charts

### Technical Requirements
- Integration with DeepSeek R1 API for generation/evaluation
- Dynamic form rendering based on JSON structure
- Real-time scoring calculation
- Responsive analytics visualization

### Success Criteria
- [ ] AI can generate tobacco industry-specific questionnaires in <30 seconds
- [ ] Generated questionnaires include proper scoring logic for each option
- [ ] Dynamic rendering handles all 8 existing question types
- [ ] Evaluation provides dimensional scores with explanatory text
- [ ] Dashboard allows filtering candidates by multiple criteria
- [ ] System handles 100+ concurrent assessments without performance degradation

## All Needed Context

### Documentation & References
```yaml
# MUST READ - Include these in your context window
- file: /home/<USER>/source_code/talent_forge_pro/app/backend/app/models/questionnaire.py
  why: Existing questionnaire model with 8 question types, scoring system
  
- file: /home/<USER>/source_code/talent_forge_pro/app/backend/app/crud/questionnaire.py
  why: CRUD operations including scoring calculation logic
  
- file: /home/<USER>/source_code/talent_forge_pro/app/frontend/components/survey-builder/
  why: Existing survey builder components to extend for AI generation
  
- file: /home/<USER>/source_code/talent_forge_pro/CLAUDE.md
  why: Project coding standards and conventions

- doc: https://platform.deepseek.com/api-docs
  section: Chat Completions API
  critical: Use streaming for better UX, handle rate limits properly

- file: /home/<USER>/source_code/talent_forge_pro/app/backend/app/core/config.py
  why: Add DeepSeek API configuration here
```

### Current Codebase Structure
```bash
app/
├── backend/
│   ├── app/
│   │   ├── models/
│   │   │   ├── questionnaire.py         # Existing questionnaire model
│   │   │   └── application_submission.py # Response storage
│   │   ├── schemas/
│   │   │   └── questionnaire.py         # Pydantic schemas
│   │   ├── crud/
│   │   │   └── questionnaire.py         # CRUD with scoring
│   │   ├── api/v1/
│   │   │   └── questionnaires.py        # REST endpoints
│   │   └── services/                    # [NEW] Service layer needed
│   └── tests/
└── frontend/
    ├── components/
    │   ├── survey-builder/               # Existing builder
    │   └── public/                       # Public questionnaire
    └── app/
        └── (dashboard)/
            └── questionnaires/           # Management UI
```

### Desired Codebase Structure
```bash
app/
├── backend/
│   ├── app/
│   │   ├── models/
│   │   │   ├── questionnaire_template.py    # [NEW] AI template storage
│   │   │   └── evaluation_report.py         # [NEW] Assessment results
│   │   ├── schemas/
│   │   │   ├── llm_generation.py           # [NEW] LLM request/response
│   │   │   └── evaluation.py               # [NEW] Evaluation schemas
│   │   ├── services/
│   │   │   ├── llm_service.py              # [NEW] DeepSeek integration
│   │   │   ├── generation_service.py       # [NEW] Questionnaire generation
│   │   │   └── evaluation_service.py       # [NEW] Response evaluation
│   │   └── api/v1/
│   │       ├── ai_questionnaires.py        # [NEW] AI endpoints
│   │       └── analytics.py                # [NEW] Analytics endpoints
└── frontend/
    ├── components/
    │   ├── ai-generation/                   # [NEW] AI generation UI
    │   │   ├── GenerationWizard.tsx
    │   │   └── TemplateSelector.tsx
    │   ├── analytics/                       # [NEW] Analytics components
    │   │   ├── CandidateComparison.tsx
    │   │   ├── DimensionRadarChart.tsx
    │   │   └── ScoreDistribution.tsx
    │   └── dynamic-renderer/                # [NEW] Dynamic form
    │       └── DynamicQuestionRenderer.tsx
    └── app/
        └── (dashboard)/
            └── analytics/                    # [NEW] Analytics pages
```

### Known Gotchas & Critical Information
```python
# CRITICAL: Project uses Snowflake IDs, not UUIDs
# Always use: from app.core.id_generator import generate_id

# CRITICAL: DeepSeek API rate limits
# R1 model: 10 requests/minute for generation
# Implement exponential backoff and queuing

# CRITICAL: Existing scoring system in questionnaire.py
# scoring_method: 'sum' | 'average' | 'weighted' | 'custom'
# Each question has scoring_config JSONB field

# CRITICAL: Frontend uses shadcn/ui components
# Don't mix with other UI libraries

# CRITICAL: API responses must include error_code field
# Format: [MODULE]_[CATEGORY]_[ERROR]

# CRITICAL: Docker environment requires dependency refresh
# After modifying package.json: make frontend-package-refresh
# After modifying pyproject.toml: make backend-package-refresh
```

## Implementation Blueprint

### Data Models and Structure

```python
# app/backend/app/models/questionnaire_template.py
from sqlalchemy import Column, String, JSON, Boolean, Integer, ForeignKey, TIMESTAMP
from app.models.base import BaseModel

class QuestionnaireTemplate(BaseModel):
    __tablename__ = "questionnaire_templates"
    
    name = Column(String(200), nullable=False)
    industry = Column(String(50), default='烟草')
    position_type = Column(String(100))
    ai_generated = Column(Boolean, default=True)
    generation_prompt = Column(Text)  # Store the prompt used
    evaluation_criteria = Column(JSON)  # Scoring dimensions and rules
    status = Column(String(20))  # draft/reviewing/published/archived
    version = Column(Integer, default=1)
    
    # Publishing fields
    slug = Column(String(200), unique=True, nullable=True)  # Unique public URL
    valid_until = Column(TIMESTAMP(timezone=True))  # Expiry date
    access_type = Column(String(20))  # public/invitation
    max_submissions = Column(Integer, nullable=True)
    
    # Review fields
    review_comments = Column(Text)  # Review feedback
    rejection_reason = Column(Text)  # If rejected
    
    # Tobacco-specific dimensions
    dimensions = Column(JSON)  # ["工艺流程理解", "质量控制意识", ...]
    
    # Relations
    created_by_id = Column(BigInteger, ForeignKey("users.id"))
    reviewed_by_id = Column(BigInteger, ForeignKey("users.id"))
    published_by_id = Column(BigInteger, ForeignKey("users.id"))
    published_at = Column(TIMESTAMP(timezone=True))

# app/backend/app/models/evaluation_report.py  
class EvaluationReport(BaseModel):
    __tablename__ = "evaluation_reports"
    
    questionnaire_id = Column(BigInteger, ForeignKey("questionnaires.id"))
    candidate_id = Column(BigInteger, ForeignKey("candidates.id"))
    
    # Multi-dimensional scores
    dimension_scores = Column(JSON)  # {"工艺流程理解": 85, "质量控制意识": 92, ...}
    total_score = Column(Float)
    
    # AI evaluation
    ai_evaluation = Column(Text)  # AI generated evaluation text
    recommendations = Column(Text)  # Improvement suggestions
    is_qualified = Column(Boolean)  # Pass/fail based on threshold
    
    evaluated_at = Column(TIMESTAMP(timezone=True))
```

### List of Tasks

```yaml
Task 1: Configure DeepSeek API Integration
MODIFY app/backend/app/core/config.py:
  - ADD DeepSeek API configuration fields
  - PATTERN: Follow existing OpenAI config pattern
  - ADD: DEEPSEEK_API_KEY, DEEPSEEK_API_BASE, DEEPSEEK_MODEL

CREATE app/backend/app/services/llm_service.py:
  - PATTERN: Follow app/backend/app/services/auth_service.py structure
  - IMPLEMENT: DeepSeek API client with retry logic
  - ADD: Rate limiting with 10 req/min for R1 model

Task 2: Database Models and Migrations
CREATE app/backend/app/models/questionnaire_template.py:
  - PATTERN: Follow app/backend/app/models/questionnaire.py
  - USE: Snowflake ID from app.core.id_generator
  - ADD: All fields as specified in data models section

CREATE alembic migration:
  - RUN: make db-migrate-create name=add_ai_questionnaire_tables
  - ADD: questionnaire_templates and evaluation_reports tables
  - RUN: make db-migrate

Task 3: Generation Service Implementation
CREATE app/backend/app/services/generation_service.py:
  - IMPLEMENT: Tobacco industry templates (5 types)
  - PATTERN: Async service pattern from existing services
  - ADD: Prompt engineering for questionnaire generation
  - RETURN: Structured JSON matching questionnaire schema

Task 4: API Endpoints for AI Generation
CREATE app/backend/app/api/v1/ai_questionnaires.py:
  - PATTERN: Follow app/backend/app/api/v1/questionnaires.py
  - ADD: POST /api/v1/ai-questionnaires/generate
  - ADD: POST /api/v1/ai-questionnaires/evaluate
  - ADD: GET /api/v1/ai-questionnaires/templates
  - USE: Dependency injection for services

Task 4.5: Publishing Workflow API (FR-PUB Requirements)
CREATE app/backend/app/api/v1/questionnaire_publishing.py:
  - ADD: POST /api/v1/ai-questionnaires/{id}/submit-review
  - ADD: POST /api/v1/ai-questionnaires/{id}/approve
  - ADD: POST /api/v1/ai-questionnaires/{id}/publish
  - ADD: GET /api/v1/public/questionnaires/{slug}
  
CREATE app/backend/app/services/publishing_service.py:
  - IMPLEMENT: Status workflow: draft → reviewing → published → archived
  - ADD: Unique slug generation for public access
  - ADD: Notification service for email alerts
  - ADD: Approval tracking with comments

Task 5: Frontend AI Generation Wizard & Complete Editing (FR-EDIT Requirements)
CREATE app/frontend/components/ai-generation/GenerationWizard.tsx:
  - USE: shadcn/ui components only
  - PATTERN: Multi-step form with progress indicator
  - ADD: Industry template selection
  - ADD: Position and dimension configuration
  - ADD: Real-time generation with loading state
  - ADD: Preview mode for generated questionnaire

CREATE app/frontend/components/ai-generation/QuestionEditor.tsx:
  - IMPLEMENT: Question text, options, and weight editing (FR-EDIT-001)
  - ADD: Add/delete questions functionality (FR-EDIT-002)
  - ADD: Drag-and-drop reordering using react-beautiful-dnd (FR-EDIT-002)
  - ADD: Conditional logic configuration UI (FR-EDIT-003)
  - ADD: Version management with history tracking (FR-EDIT-004)
  - ADD: Auto-save every 5 seconds with debouncing
  - PATTERN: Split view - question list left, details right

Task 6: Dynamic Question Renderer
CREATE app/frontend/components/dynamic-renderer/DynamicQuestionRenderer.tsx:
  - SUPPORT: All 8 existing question types
  - ADD: Conditional logic based on answers
  - PATTERN: Use existing public questionnaire components
  - ADD: Score calculation on client side

Task 7: Evaluation Service
CREATE app/backend/app/services/evaluation_service.py:
  - IMPLEMENT: Multi-dimensional scoring logic
  - ADD: DeepSeek evaluation prompt construction
  - CALCULATE: Dimension scores from responses
  - GENERATE: Evaluation report with recommendations

Task 8: Analytics Dashboard with Statistics & Export (FR-DASH Requirements)
CREATE app/frontend/components/analytics/CandidateComparison.tsx:
  - USE: tanstack/react-table for data grid
  - ADD: Sortable columns for scores (FR-DASH-001)
  - ADD: Filter by score thresholds (FR-DASH-001)
  - ADD: 2-5 candidate comparison selection (FR-DASH-002)
  - PATTERN: Follow existing table components

CREATE app/frontend/components/analytics/DimensionRadarChart.tsx:
  - USE: recharts library (already in project)
  - DISPLAY: Multi-dimensional competency scores (FR-DASH-002)
  - ADD: Candidate comparison overlay with highlighting

CREATE app/frontend/components/analytics/StatisticsPanel.tsx:
  - ADD: Score distribution histogram (FR-DASH-003)
  - CALCULATE: Mean, median, standard deviation (FR-DASH-003)
  - DISPLAY: Pass rate percentage (FR-DASH-003)
  - USE: recharts for visualizations

CREATE app/backend/app/services/export_service.py:
  - IMPLEMENT: Excel export with xlsxwriter (FR-DASH-004)
  - IMPLEMENT: PDF report generation (FR-DASH-004)
  - ADD: Batch export for multiple candidates (FR-DASH-004)
  - PATTERN: Async service with progress tracking

Task 9: Testing and Validation
CREATE app/backend/tests/test_ai_generation.py:
  - TEST: Questionnaire generation with mock DeepSeek
  - TEST: Evaluation scoring logic
  - TEST: Rate limiting behavior

CREATE app/frontend/__tests__/ai-generation.test.tsx:
  - TEST: Generation wizard flow
  - TEST: Dynamic rendering of questions
  - TEST: Score calculation accuracy

Task 10: Security & Permission Implementation (NFR-SEC Requirements)
CREATE app/backend/app/core/security/rbac.py:
  - IMPLEMENT: Role-based access control (NFR-SEC-001)
  - DEFINE: Permission matrix for HR roles
  - ADD: Permission decorators for API endpoints
  - PATTERN: Follow existing auth_service patterns

CREATE app/backend/app/core/security/audit_logger.py:
  - IMPLEMENT: Audit logging for all operations (NFR-SEC-003)
  - LOG: User, timestamp, operation, result
  - RETENTION: 90 days as per requirement
  - STORAGE: PostgreSQL audit_logs table

MODIFY app/backend/app/schemas/auth.py:
  - ADD: Role field to JWT payload (NFR-SEC-001)
  - ADD: Permission list to user context
  - IMPLEMENT: Session timeout configuration

CREATE app/backend/app/middleware/security.py:
  - ADD: SQL injection prevention (NFR-SEC-002)
  - IMPLEMENT: Input sanitization
  - ADD: Rate limiting middleware
```

### Per Task Pseudocode

```python
# Task 3: Generation Service
async def generate_questionnaire(
    position_type: str,
    dimensions: List[str],
    question_count: int = 20
) -> QuestionnaireSchema:
    # Build prompt for DeepSeek
    prompt = f"""
    生成{position_type}岗位的人才测评问卷：
    评估维度：{', '.join(dimensions)}
    题目数量：{question_count}
    
    要求：
    1. 每道题包含4个选项，分值分别为5,3,1,0分
    2. 题目要结合烟草行业实际工作场景
    3. 返回JSON格式...
    """
    
    # Call DeepSeek with retry
    @retry(attempts=3, backoff=exponential)
    async def _generate():
        async with rate_limiter.acquire():  # 10 req/min limit
            response = await llm_service.complete(
                prompt=prompt,
                model="deepseek-reasoner"
            )
            return parse_llm_response(response)
    
    questionnaire_data = await _generate()
    
    # Save as template
    template = QuestionnaireTemplate(
        name=f"{position_type}能力测评",
        position_type=position_type,
        dimensions=dimensions,
        generation_prompt=prompt,
        evaluation_criteria=questionnaire_data['evaluation_criteria']
    )
    
    await db.save(template)
    return questionnaire_data

# Task 7: Evaluation Service  
async def evaluate_responses(
    questionnaire_id: int,
    candidate_id: int,
    responses: Dict[str, Any]
) -> EvaluationReport:
    # Get questionnaire and scoring config
    questionnaire = await questionnaire_crud.get(questionnaire_id)
    
    # Calculate dimension scores
    dimension_scores = {}
    for question in questionnaire.questions:
        dimension = question.dimension
        score = calculate_question_score(question, responses[question.id])
        dimension_scores[dimension] = dimension_scores.get(dimension, 0) + score
    
    # Normalize scores to 0-100
    for dimension in dimension_scores:
        dimension_scores[dimension] = (dimension_scores[dimension] / max_score) * 100
    
    # Build evaluation prompt
    eval_prompt = f"""
    基于以下测评结果，提供专业评价：
    候选人各维度得分：{json.dumps(dimension_scores, ensure_ascii=False)}
    岗位要求：{questionnaire.position_type}
    
    请提供：
    1. 能力评价（200字）
    2. 优势分析
    3. 改进建议
    4. 是否推荐录用
    """
    
    ai_evaluation = await llm_service.complete(eval_prompt)
    
    # Create report
    report = EvaluationReport(
        questionnaire_id=questionnaire_id,
        candidate_id=candidate_id,
        dimension_scores=dimension_scores,
        total_score=sum(dimension_scores.values()) / len(dimension_scores),
        ai_evaluation=ai_evaluation['evaluation'],
        recommendations=ai_evaluation['recommendations'],
        is_qualified=ai_evaluation['is_qualified']
    )
    
    await db.save(report)
    return report

# Task 4.5: Publishing Service
async def publish_questionnaire(
    questionnaire_id: int,
    settings: PublishSettings,
    user_id: int
) -> PublishResult:
    """发布问卷并生成公开链接"""
    questionnaire = await get_questionnaire(questionnaire_id)
    
    # 验证状态
    if questionnaire.status != "reviewing":
        raise ValueError("QUESTIONNAIRE_PUBLISH_INVALID_STATUS")
    
    # 生成唯一slug
    slug = generate_unique_slug(questionnaire.name)
    
    # 更新发布设置
    questionnaire.slug = slug
    questionnaire.valid_until = settings.valid_until
    questionnaire.access_type = settings.access_type
    questionnaire.status = "published"
    questionnaire.published_at = datetime.now(timezone.utc)
    questionnaire.published_by_id = user_id
    
    # 发送通知邮件
    if settings.notify_candidates:
        await send_notifications(
            template="questionnaire_invitation",
            recipients=settings.candidate_emails,
            context={
                "questionnaire_name": questionnaire.name,
                "link": f"{BASE_URL}/public/{slug}",
                "valid_until": settings.valid_until
            }
        )
    
    await db.save(questionnaire)
    return PublishResult(
        slug=slug,
        public_link=f"{BASE_URL}/public/{slug}"
    )

# Task 10: RBAC Security
from functools import wraps

class Permission(Enum):
    QUESTIONNAIRE_CREATE = "questionnaire:create"
    QUESTIONNAIRE_EDIT_OWN = "questionnaire:edit:own"
    QUESTIONNAIRE_EDIT_ALL = "questionnaire:edit:all"
    QUESTIONNAIRE_PUBLISH = "questionnaire:publish"
    QUESTIONNAIRE_REVIEW = "questionnaire:review"
    ANALYTICS_VIEW = "analytics:view"
    ANALYTICS_EXPORT = "analytics:export"

ROLE_PERMISSIONS = {
    "hr_specialist": [
        Permission.QUESTIONNAIRE_CREATE,
        Permission.QUESTIONNAIRE_EDIT_OWN,
        Permission.ANALYTICS_VIEW
    ],
    "hr_manager": [
        Permission.QUESTIONNAIRE_CREATE,
        Permission.QUESTIONNAIRE_EDIT_ALL,
        Permission.QUESTIONNAIRE_PUBLISH,
        Permission.QUESTIONNAIRE_REVIEW,
        Permission.ANALYTICS_VIEW,
        Permission.ANALYTICS_EXPORT
    ],
    "admin": ["*"]  # All permissions
}

def require_permission(permission: Permission):
    """权限检查装饰器"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            user = get_current_user()
            if not has_permission(user.role, permission):
                raise HTTPException(
                    status_code=403,
                    detail={
                        "error_code": "AUTH_PERMISSION_DENIED",
                        "message": f"Missing permission: {permission.value}"
                    }
                )
            
            # 审计日志
            await log_audit_event(
                user_id=user.id,
                operation=f"{func.__name__}",
                resource=permission.value,
                timestamp=datetime.now(timezone.utc)
            )
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator

# 使用示例
@router.post("/ai-questionnaires/{id}/publish")
@require_permission(Permission.QUESTIONNAIRE_PUBLISH)
async def publish_questionnaire(questionnaire_id: int):
    pass
```

### Integration Points
```yaml
DATABASE:
  - migration: "Add questionnaire_templates and evaluation_reports tables"
  - tables:
    - "questionnaire_templates with publishing fields"
    - "evaluation_reports with scoring"
    - "audit_logs for security tracking"
  - indexes: 
    - "CREATE INDEX idx_template_industry ON questionnaire_templates(industry, position_type)"
    - "CREATE INDEX idx_template_status ON questionnaire_templates(status)"
    - "CREATE INDEX idx_template_slug ON questionnaire_templates(slug)"
    - "CREATE INDEX idx_report_scores ON evaluation_reports(total_score DESC)"
    - "CREATE INDEX idx_report_candidate ON evaluation_reports(candidate_id, total_score DESC)"
    - "CREATE INDEX idx_audit_user_timestamp ON audit_logs(user_id, timestamp DESC)"
  
CONFIG:
  - add to: app/backend/app/core/config.py
  - pattern: |
      DEEPSEEK_API_KEY: str = Field(default="", env="DEEPSEEK_API_KEY")
      DEEPSEEK_API_BASE: str = Field(default="https://api.deepseek.com/v1", env="DEEPSEEK_API_BASE")
      DEEPSEEK_MODEL: str = Field(default="deepseek-reasoner", env="DEEPSEEK_MODEL")
  
ROUTES:
  - add to: app/backend/app/api/v1/__init__.py
  - pattern: |
      from app.api.v1 import ai_questionnaires
      api_router.include_router(ai_questionnaires.router, prefix="/ai-questionnaires", tags=["ai"])
  
FRONTEND_ROUTES:
  - add to: app/frontend/app/(dashboard)/layout.tsx
  - pattern: "Add navigation item for Analytics Dashboard"
```

## Validation Loop

### Level 1: Syntax & Style
```bash
# Backend Python checks
cd app/backend
ruff check app/services/llm_service.py --fix
ruff check app/services/generation_service.py --fix
mypy app/services/ --ignore-missing-imports

# Frontend TypeScript checks
cd app/frontend
pnpm type-check
pnpm lint

# Expected: No errors. Fix any issues before proceeding.
```

### Level 2: Unit Tests
```python
# app/backend/tests/test_generation_service.py
import pytest
from unittest.mock import Mock, patch
from app.services.generation_service import GenerationService

@pytest.mark.asyncio
async def test_generate_tobacco_questionnaire():
    """Test questionnaire generation for tobacco industry"""
    service = GenerationService()
    
    with patch('app.services.llm_service.complete') as mock_llm:
        mock_llm.return_value = {
            "questions": [...],
            "evaluation_criteria": {...}
        }
        
        result = await service.generate_questionnaire(
            position_type="烟草生产技术岗",
            dimensions=["工艺流程理解", "质量控制意识"],
            question_count=10
        )
        
        assert len(result.questions) == 10
        assert all(q.dimension in ["工艺流程理解", "质量控制意识"] for q in result.questions)

@pytest.mark.asyncio
async def test_rate_limiting():
    """Test that rate limiting prevents >10 req/min"""
    service = GenerationService()
    
    # Attempt 11 requests rapidly
    requests = []
    for _ in range(11):
        requests.append(service.generate_questionnaire(...))
    
    with pytest.raises(RateLimitExceeded):
        await asyncio.gather(*requests)

# app/frontend/__tests__/DynamicQuestionRenderer.test.tsx
import { render, fireEvent } from '@testing-library/react';
import { DynamicQuestionRenderer } from '@/components/dynamic-renderer';

describe('DynamicQuestionRenderer', () => {
  it('renders all 8 question types correctly', () => {
    const questions = [
      { type: 'single_choice', ... },
      { type: 'multiple_choice', ... },
      // ... all 8 types
    ];
    
    const { getByText } = render(
      <DynamicQuestionRenderer questions={questions} />
    );
    
    // Verify each question type renders
    expect(getByText('Single choice question')).toBeInTheDocument();
  });
  
  it('handles conditional logic correctly', () => {
    // Test show/hide based on previous answers
  });
});
```

```bash
# Run backend tests
cd app/backend
pytest tests/test_generation_service.py -v
pytest tests/test_evaluation_service.py -v

# Run frontend tests  
cd app/frontend
pnpm test

# Expected: All tests pass
```

### Level 3: Integration Test
```bash
# Start services
make up

# Test AI generation endpoint
curl -X POST http://localhost:8088/api/v1/ai-questionnaires/generate \
  -H "Authorization: Bearer dev_bypass_token_2025_talentforge" \
  -H "Content-Type: application/json" \
  -d '{
    "position_type": "烟草生产技术岗",
    "dimensions": ["工艺流程理解", "质量控制意识"],
    "question_count": 10
  }'

# Expected: Returns generated questionnaire JSON

# Test evaluation endpoint
curl -X POST http://localhost:8088/api/v1/ai-questionnaires/evaluate \
  -H "Authorization: Bearer dev_bypass_token_2025_talentforge" \
  -H "Content-Type: application/json" \
  -d '{
    "questionnaire_id": "123456",
    "candidate_id": "789012",
    "responses": {...}
  }'

# Expected: Returns evaluation report with scores
```

## Final Validation Checklist

### Core Functionality (FR Requirements)
- [ ] AI generation completes in <30 seconds (FR-GEN-003)
- [ ] Preview mode shows generated questionnaire (FR-GEN-004)
- [ ] Question editing with text, options, weights works (FR-EDIT-001)
- [ ] Add/delete questions and drag-drop reordering functional (FR-EDIT-002)
- [ ] Conditional logic configuration works (FR-EDIT-003)
- [ ] Version management tracks changes (FR-EDIT-004)
- [ ] Publishing workflow: draft→reviewing→published (FR-PUB-001-003)
- [ ] Public link generation and access works (FR-PUB-001)
- [ ] Email notifications sent on publish (FR-PUB-002)
- [ ] Approval workflow with comments functional (FR-PUB-003)
- [ ] Candidate submission via public link works (FR-ANSWER-001-004)
- [ ] Evaluation generates all dimension scores (FR-EVAL-001-004)
- [ ] Candidate comparison shows 2-5 candidates (FR-DASH-002)
- [ ] Statistics panel shows mean/median/std deviation (FR-DASH-003)
- [ ] Excel and PDF export functional (FR-DASH-004)

### Security & Performance (NFR Requirements)
- [ ] JWT authentication with role information (NFR-SEC-001)
- [ ] RBAC permissions enforced on all endpoints (NFR-SEC-001)
- [ ] Audit logs capture all operations (NFR-SEC-003)
- [ ] Page load time < 3 seconds (NFR-PERF-001)
- [ ] API response time < 500ms (NFR-PERF-001)
- [ ] System handles 100+ concurrent users (NFR-PERF-002)
- [ ] Rate limiting prevents API abuse (10 req/min for DeepSeek)
- [ ] Error messages use proper error codes format

### Technical Validation
- [ ] All backend tests pass: `cd app/backend && pytest tests/ -v`
- [ ] All frontend tests pass: `cd app/frontend && pnpm test`
- [ ] No linting errors: `ruff check app/backend/app/`
- [ ] No TypeScript errors: `cd app/frontend && pnpm type-check`
- [ ] Database migrations applied successfully
- [ ] Docker containers healthy: `make status`
- [ ] All text content uses i18n translation functions

## Anti-Patterns to Avoid
- ❌ Don't hardcode Chinese text - use i18n system
- ❌ Don't use UUID - project uses Snowflake IDs
- ❌ Don't skip rate limiting for DeepSeek API
- ❌ Don't mix UI libraries - use shadcn/ui only
- ❌ Don't access crud_instance.model.method() - use crud methods directly
- ❌ Don't store sensitive API keys in code
- ❌ Don't catch all exceptions - handle specific errors
- ❌ Don't skip dependency refresh after package changes

## Tobacco Industry Templates

### 1. 烟草生产技术岗
```json
{
  "dimensions": [
    "工艺流程理解",
    "质量控制意识", 
    "设备操作能力",
    "安全生产意识",
    "问题解决能力"
  ],
  "sample_questions": [
    {
      "title": "当制丝车间出现烟叶含水率异常时，您的第一反应是？",
      "options": {
        "A": {"text": "立即调整温湿度参数并记录", "score": 5},
        "B": {"text": "先观察一段时间再处理", "score": 2},
        "C": {"text": "等待主管指示", "score": 1},
        "D": {"text": "继续生产不予理会", "score": 0}
      }
    }
  ]
}
```

### 2. 烟草营销管理岗
```json
{
  "dimensions": [
    "专卖法规知识",
    "市场分析能力",
    "客户服务意识",
    "营销策略制定",
    "数据分析能力"
  ]
}
```

### 3. 烟草质检岗
```json
{
  "dimensions": [
    "检验标准掌握",
    "检测技术能力",
    "数据分析能力",
    "问题追溯能力",
    "规范执行力"
  ]
}
```

### 4. 烟草物流配送岗
```json
{
  "dimensions": [
    "物流规划能力",
    "库存管理能力",
    "信息系统操作",
    "应急处理能力",
    "服务意识"
  ]
}
```

### 5. 综合管理岗
```json
{
  "dimensions": [
    "企业文化认同",
    "合规意识",
    "团队协作",
    "创新思维",
    "执行力"
  ]
}
```

## Development Timeline (8-9 Days)

### Day 1-2: Backend Foundation
- DeepSeek API integration
- Database models and migrations (with publishing fields)
- Generation service implementation
- Security foundation (RBAC structure)

### Day 3: API Layer & Publishing
- AI questionnaire endpoints
- Publishing workflow API (FR-PUB)
- Approval and review endpoints
- Notification service integration

### Day 4: Frontend Generation & Editing
- AI generation wizard with preview (FR-GEN-004)
- Complete question editor (FR-EDIT-001-004)
- Drag-drop reordering
- Version management UI

### Day 5: Dynamic Rendering
- Question renderer component
- Conditional logic (FR-EDIT-003)
- Score calculation
- Public questionnaire access

### Day 6: Analytics & Export
- Candidate comparison table (FR-DASH-001-002)
- Radar chart visualization
- Statistics panel (FR-DASH-003)
- Excel/PDF export service (FR-DASH-004)

### Day 7: Security Implementation
- RBAC permission system (NFR-SEC-001)
- Audit logging (NFR-SEC-003)
- SQL injection prevention (NFR-SEC-002)
- Session management

### Day 8: Testing & Validation
- End-to-end testing
- Performance optimization
- Security testing
- Load testing (100+ users)

### Day 9: Polish & Deployment (Optional Buffer)
- UI/UX refinements
- Documentation updates
- Final validation checklist
- Production deployment preparation

---

## Notes for Implementation Team

1. **Priority**: Focus on core generation and evaluation first, analytics can be simplified if time is tight
2. **DeepSeek Integration**: Start with mock responses for development, integrate real API once available
3. **Scoring Logic**: Leverage existing scoring system in questionnaire.py, don't reinvent
4. **UI Components**: Reuse existing survey-builder components where possible
5. **Testing**: Mock DeepSeek API calls in tests to avoid rate limits and costs
6. **Security**: Never expose DeepSeek API key to frontend, all LLM calls through backend
7. **Performance**: Cache generated templates for reuse, don't regenerate identical assessments

## Risk Mitigation

1. **DeepSeek API Unavailable**: Implement fallback to predefined templates
2. **Rate Limiting**: Queue generation requests, notify users of wait time
3. **Poor AI Output**: Add validation layer to check generated JSON structure
4. **Performance Issues**: Implement pagination for analytics, limit concurrent evaluations
5. **Data Privacy**: Ensure candidate responses are properly encrypted and access-controlled