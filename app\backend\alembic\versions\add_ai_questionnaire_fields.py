"""Add AI questionnaire generation fields

Revision ID: add_ai_fields_001
Revises: fix_questionnaire
Create Date: 2025-01-21 02:50:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'add_ai_fields_001'
down_revision: Union[str, None] = 'fix_questionnaire'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Add AI-related columns to questionnaires table
    op.add_column('questionnaires', sa.Column('ai_generated', sa.<PERSON>(), nullable=True, default=False))
    op.add_column('questionnaires', sa.Column('generation_prompt', sa.Text(), nullable=True))
    op.add_column('questionnaires', sa.Column('ai_template_id', sa.<PERSON>nte<PERSON>(), nullable=True))
    op.add_column('questionnaires', sa.Column('evaluation_criteria', sa.JSON(), nullable=True))
    op.add_column('questionnaires', sa.Column('industry', sa.String(length=50), nullable=True))
    op.add_column('questionnaires', sa.Column('position_type', sa.String(length=100), nullable=True))
    op.add_column('questionnaires', sa.Column('dimensions', sa.JSON(), nullable=True))
    op.add_column('questionnaires', sa.Column('dimensions_weight', sa.JSON(), nullable=True))
    
    # Add publishing-related columns
    op.add_column('questionnaires', sa.Column('slug', sa.String(length=255), nullable=True))
    op.add_column('questionnaires', sa.Column('valid_until', sa.DateTime(timezone=True), nullable=True))
    op.add_column('questionnaires', sa.Column('access_type', sa.String(length=50), nullable=True))
    op.add_column('questionnaires', sa.Column('max_submissions', sa.Integer(), nullable=True))
    
    # Add review-related columns
    op.add_column('questionnaires', sa.Column('reviewed_by', sa.BigInteger(), nullable=True))
    op.add_column('questionnaires', sa.Column('review_comments', sa.Text(), nullable=True))
    op.add_column('questionnaires', sa.Column('rejection_reason', sa.Text(), nullable=True))
    op.add_column('questionnaires', sa.Column('approved_at', sa.DateTime(timezone=True), nullable=True))
    
    # Create unique constraint for slug
    op.create_unique_constraint('uq_questionnaires_slug', 'questionnaires', ['slug'])
    
    # Create foreign key for reviewed_by
    op.create_foreign_key('fk_questionnaires_reviewed_by', 'questionnaires', 'users', ['reviewed_by'], ['id'])
    
    # Create evaluation_reports table
    op.create_table('evaluation_reports',
        sa.Column('id', sa.BigInteger(), nullable=False),
        sa.Column('questionnaire_id', sa.BigInteger(), nullable=False),
        sa.Column('candidate_id', sa.BigInteger(), nullable=True),
        sa.Column('submission_id', sa.BigInteger(), nullable=True),
        sa.Column('dimension_scores', sa.JSON(), nullable=True),
        sa.Column('total_score', sa.Float(), nullable=True),
        sa.Column('overall_evaluation', sa.Text(), nullable=True),
        sa.Column('strengths', sa.JSON(), nullable=True),
        sa.Column('weaknesses', sa.JSON(), nullable=True),
        sa.Column('recommendations', sa.Text(), nullable=True),
        sa.Column('key_insights', sa.Text(), nullable=True),
        sa.Column('is_qualified', sa.Boolean(), nullable=True, default=False),
        sa.Column('match_score', sa.Float(), nullable=True),
        sa.Column('risk_level', sa.String(length=20), nullable=True),
        sa.Column('percentile_rank', sa.Float(), nullable=True),
        sa.Column('evaluated_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes for evaluation_reports
    op.create_index('idx_eval_report_questionnaire', 'evaluation_reports', ['questionnaire_id'])
    op.create_index('idx_eval_report_candidate', 'evaluation_reports', ['candidate_id'])
    op.create_index('idx_eval_report_submission', 'evaluation_reports', ['submission_id'])
    op.create_index('idx_eval_report_evaluated_at', 'evaluation_reports', ['evaluated_at'])
    op.create_index('idx_eval_report_match_score', 'evaluation_reports', ['match_score'])
    
    # Create foreign keys for evaluation_reports
    op.create_foreign_key('fk_eval_report_questionnaire', 'evaluation_reports', 'questionnaires', ['questionnaire_id'], ['id'])
    op.create_foreign_key('fk_eval_report_candidate', 'evaluation_reports', 'candidates', ['candidate_id'], ['id'])
    
    # Create audit_logs table
    op.create_table('audit_logs',
        sa.Column('id', sa.BigInteger(), nullable=False),
        sa.Column('user_id', sa.BigInteger(), nullable=True),
        sa.Column('action', sa.String(length=100), nullable=False),
        sa.Column('resource_type', sa.String(length=50), nullable=False),
        sa.Column('resource_id', sa.BigInteger(), nullable=True),
        sa.Column('changes', sa.JSON(), nullable=True),
        sa.Column('ip_address', sa.String(length=50), nullable=True),
        sa.Column('user_agent', sa.String(length=500), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes for audit_logs
    op.create_index('idx_audit_log_user', 'audit_logs', ['user_id'])
    op.create_index('idx_audit_log_action', 'audit_logs', ['action'])
    op.create_index('idx_audit_log_resource', 'audit_logs', ['resource_type', 'resource_id'])
    op.create_index('idx_audit_log_created_at', 'audit_logs', ['created_at'])
    
    # Create foreign key for audit_logs
    op.create_foreign_key('fk_audit_log_user', 'audit_logs', 'users', ['user_id'], ['id'])


def downgrade() -> None:
    # Drop audit_logs table
    op.drop_table('audit_logs')
    
    # Drop evaluation_reports table
    op.drop_table('evaluation_reports')
    
    # Remove columns from questionnaires table
    op.drop_constraint('fk_questionnaires_reviewed_by', 'questionnaires', type_='foreignkey')
    op.drop_constraint('uq_questionnaires_slug', 'questionnaires', type_='unique')
    
    op.drop_column('questionnaires', 'approved_at')
    op.drop_column('questionnaires', 'rejection_reason')
    op.drop_column('questionnaires', 'review_comments')
    op.drop_column('questionnaires', 'reviewed_by')
    op.drop_column('questionnaires', 'max_submissions')
    op.drop_column('questionnaires', 'access_type')
    op.drop_column('questionnaires', 'valid_until')
    op.drop_column('questionnaires', 'slug')
    op.drop_column('questionnaires', 'dimensions_weight')
    op.drop_column('questionnaires', 'dimensions')
    op.drop_column('questionnaires', 'position_type')
    op.drop_column('questionnaires', 'industry')
    op.drop_column('questionnaires', 'evaluation_criteria')
    op.drop_column('questionnaires', 'ai_template_id')
    op.drop_column('questionnaires', 'generation_prompt')
    op.drop_column('questionnaires', 'ai_generated')