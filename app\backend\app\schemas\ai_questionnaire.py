"""
Pydantic schemas for AI Questionnaire Generation and Evaluation
"""
from typing import Dict, Any, List, Optional
from pydantic import BaseModel, Field, ConfigDict
from datetime import datetime
from app.core.id_types import SnowflakeID


class QuestionnaireGenerateRequest(BaseModel):
    """Request model for generating AI questionnaire"""
    position_type: str = Field(..., description="Position type (e.g., 生产技术岗, 营销管理岗)")
    dimensions: Optional[List[str]] = Field(None, description="Evaluation dimensions")
    question_count: int = Field(20, ge=5, le=50, description="Number of questions to generate")
    industry: Optional[str] = Field("烟草", description="Industry context")
    custom_requirements: Optional[str] = Field(None, description="Additional custom requirements")
    difficulty_level: Optional[str] = Field("medium", description="Difficulty level: easy, medium, hard")


class QuestionnaireEvaluateRequest(BaseModel):
    """Request model for evaluating questionnaire responses"""
    candidate_id: SnowflakeID = Field(..., description="Candidate ID")
    submission_id: Optional[SnowflakeID] = Field(None, description="Submission ID")
    responses: Dict[str, Any] = Field(..., description="Question ID to answer mapping")


class EvaluationReportResponse(BaseModel):
    """Response model for evaluation report"""
    model_config = ConfigDict(from_attributes=True)
    
    id: SnowflakeID
    questionnaire_id: SnowflakeID
    candidate_id: Optional[SnowflakeID]
    submission_id: Optional[SnowflakeID]
    dimension_scores: Optional[Dict[str, float]]
    total_score: Optional[float]
    overall_evaluation: Optional[str]
    strengths: Optional[List[str]]
    weaknesses: Optional[List[str]]
    recommendations: Optional[str]
    key_insights: Optional[str]
    is_qualified: Optional[bool]
    match_score: Optional[float]
    risk_level: Optional[str]
    percentile_rank: Optional[float]
    evaluated_at: Optional[datetime]
    created_at: Optional[datetime]
    updated_at: Optional[datetime]


class QuestionGenerateRequest(BaseModel):
    """Request model for generating individual questions"""
    dimension: str = Field(..., description="Evaluation dimension")
    question_type: str = Field(..., description="Question type: single_choice, multiple_choice, etc.")
    difficulty: str = Field("medium", description="Difficulty level")
    context: Optional[str] = Field(None, description="Additional context for question generation")


class QuestionResponse(BaseModel):
    """Response model for generated question"""
    id: Optional[SnowflakeID]
    title: str
    type: str
    dimension: Optional[str]
    required: bool = True
    options: Optional[List[Dict[str, Any]]]
    validation_rules: Optional[Dict[str, Any]]
    scoring_config: Optional[Dict[str, Any]]
    order: Optional[int]


class QuestionnairePublishRequest(BaseModel):
    """Request model for publishing questionnaire"""
    slug: str = Field(..., min_length=3, max_length=100, description="URL-friendly identifier")
    valid_until: Optional[datetime] = Field(None, description="Expiration date")
    access_type: str = Field("private", description="Access type: private, public, password")
    max_submissions: Optional[int] = Field(None, ge=1, description="Maximum number of submissions")
    password: Optional[str] = Field(None, description="Password for password-protected access")


class QuestionnaireReviewRequest(BaseModel):
    """Request model for reviewing questionnaire"""
    approved: bool = Field(..., description="Approval decision")
    comments: Optional[str] = Field(None, description="Review comments")
    rejection_reason: Optional[str] = Field(None, description="Reason for rejection if not approved")


class CandidateComparisonRequest(BaseModel):
    """Request model for comparing candidates"""
    questionnaire_id: SnowflakeID = Field(..., description="Questionnaire ID")
    candidate_ids: List[SnowflakeID] = Field(..., min_items=2, max_items=10, description="Candidate IDs to compare")


class TemplateResponse(BaseModel):
    """Response model for questionnaire template"""
    position_type: str
    description: str
    dimensions: List[str]
    question_types: List[str]
    focus_areas: List[str]


class GenerationStatusResponse(BaseModel):
    """Response model for generation status"""
    status: str = Field(..., description="Generation status: pending, processing, completed, failed")
    progress: int = Field(0, ge=0, le=100, description="Progress percentage")
    message: Optional[str] = Field(None, description="Status message")
    result: Optional[Dict[str, Any]] = Field(None, description="Generation result when completed")
    error: Optional[str] = Field(None, description="Error message if failed")


class BulkGenerateRequest(BaseModel):
    """Request model for bulk question generation"""
    questionnaire_id: SnowflakeID = Field(..., description="Target questionnaire ID")
    questions: List[QuestionGenerateRequest] = Field(..., min_items=1, max_items=50, description="Questions to generate")
    auto_order: bool = Field(True, description="Automatically order questions")


class AIFeedbackRequest(BaseModel):
    """Request model for providing feedback on AI-generated content"""
    resource_type: str = Field(..., description="Resource type: questionnaire, question, evaluation")
    resource_id: SnowflakeID = Field(..., description="Resource ID")
    feedback_type: str = Field(..., description="Feedback type: positive, negative, correction")
    feedback_text: Optional[str] = Field(None, description="Detailed feedback")
    corrected_content: Optional[Dict[str, Any]] = Field(None, description="Corrected content if applicable")


class AIImproveRequest(BaseModel):
    """Request model for improving AI-generated content"""
    questionnaire_id: SnowflakeID = Field(..., description="Questionnaire to improve")
    improvement_type: str = Field(..., description="Type: clarity, difficulty, relevance, diversity")
    target_dimensions: Optional[List[str]] = Field(None, description="Specific dimensions to improve")
    additional_context: Optional[str] = Field(None, description="Additional context for improvement")