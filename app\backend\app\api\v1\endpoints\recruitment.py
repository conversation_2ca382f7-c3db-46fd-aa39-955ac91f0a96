"""
Recruitment Dashboard API Endpoints for Sprint 4
Provides dashboard statistics, trends, and activities

Data Flow:
- Statistics: Aggregated from candidates, positions, and assessments tables
- Trends: Time-series data from candidates and assessments creation dates
- Activities: Recent changes tracked from multiple tables
"""
from typing import Any, Dict, List, Optional
from datetime import datetime, timedelta, timezone
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_, case, distinct

from app.api.deps import get_db
from app.core.permissions import check_permission
from app.core.exceptions import not_found, bad_request
from app.core.enums import Permission
from app.models.user import User
from app.models.candidate import Candidate, CandidateStatus
from app.models.position import Position, PositionStatus
from app.models.assessment import CandidateAssessment
from app.schemas.recruitment import (
    DashboardStatsResponse,
    TrendsResponse,
    TrendDataPoint,
    ActivitiesResponse,
    ActivityItem,
    ExportRequest,
    ExportResponse
)
from app.services.recruitment_service import RecruitmentService
from app.services.cache_service import cache_service
import logging

router = APIRouter()
logger = logging.getLogger(__name__)


@router.get("/dashboard/stats/", response_model=DashboardStatsResponse)
async def get_dashboard_stats(
    time_range: str = Query("today", regex="^(today|week|month)$"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.AI_MATCH)),
) -> Any:
    """
    Get recruitment dashboard statistics
    
    Data Sources:
    - new_candidates: COUNT from candidates table where created_at matches time_range
    - pending_matches: COUNT from positions with status='open' without recent matches
    - avg_dci_score: AVG from candidate_assessments.dci_score
    - weekly_assessments: COUNT from candidate_assessments where assessed_at in last 7 days
    
    Cache Strategy:
    - Cache key: recruitment:stats:{user_id}:{time_range}
    - TTL: 5 minutes for real-time feel with reasonable DB load
    """
    # Check cache first
    cache_key = f"recruitment:stats:{current_user.id}:{time_range}"
    cached = await cache_service.get(cache_key)
    if cached:
        logger.info(f"Returning cached stats for user {current_user.id}")
        return cached
    
    # Initialize service
    recruitment_service = RecruitmentService(db)
    
    # Get fresh stats from database
    stats = await recruitment_service.get_dashboard_stats(
        user_id=current_user.id,
        time_range=time_range
    )
    
    # Cache the result
    await cache_service.set(cache_key, stats.dict(), ttl=300)  # 5 minutes
    
    return stats


@router.get("/dashboard/trends/", response_model=TrendsResponse)
async def get_recruitment_trends(
    period: str = Query("7d", regex="^(1d|7d|30d|90d)$"),
    metrics: List[str] = Query(default=["candidates", "assessments", "matches"]),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.AI_MATCH)),
) -> Any:
    """
    Get recruitment trends over time
    
    Data Sources:
    - candidates: Daily/Weekly counts from candidates.created_at
    - assessments: Daily/Weekly counts from candidate_assessments.assessed_at
    - matches: Estimated from assessment completion rates
    
    Aggregation:
    - 1d: Hourly data points
    - 7d: Daily data points
    - 30d: Daily data points
    - 90d: Weekly data points
    """
    # Cache key with period and metrics
    cache_key = f"recruitment:trends:{current_user.id}:{period}:{'_'.join(sorted(metrics))}"
    cached = await cache_service.get(cache_key)
    if cached:
        return cached
    
    recruitment_service = RecruitmentService(db)
    trends = await recruitment_service.get_trends(
        period=period,
        metrics=metrics,
        user_id=current_user.id
    )
    
    # Cache with shorter TTL for recent data
    ttl = 300 if period in ["1d", "7d"] else 900  # 5 or 15 minutes
    await cache_service.set(cache_key, trends.dict(), ttl=ttl)
    
    return trends


@router.get("/dashboard/activities/", response_model=ActivitiesResponse)
async def get_recent_activities(
    limit: int = Query(20, ge=1, le=100),
    skip: int = Query(0, ge=0),
    activity_type: Optional[str] = Query(None, regex="^(candidate_added|assessment_completed|matching_completed|position_created)$"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.AI_MATCH)),
) -> Any:
    """
    Get recent recruitment activities
    
    Data Sources (Unified from multiple tables):
    - candidate_added: FROM candidates WHERE created_at > threshold
    - assessment_completed: FROM candidate_assessments WHERE assessed_at > threshold
    - position_created: FROM positions WHERE created_at > threshold
    - matching_completed: Inferred from assessment completions with high scores
    
    Ordering: By timestamp DESC (most recent first)
    """
    recruitment_service = RecruitmentService(db)
    
    activities = await recruitment_service.get_recent_activities(
        limit=limit,
        skip=skip,
        activity_type=activity_type,
        user_id=current_user.id
    )
    
    return activities


@router.get("/dashboard/summary/", response_model=Dict[str, Any])
async def get_recruitment_summary(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.AI_MATCH)),
) -> Any:
    """
    Get comprehensive recruitment summary
    
    Aggregates:
    - Total candidates by status
    - Total positions by urgency
    - Average assessment scores by dimension
    - Top performing candidates (DCI > 80)
    - Critical open positions (urgency = 'critical')
    """
    cache_key = f"recruitment:summary:{current_user.id}"
    cached = await cache_service.get(cache_key)
    if cached:
        return cached
    
    recruitment_service = RecruitmentService(db)
    summary = await recruitment_service.get_recruitment_summary(user_id=current_user.id)
    
    await cache_service.set(cache_key, summary, ttl=600)  # 10 minutes
    return summary


@router.post("/dashboard/export/", response_model=ExportResponse)
async def export_dashboard_data(
    request: ExportRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.AI_MATCH)),
) -> Any:
    """
    Export dashboard data in various formats
    
    Process:
    1. Gather requested metrics from database
    2. Format according to requested format (CSV, Excel, PDF)
    3. Store in MinIO with temporary access URL
    4. Return download URL with expiration
    """
    recruitment_service = RecruitmentService(db)
    
    # Generate export
    export_result = await recruitment_service.export_dashboard_data(
        format=request.format,
        metrics=request.metrics,
        date_range=request.date_range,
        include_charts=request.include_charts,
        user_id=current_user.id
    )
    
    return export_result


@router.get("/stats/candidates/by-status/", response_model=Dict[str, int])
async def get_candidates_by_status(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.AI_MATCH)),
) -> Any:
    """
    Get candidate count grouped by status
    
    Direct Query:
    SELECT status, COUNT(*) FROM candidates 
    WHERE created_by = :user_id OR data_permission != 'PRIVATE'
    GROUP BY status
    """
    query = select(
        Candidate.status,
        func.count(Candidate.id).label('count')
    ).where(
        or_(
            Candidate.created_by == current_user.id,
            Candidate.data_permission != 'PRIVATE'
        )
    ).group_by(Candidate.status)
    
    result = await db.execute(query)
    stats = {row.status.value: row.count for row in result}
    
    # Ensure all statuses are represented
    for status in CandidateStatus:
        if status.value not in stats:
            stats[status.value] = 0
    
    return stats


@router.get("/stats/positions/by-urgency/", response_model=Dict[str, int])
async def get_positions_by_urgency(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.AI_MATCH)),
) -> Any:
    """
    Get position count grouped by urgency level
    
    Direct Query:
    SELECT urgency, COUNT(*) FROM positions
    WHERE status = 'open' AND (created_by = :user_id OR data_permission != 'PRIVATE')
    GROUP BY urgency
    """
    query = select(
        Position.urgency,
        func.count(Position.id).label('count')
    ).where(
        and_(
            Position.status == PositionStatus.OPEN,
            or_(
                Position.created_by == current_user.id,
                Position.data_permission != 'PRIVATE'
            )
        )
    ).group_by(Position.urgency)
    
    result = await db.execute(query)
    stats = {row.urgency.value: row.count for row in result}
    
    return stats


@router.get("/stats/assessments/dimensions/", response_model=Dict[str, float])
async def get_average_dimension_scores(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.AI_MATCH)),
) -> Any:
    """
    Get average scores for each assessment dimension
    
    Direct Query:
    SELECT 
        AVG(digital_literacy_score) as digital_literacy,
        AVG(industry_skills_score) as industry_skills,
        AVG(position_skills_score) as position_skills,
        AVG(innovation_score) as innovation,
        AVG(learning_potential_score) as learning_potential
    FROM candidate_assessments
    WHERE assessed_at > NOW() - INTERVAL '30 days'
    """
    # Get assessments from last 30 days
    thirty_days_ago = datetime.now(timezone.utc) - timedelta(days=30)
    
    query = select(
        func.avg(CandidateAssessment.digital_literacy_score).label('digital_literacy'),
        func.avg(CandidateAssessment.industry_skills_score).label('industry_skills'),
        func.avg(CandidateAssessment.position_skills_score).label('position_skills'),
        func.avg(CandidateAssessment.innovation_score).label('innovation'),
        func.avg(CandidateAssessment.learning_potential_score).label('learning_potential'),
        func.avg(CandidateAssessment.dci_score).label('dci_average')
    ).where(
        CandidateAssessment.assessed_at > thirty_days_ago
    )
    
    result = await db.execute(query)
    row = result.first()
    
    if row:
        return {
            'digital_literacy': round(row.digital_literacy or 0, 2),
            'industry_skills': round(row.industry_skills or 0, 2),
            'position_skills': round(row.position_skills or 0, 2),
            'innovation': round(row.innovation or 0, 2),
            'learning_potential': round(row.learning_potential or 0, 2),
            'dci_average': round(row.dci_average or 0, 2)
        }
    
    return {
        'digital_literacy': 0,
        'industry_skills': 0,
        'position_skills': 0,
        'innovation': 0,
        'learning_potential': 0,
        'dci_average': 0
    }