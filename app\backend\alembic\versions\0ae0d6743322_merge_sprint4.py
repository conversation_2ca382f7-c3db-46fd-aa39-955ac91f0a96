"""merge_sprint4

Revision ID: 0ae0d6743322
Revises: add_candidate_stats_20250809, 20250810_sprint4
Create Date: 2025-08-10 14:44:09.700819

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '0ae0d6743322'
down_revision: Union[str, None] = ('add_candidate_stats_20250809', '20250810_sprint4')
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass