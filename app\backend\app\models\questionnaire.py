"""
Enhanced questionnaire models with scoring and advanced features
"""
from enum import Enum
from typing import Optional, List, Dict, Any
from datetime import datetime, timezone
from sqlalchemy import (
    Column, String, Text, Integer, BigInteger, Float, Boolean, 
    ForeignKey, JSON, TIMESTAMP, Enum as SQLEnum,
    Index, UniqueConstraint, CheckConstraint
)
from sqlalchemy.orm import relationship, validates
from sqlalchemy.dialects.postgresql import UUID
from app.core.database import Base
from app.core.id_types import generate_snowflake_id


class QuestionType(str, Enum):
    """Supported question types"""
    SINGLE_CHOICE = "single_choice"
    MULTIPLE_CHOICE = "multiple_choice"
    TEXT_INPUT = "text_input"
    RATING_SCALE = "rating_scale"
    MATRIX = "matrix"
    RANKING = "ranking"
    DATE = "date"
    FILE_UPLOAD = "file_upload"


class ScoringMethod(str, Enum):
    """Scoring calculation methods"""
    SUM = "sum"
    AVERAGE = "average"
    WEIGHTED = "weighted"
    CUSTOM = "custom"


class QuestionnaireCategory(str, Enum):
    """Questionnaire categories"""
    SURVEY = "survey"
    ASSESSMENT = "assessment"
    EVALUATION = "evaluation"
    QUIZ = "quiz"
    FEEDBACK = "feedback"


class QuestionnaireStatus(str, Enum):
    """Questionnaire status"""
    DRAFT = "draft"
    REVIEWING = "reviewing"  # Added for approval workflow
    ACTIVE = "active"
    PAUSED = "paused"
    COMPLETED = "completed"
    ARCHIVED = "archived"


class Questionnaire(Base):
    """Enhanced questionnaire model with scoring and advanced features"""
    __tablename__ = "questionnaires"
    
    # Primary key
    id = Column(BigInteger, primary_key=True, default=generate_snowflake_id)
    
    # Basic information
    title = Column(String(255), nullable=False)
    description = Column(Text)
    category = Column(SQLEnum(QuestionnaireCategory), default=QuestionnaireCategory.SURVEY)
    version = Column(Integer, default=1)
    
    # Metadata
    created_by = Column(BigInteger, ForeignKey("users.id", ondelete="SET NULL"))
    updated_by = Column(BigInteger, ForeignKey("users.id", ondelete="SET NULL"))
    created_at = Column(TIMESTAMP(timezone=True), default=lambda: datetime.now(timezone.utc))
    updated_at = Column(TIMESTAMP(timezone=True), default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))
    
    # Status
    status = Column(SQLEnum(QuestionnaireStatus), default=QuestionnaireStatus.DRAFT)
    published_at = Column(TIMESTAMP(timezone=True))
    archived_at = Column(TIMESTAMP(timezone=True))
    
    # Display settings
    theme = Column(String(50), default="default")
    branding = Column(JSON, default={})  # {logoUrl, primaryColor, backgroundImage}
    
    # Behavior settings
    allow_back_navigation = Column(Boolean, default=True)
    show_progress_bar = Column(Boolean, default=True)
    randomize_questions = Column(Boolean, default=False)
    one_question_per_page = Column(Boolean, default=False)
    auto_save = Column(Boolean, default=True)
    
    # Time settings
    time_limit_minutes = Column(Integer)
    scheduled_start = Column(TIMESTAMP(timezone=True))
    scheduled_end = Column(TIMESTAMP(timezone=True))
    
    # Scoring settings
    scoring_enabled = Column(Boolean, default=False)
    show_score_on_completion = Column(Boolean, default=False)
    passing_score = Column(Float)
    scoring_method = Column(SQLEnum(ScoringMethod), default=ScoringMethod.SUM)
    custom_scoring_formula = Column(Text)  # For custom scoring logic
    
    # Response settings
    max_responses = Column(Integer)
    allow_multiple_submissions = Column(Boolean, default=False)
    require_authentication = Column(Boolean, default=False)
    collect_email = Column(Boolean, default=True)
    collect_name = Column(Boolean, default=True)
    
    # Notification settings
    notify_on_submission = Column(Boolean, default=False)
    notification_emails = Column(JSON, default=[])
    respondent_notification = Column(Boolean, default=False)
    completion_message = Column(Text)
    redirect_url = Column(String(500))
    
    # AI Generation fields
    ai_generated = Column(Boolean, default=False)
    generation_prompt = Column(Text)  # Store the prompt used for generation
    ai_template_id = Column(BigInteger)  # Reference to template used
    evaluation_criteria = Column(JSON)  # AI evaluation criteria and dimensions
    industry = Column(String(50))  # Industry context (e.g., "烟草")
    position_type = Column(String(100))  # Position type for the questionnaire
    dimensions = Column(JSON)  # Evaluation dimensions ["工艺流程理解", "质量控制意识", ...]
    
    # Publishing fields
    slug = Column(String(200), unique=True)  # Unique URL slug for public access
    valid_until = Column(TIMESTAMP(timezone=True))  # Expiry date for public access
    access_type = Column(String(20), default="public")  # public/invitation
    max_submissions = Column(Integer)  # Maximum number of submissions allowed
    
    # Review fields
    reviewed_by = Column(BigInteger, ForeignKey("users.id", ondelete="SET NULL"))
    review_comments = Column(Text)  # Review feedback
    rejection_reason = Column(Text)  # If rejected during review
    approved_at = Column(TIMESTAMP(timezone=True))
    
    # Statistics (denormalized for performance)
    total_responses = Column(Integer, default=0)
    completion_rate = Column(Float, default=0.0)
    average_score = Column(Float)
    average_time_minutes = Column(Float)
    last_response_at = Column(TIMESTAMP(timezone=True))
    
    # Relationships
    sections = relationship("QuestionnaireSection", back_populates="questionnaire", cascade="all, delete-orphan", order_by="QuestionnaireSection.order")
    questions = relationship("Question", back_populates="questionnaire", cascade="all, delete-orphan", order_by="Question.order")
    responses = relationship("QuestionnaireResponse", back_populates="questionnaire")
    application_forms = relationship("ApplicationForm", back_populates="questionnaire")
    creator = relationship("User", foreign_keys=[created_by], backref="created_questionnaires")
    updater = relationship("User", foreign_keys=[updated_by], backref="updated_questionnaires")
    
    # Indexes
    __table_args__ = (
        Index("idx_questionnaire_status", "status"),
        Index("idx_questionnaire_category", "category"),
        Index("idx_questionnaire_created_by", "created_by"),
        Index("idx_questionnaire_created_at", "created_at"),
    )


class QuestionnaireSection(Base):
    """Section within a questionnaire for grouping questions"""
    __tablename__ = "questionnaire_sections"
    
    id = Column(BigInteger, primary_key=True, default=generate_snowflake_id)
    questionnaire_id = Column(BigInteger, ForeignKey("questionnaires.id", ondelete="CASCADE"), nullable=False)
    
    title = Column(String(255), nullable=False)
    description = Column(Text)
    order = Column(Integer, nullable=False)
    
    # Display settings
    is_visible = Column(Boolean, default=True)
    is_collapsible = Column(Boolean, default=False)
    is_collapsed_by_default = Column(Boolean, default=False)
    
    # Logic
    show_if_conditions = Column(JSON)  # Conditional display logic
    
    # Relationships
    questionnaire = relationship("Questionnaire", back_populates="sections")
    questions = relationship("Question", back_populates="section", cascade="all, delete-orphan", order_by="Question.order")
    
    __table_args__ = (
        UniqueConstraint("questionnaire_id", "order", name="uq_section_order"),
    )


class Question(Base):
    """Individual question with scoring capabilities"""
    __tablename__ = "questions"
    
    id = Column(BigInteger, primary_key=True, default=generate_snowflake_id)
    questionnaire_id = Column(BigInteger, ForeignKey("questionnaires.id", ondelete="CASCADE"), nullable=False)
    section_id = Column(BigInteger, ForeignKey("questionnaire_sections.id", ondelete="SET NULL"))
    
    # Basic information
    type = Column(SQLEnum(QuestionType), nullable=False)
    title = Column(Text, nullable=False)
    description = Column(Text)
    help_text = Column(Text)
    
    # Order and requirement
    order = Column(Integer, nullable=False)
    is_required = Column(Boolean, default=False)
    
    # Display configuration
    layout = Column(String(20), default="vertical")  # vertical, horizontal, grid
    media_url = Column(String(500))  # Image or video
    media_type = Column(String(20))  # image, video
    randomize_options = Column(Boolean, default=False)
    
    # Scoring configuration (admin only)
    scoring_enabled = Column(Boolean, default=False)
    max_score = Column(Float, default=0)
    weight = Column(Float, default=1.0)  # Weight in total score
    scoring_type = Column(String(20), default="fixed")  # fixed, weighted, custom
    custom_scoring_formula = Column(Text)
    
    # Options (for choice questions)
    options = Column(JSON, default=[])
    # Format: [
    #   {
    #     "id": "opt1",
    #     "text": "Option 1",
    #     "value": "value1",
    #     "order": 1,
    #     "score": 10,  # Hidden from respondents
    #     "is_correct": true,  # For quiz mode
    #     "feedback": "Explanation",
    #     "image_url": "...",
    #     "color": "#hex"
    #   }
    # ]
    
    # Validation rules
    validation = Column(JSON, default={})
    # Format: {
    #   "min_length": 10,
    #   "max_length": 500,
    #   "pattern": "regex",
    #   "min_value": 0,
    #   "max_value": 100,
    #   "min_selections": 1,
    #   "max_selections": 3,
    #   "allowed_file_types": ["pdf", "doc"]
    # }
    
    # Logic rules
    logic = Column(JSON, default={})
    # Format: {
    #   "show_if": [{"question_id": "q1", "operator": "equals", "value": "yes"}],
    #   "skip_to": "q5"
    # }
    
    # Statistics (denormalized)
    response_count = Column(Integer, default=0)
    average_score = Column(Float)
    correct_percentage = Column(Float)  # For quiz questions
    
    # Relationships
    questionnaire = relationship("Questionnaire", back_populates="questions")
    section = relationship("QuestionnaireSection", back_populates="questions")
    answers = relationship("Answer", back_populates="question")
    
    __table_args__ = (
        UniqueConstraint("questionnaire_id", "order", name="uq_question_order"),
        CheckConstraint("weight >= 0", name="check_weight_positive"),
        CheckConstraint("max_score >= 0", name="check_max_score_positive"),
    )
    
    @validates("type")
    def validate_type(self, key, value):
        """Validate question type"""
        if value not in QuestionType:
            raise ValueError(f"Invalid question type: {value}")
        return value


class QuestionnaireResponse(Base):
    """Response to a questionnaire"""
    __tablename__ = "questionnaire_responses"
    
    id = Column(BigInteger, primary_key=True, default=generate_snowflake_id)
    questionnaire_id = Column(BigInteger, ForeignKey("questionnaires.id", ondelete="CASCADE"), nullable=False)
    candidate_id = Column(BigInteger, ForeignKey("candidates.id", ondelete="SET NULL"))
    
    # Respondent information
    respondent_email = Column(String(255))
    respondent_name = Column(String(255))
    ip_address = Column(String(45))
    user_agent = Column(Text)
    location = Column(String(255))
    
    # Timing
    started_at = Column(TIMESTAMP(timezone=True), default=lambda: datetime.now(timezone.utc))
    completed_at = Column(TIMESTAMP(timezone=True))
    last_activity_at = Column(TIMESTAMP(timezone=True), default=lambda: datetime.now(timezone.utc))
    time_spent_seconds = Column(Integer, default=0)
    
    # Status
    status = Column(String(20), default="in_progress")  # in_progress, completed, abandoned
    progress = Column(Float, default=0)  # 0-100
    current_question_id = Column(BigInteger)
    
    # Scoring results
    total_score = Column(Float)
    max_possible_score = Column(Float)
    percentage_score = Column(Float)
    passed = Column(Boolean)
    score_breakdown = Column(JSON)  # Detailed scoring by question
    
    # Metadata
    source = Column(String(50))  # direct, email, social, etc.
    device_type = Column(String(20))  # desktop, mobile, tablet
    browser = Column(String(50))
    
    # Relationships
    questionnaire = relationship("Questionnaire", back_populates="responses")
    candidate = relationship("Candidate", backref="questionnaire_responses")
    answers = relationship("Answer", back_populates="response", cascade="all, delete-orphan")
    
    __table_args__ = (
        Index("idx_response_questionnaire", "questionnaire_id"),
        Index("idx_response_candidate", "candidate_id"),
        Index("idx_response_status", "status"),
        Index("idx_response_completed_at", "completed_at"),
    )


class Answer(Base):
    """Individual answer to a question"""
    __tablename__ = "answers"
    
    id = Column(BigInteger, primary_key=True, default=generate_snowflake_id)
    response_id = Column(BigInteger, ForeignKey("questionnaire_responses.id", ondelete="CASCADE"), nullable=False)
    question_id = Column(BigInteger, ForeignKey("questions.id", ondelete="CASCADE"), nullable=False)
    
    # Response data (different fields for different question types)
    text_value = Column(Text)  # For text input
    selected_option_ids = Column(JSON)  # For choice questions ["opt1", "opt2"]
    rating_value = Column(Integer)  # For rating scale
    ranking_order = Column(JSON)  # For ranking ["opt1", "opt2", "opt3"]
    matrix_responses = Column(JSON)  # For matrix {"row1": "col1", "row2": "col2"}
    date_value = Column(TIMESTAMP(timezone=True))  # For date questions
    file_url = Column(String(500))  # For file upload
    
    # Metadata
    answered_at = Column(TIMESTAMP(timezone=True), default=lambda: datetime.now(timezone.utc))
    time_spent_seconds = Column(Integer)
    
    # Scoring
    score = Column(Float)
    max_score = Column(Float)
    is_correct = Column(Boolean)
    feedback = Column(Text)  # Explanation shown after answering
    
    # Relationships
    response = relationship("QuestionnaireResponse", back_populates="answers")
    question = relationship("Question", back_populates="answers")
    
    __table_args__ = (
        UniqueConstraint("response_id", "question_id", name="uq_response_question"),
        Index("idx_answer_response", "response_id"),
        Index("idx_answer_question", "question_id"),
    )