"""auto_merge_heads

Revision ID: 280c47f0dc74
Revises: 0ae0d6743322
Create Date: 2025-08-10 17:55:44.670128

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '280c47f0dc74'
down_revision: Union[str, None] = '0ae0d6743322'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass