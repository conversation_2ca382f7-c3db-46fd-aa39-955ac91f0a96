"""
Role management API endpoints
"""
from typing import Any, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.api import deps
from app.core.database import get_db
from app.core.exceptions import ConflictError, NotFoundError, ValidationError
from app.core.exceptions import PermissionError as AppPermissionError
from app.core.permissions import has_permission, check_permission
from app.core.enums import Permission
from app.models.user import User
from app.schemas.role import (
    BulkRoleAssignment,
    RoleAssignment,
    RoleCreate,
    RoleFilter,
    RoleListResponse,
    RoleResponse,
    RoleUpdate,
)
from app.schemas.user import UserResponse
from app.services.role import role_service

router = APIRouter()


@router.get("/", response_model=RoleListResponse)
async def list_roles(
    *,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.ROLES_READ)),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    is_active: bool | None = None,
    is_system: bool | None = None,
    is_super_admin: bool | None = None,
    can_be_assigned: bool | None = None,
    min_level: int | None = None,
    max_level: int | None = None,
    search: str | None = None
) -> Any:
    """
    List all roles with optional filtering.
    
    Requires permission: roles.view
    """

    # Build filter
    filters = RoleFilter(
        is_active=is_active,
        is_system=is_system,
        is_super_admin=is_super_admin,
        can_be_assigned=can_be_assigned,
        min_level=min_level,
        max_level=max_level,
        search=search
    )

    return await role_service.list_roles(
        db,
        skip=skip,
        limit=limit,
        filters=filters
    )


@router.get("/assignable", response_model=list[RoleResponse])
async def get_assignable_roles(
    *,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.ROLES_READ))
) -> Any:
    """
    Get all roles that can be assigned to users.
    
    Requires permission: roles.view
    """
    return await role_service.get_assignable_roles(db)


@router.get("/default", response_model=Optional[RoleResponse])
async def get_default_role(
    *,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.ROLES_READ))
) -> Any:
    """
    Get the default role for new users.
    
    Requires permission: roles.view
    """
    return await role_service.get_default_role(db)


@router.get("/{role_id}", response_model=RoleResponse)
async def get_role(
    *,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.ROLES_READ)),
    role_id: int
) -> Any:
    """
    Get a specific role by ID.
    
    Requires permission: roles.view
    """
    try:
        return await role_service.get_role(db, role_id)
    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )


@router.post("/", response_model=RoleResponse, status_code=status.HTTP_201_CREATED)
async def create_role(
    *,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.ROLES_WRITE)),
    role_in: RoleCreate
) -> Any:
    """
    Create a new role.
    
    Requires permission: roles.create
    """
    try:
        return await role_service.create_role(db, role_in)
    except ConflictError as e:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=str(e)
        )
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.put("/{role_id}", response_model=RoleResponse)
async def update_role(
    *,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.ROLES_WRITE)),
    role_id: int,
    role_in: RoleUpdate
) -> Any:
    """
    Update a role.
    
    Requires permission: roles.update
    """
    try:
        return await role_service.update_role(db, role_id, role_in)
    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except AppPermissionError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )
    except ConflictError as e:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=str(e)
        )
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.delete("/{role_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_role(
    *,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.ROLES_DELETE)),
    role_id: int
) -> None:
    """
    Delete a role.
    
    Requires permission: roles.delete
    """
    try:
        await role_service.delete_role(db, role_id)
    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except AppPermissionError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )


@router.post("/assign", response_model=UserResponse)
async def assign_role_to_user(
    *,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.USERS_WRITE)),
    assignment: RoleAssignment
) -> Any:
    """
    Assign a role to a user.
    
    Requires permission: users.update
    """
    try:
        return await role_service.assign_role_to_user(db, assignment)
    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except AppPermissionError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )


@router.post("/assign/bulk", response_model=list[UserResponse])
async def bulk_assign_role(
    *,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.USERS_WRITE)),
    bulk_assignment: BulkRoleAssignment
) -> Any:
    """
    Assign a role to multiple users.
    
    Requires permission: users.update
    """
    try:
        return await role_service.bulk_assign_role(db, bulk_assignment)
    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except AppPermissionError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/user/{user_id}/permissions", response_model=list[str])
async def get_user_permissions(
    *,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(deps.get_current_active_user),
    user_id: int
) -> Any:
    """
    Get all permission codes for a user based on their role.
    
    Requires permission: users.view or viewing own permissions
    """
    # Check if viewing own permissions
    if current_user.id != user_id:
        # Check if user has permission to view other users
        from app.core.permissions import has_permission
        if not has_permission(current_user, Permission.USERS_READ):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions to view user permissions"
            )

    return await role_service.get_user_permissions(db, user_id)


@router.post("/initialize", response_model=list[RoleResponse])
async def initialize_roles(
    *,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(deps.get_current_active_superuser)
) -> Any:
    """
    Initialize default system roles.
    
    Requires super admin access.
    """
    return await role_service.initialize_default_roles(db)
