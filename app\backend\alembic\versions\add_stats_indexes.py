"""Add optimized indexes for statistics queries

Revision ID: add_stats_indexes
Revises: add_stats_prefs
Create Date: 2025-08-12 09:20:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision: str = 'add_stats_indexes'
down_revision: Union[str, None] = 'add_stats_prefs'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Composite index for candidate statistics queries
    op.execute("""
        CREATE INDEX IF NOT EXISTS idx_candidates_stats_composite 
        ON candidates (created_by, status, is_deleted, created_at) 
        WHERE is_deleted = false
    """)
    
    # Index for user management statistics (admin users)
    op.execute("""
        CREATE INDEX IF NOT EXISTS idx_users_admin_stats
        ON users (is_active, is_verified, locked_until, role)
        WHERE role IN ('super_admin', 'hr_admin', 'admin')
    """)
    
    # Index for candidate activity tracking
    op.execute("""
        CREATE INDEX IF NOT EXISTS idx_candidates_activity
        ON candidates (last_active_at, status, created_by)
        WHERE is_deleted = false AND last_active_at IS NOT NULL
    """)
    
    # Index for candidate status distribution queries
    op.execute("""
        CREATE INDEX IF NOT EXISTS idx_candidates_status_created
        ON candidates (status, created_at, data_permission)
        WHERE is_deleted = false
    """)


def downgrade() -> None:
    # Remove the statistics optimization indexes
    op.drop_index('idx_candidates_stats_composite', table_name='candidates')
    op.drop_index('idx_users_admin_stats', table_name='users')
    op.drop_index('idx_candidates_activity', table_name='candidates')
    op.drop_index('idx_candidates_status_created', table_name='candidates')