"""
Core configuration for TalentForge Pro
"""
from typing import Optional, List, Union
from pydantic import AnyHttpUrl, field_validator, ValidationInfo
from pydantic_settings import BaseSettings, SettingsConfigDict
import json


class Settings(BaseSettings):
    """Application settings with environment variable support"""
    
    # 项目配置
    PROJECT_NAME: str = "TalentForge Pro"
    VERSION: str = "1.0.0"
    API_V1_STR: str = "/api/v1"
    ENVIRONMENT: str = "development"
    DEBUG: bool = True
    
    # 安全配置
    SECRET_KEY: str  # 必须设置，至少32个字符
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7
    
    # 开发环境配置
    DEV_BYPASS_TOKEN: str = "dev_bypass_token_2025_talentforge"  # 开发环境专用通用令牌
    
    # 数据库配置
    POSTGRES_SERVER: str = "localhost"
    POSTGRES_USER: str = "tfuser"
    POSTGRES_PASSWORD: str = "tfpass123"
    POSTGRES_DB: str = "talentforge"
    DATABASE_URL: Optional[str] = None
    
    # Redis配置
    REDIS_URL: str = "redis://localhost:6379"
    REDIS_PASSWORD: Optional[str] = None
    REDIS_HOST: str = "localhost"
    REDIS_PORT: int = 6379
    REDIS_MONITORING_DB: int = 2  # Dedicated database for monitoring cache
    
    @field_validator("REDIS_HOST", mode="after")
    @classmethod
    def validate_redis_host(cls, v: str, info: ValidationInfo) -> str:
        """Parse Redis host from REDIS_URL if available"""
        redis_url = info.data.get("REDIS_URL", "redis://localhost:6379")
        if "://" in redis_url:
            # Parse redis://host:port/db or redis://:password@host:port/db
            try:
                import urllib.parse
                parsed = urllib.parse.urlparse(redis_url)
                if parsed.hostname:
                    return parsed.hostname
            except Exception:
                pass
        return v
    
    @field_validator("REDIS_PORT", mode="after")
    @classmethod  
    def validate_redis_port(cls, v: int, info: ValidationInfo) -> int:
        """Parse Redis port from REDIS_URL if available"""
        redis_url = info.data.get("REDIS_URL", "redis://localhost:6379")
        if "://" in redis_url:
            try:
                import urllib.parse
                parsed = urllib.parse.urlparse(redis_url)
                if parsed.port:
                    return parsed.port
            except Exception:
                pass
        return v
    
    # MinIO配置
    MINIO_ENDPOINT: str = "localhost:9000"
    MINIO_ACCESS_KEY: str = "minioadmin"
    MINIO_SECRET_KEY: str = "minioadmin123"
    MINIO_BUCKET: str = "talentforge"
    MINIO_USE_SSL: bool = False
    MINIO_BUCKET_RESUMES: str = "resumes"
    MINIO_BUCKET_JDS: str = "job-descriptions"
    
    # ChromaDB配置
    CHROMA_HOST: str = "localhost"
    CHROMA_PORT: int = 8000
    CHROMA_AUTH_USER: Optional[str] = None
    CHROMA_AUTH_PASSWORD: Optional[str] = None
    
    # CORS配置
    BACKEND_CORS_ORIGINS: str = "http://localhost:3000,http://localhost:3001"
    
    @property
    def cors_origins_list(self) -> List[str]:
        """Get CORS origins as a list."""
        if not self.BACKEND_CORS_ORIGINS:
            return ["http://localhost:3000", "http://localhost:3001"]
        return [url.strip() for url in self.BACKEND_CORS_ORIGINS.split(",") if url.strip()]
    
    # Snowflake ID配置
    SNOWFLAKE_MACHINE_ID: int = 1
    SNOWFLAKE_DATACENTER_ID: int = 1
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "json"  # json or text
    
    # LLM配置
    LLM_PROVIDER: str = "deepseek"
    DEEPSEEK_API_KEY: Optional[str] = None
    DEEPSEEK_API_BASE: str = "https://api.deepseek.com/v1"
    DEEPSEEK_MODEL: str = "deepseek-reasoner"
    DEEPSEEK_TEMPERATURE: float = 0.4  # For stable output
    DEEPSEEK_MAX_TOKENS: int = 4000
    DEEPSEEK_RATE_LIMIT: int = 10  # Requests per minute
    OPENAI_API_KEY: Optional[str] = None
    OLLAMA_BASE_URL: str = "http://localhost:11434"
    
    # ML模型配置
    MODEL_PATH: str = "/app/models"
    BERT_MODEL: str = "bert-base-chinese"
    USE_GPU: bool = False
    
    # Web Application Configuration
    PUBLIC_FORM_RATE_LIMIT: int = 10  # Requests per minute
    MAX_FILE_UPLOAD_SIZE: int = 10485760  # 10MB
    ENABLE_CAPTCHA: bool = False  # Enable in production
    CAPTCHA_PROVIDER: str = "recaptcha"  # recaptcha, hcaptcha, turnstile
    CAPTCHA_SITE_KEY: Optional[str] = None
    CAPTCHA_SECRET_KEY: Optional[str] = None
    CAPTCHA_MIN_SCORE: float = 0.5  # For reCAPTCHA v3
    
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=True,
        extra="ignore"
    )
    
    @property
    def async_database_url(self) -> str:
        """Get async database URL for SQLAlchemy"""
        if self.DATABASE_URL and self.DATABASE_URL.startswith("postgresql+asyncpg://"):
            return self.DATABASE_URL
        return f"postgresql+asyncpg://{self.POSTGRES_USER}:{self.POSTGRES_PASSWORD}@{self.POSTGRES_SERVER}/{self.POSTGRES_DB}"
    
    @property
    def sync_database_url(self) -> str:
        """Get sync database URL for Alembic migrations"""
        return f"postgresql://{self.POSTGRES_USER}:{self.POSTGRES_PASSWORD}@{self.POSTGRES_SERVER}/{self.POSTGRES_DB}"
    
    @property
    def redis_url_with_auth(self) -> str:
        """Get Redis URL with authentication if password is set"""
        if self.REDIS_PASSWORD:
            # Parse existing URL and add password
            if "://" in self.REDIS_URL:
                protocol, rest = self.REDIS_URL.split("://", 1)
                return f"{protocol}://:{self.REDIS_PASSWORD}@{rest}"
            else:
                return f"redis://:{self.REDIS_PASSWORD}@{self.REDIS_URL}"
        return self.REDIS_URL
    
    @field_validator("SECRET_KEY", mode="after")
    @classmethod
    def validate_secret_key(cls, v: str) -> str:
        """Validate secret key length"""
        if len(v) < 32:
            raise ValueError("SECRET_KEY must be at least 32 characters long")
        return v
    
    @field_validator("ENVIRONMENT", mode="after")
    @classmethod
    def validate_environment(cls, v: str) -> str:
        """Validate environment value"""
        allowed_envs = ["development", "staging", "production"]
        if v not in allowed_envs:
            raise ValueError(f"ENVIRONMENT must be one of: {', '.join(allowed_envs)}")
        return v


# Create global settings instance
settings = Settings()