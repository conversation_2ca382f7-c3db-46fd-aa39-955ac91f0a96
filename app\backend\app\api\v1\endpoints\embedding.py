"""
Embedding Service API Endpoints for Sprint 4
Handles text embedding generation with Ollama and OpenAI integration
"""
import logging
from typing import Any, List, Dict, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
import asyncio
import time

from app.core.database import get_db
from app.core.exceptions import bad_request, service_error, internal_server_error
from app.core.permissions import check_permission
from app.core.enums import Permission
from app.models.user import User
from app.schemas.matching import (
    EmbeddingRequest,
    EmbeddingResponse,
    BatchEmbeddingRequest
)
from app.services.embedding_service import embedding_service

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post("/generate", response_model=EmbeddingResponse, tags=["Embedding"])
async def generate_embedding(
    *,
    request: EmbeddingRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.AI_ASSESS)),
) -> Any:
    """
    Generate vector embedding for a single text
    
    **Required Permission:** `ai:assess`
    
    **Request Body:**
    - `text`: Text to generate embedding for
    - `model`: Model name (default: "bge-m3")
    - `provider`: Provider name (default: "ollama")
    
    **Supported Models:**
    - BGE-M3 (1024 dimensions) via Ollama
    - text-embedding-3-small (1536 dimensions) via OpenAI
    
    **Returns:**
    - Vector embedding as array of floats
    - Model and provider information
    - Vector dimensions
    """
    try:
        # Validate text input
        if not request.text or len(request.text.strip()) == 0:
            raise bad_request("EMBEDDING_EMPTY_TEXT", "Text cannot be empty")
        
        if len(request.text) > 8000:  # Reasonable limit for most models
            raise bad_request("EMBEDDING_TEXT_TOO_LONG", "Text exceeds maximum length of 8000 characters")
        
        logger.info(f"User {current_user.id} generating embedding for text length {len(request.text)}")
        
        start_time = time.time()
        
        # Generate embedding
        response = await embedding_service.generate_embedding(request)
        embedding = response.embedding
        
        processing_time = time.time() - start_time
        
        logger.info(f"Embedding generated in {processing_time:.3f}s, dimension: {len(embedding)}")
        
        return EmbeddingResponse(
            embedding=embedding,
            model=request.model,
            provider=request.provider,
            dimension=len(embedding)
        )
        
    except ValueError as e:
        logger.error(f"Invalid embedding request: {str(e)}")
        raise bad_request("EMBEDDING_INVALID_REQUEST", str(e))
        
    except Exception as e:
        logger.error(f"Embedding generation failed: {str(e)}")
        raise service_error("EMBEDDING_SERVICE_ERROR", "Failed to generate embedding")


@router.post("/batch", response_model=List[EmbeddingResponse], tags=["Embedding"])
async def generate_batch_embeddings(
    *,
    request: BatchEmbeddingRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.AI_ASSESS)),
) -> Any:
    """
    Generate embeddings for multiple texts in batch
    
    **Required Permission:** `ai:assess`
    
    **Request Body:**
    - `texts`: Array of texts to generate embeddings for
    - `model`: Model name (default: "bge-m3")
    - `provider`: Provider name (default: "ollama")
    
    **Batch Limits:**
    - Maximum 50 texts per request
    - Maximum 1000 characters per text (in batch)
    
    **Returns:**
    - Array of embedding responses
    - Each response contains vector, model info, and dimensions
    """
    try:
        # Validate batch size
        if len(request.texts) > 50:
            raise bad_request("EMBEDDING_BATCH_TOO_LARGE", "Maximum 50 texts per batch request")
        
        if not request.texts:
            raise bad_request("EMBEDDING_BATCH_EMPTY", "At least one text required")
        
        # Validate individual texts
        for i, text in enumerate(request.texts):
            if not text or len(text.strip()) == 0:
                raise bad_request(
                    "EMBEDDING_EMPTY_TEXT_IN_BATCH", 
                    f"Text at index {i} cannot be empty"
                )
            
            if len(text) > 1000:  # Shorter limit for batch processing
                raise bad_request(
                    "EMBEDDING_TEXT_TOO_LONG_IN_BATCH",
                    f"Text at index {i} exceeds maximum length of 1000 characters"
                )
        
        logger.info(f"User {current_user.id} generating batch embeddings for {len(request.texts)} texts")
        
        start_time = time.time()
        
        # Generate embeddings in parallel
        embedding_tasks = [
            embedding_service.generate_embedding(
                text=text,
                model=request.model,
                provider=request.provider
            )
            for text in request.texts
        ]
        
        embeddings = await asyncio.gather(*embedding_tasks, return_exceptions=True)
        
        processing_time = time.time() - start_time
        
        # Process results
        results = []
        successful_count = 0
        
        for i, embedding in enumerate(embeddings):
            if isinstance(embedding, Exception):
                logger.warning(f"Failed to generate embedding for text {i}: {str(embedding)}")
                # Return empty embedding for failed cases
                results.append(EmbeddingResponse(
                    embedding=[],
                    model=request.model,
                    provider=request.provider,
                    dimension=0
                ))
            else:
                results.append(EmbeddingResponse(
                    embedding=embedding,
                    model=request.model,
                    provider=request.provider,
                    dimension=len(embedding)
                ))
                successful_count += 1
        
        logger.info(f"Batch embeddings completed in {processing_time:.3f}s: {successful_count}/{len(request.texts)} successful")
        
        return results
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Batch embedding generation failed: {str(e)}")
        raise service_error("EMBEDDING_BATCH_SERVICE_ERROR", "Failed to generate batch embeddings")


@router.get("/test", tags=["Embedding"])
async def test_embedding_service(
    *,
    provider: Optional[str] = Query(None, description="Test specific provider (ollama/openai)"),
    model: Optional[str] = Query(None, description="Test specific model"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.AI_ASSESS)),
) -> Any:
    """
    Test embedding service connectivity and functionality
    
    **Required Permission:** `ai:assess`
    
    **Query Parameters:**
    - `provider`: Test specific provider (ollama/openai)
    - `model`: Test specific model (bge-m3/text-embedding-3-small)
    
    **Returns:**
    - Service connectivity status
    - Available models
    - Performance metrics
    - Error diagnostics if any
    """
    try:
        logger.info(f"User {current_user.id} testing embedding service")
        
        test_text = "This is a test sentence for embedding generation."
        test_results = {}
        
        # Test configurations
        test_configs = []
        
        if provider and model:
            # Test specific configuration
            test_configs.append((provider, model))
        elif provider:
            # Test all models for specific provider
            if provider == "ollama":
                test_configs.extend([("ollama", "bge-m3")])
            elif provider == "openai":
                test_configs.extend([("openai", "text-embedding-3-small")])
        else:
            # Test all default configurations
            test_configs.extend([
                ("ollama", "bge-m3"),
                ("openai", "text-embedding-3-small")
            ])
        
        # Test each configuration
        for test_provider, test_model in test_configs:
            config_key = f"{test_provider}_{test_model}"
            
            try:
                start_time = time.time()
                
                request = EmbeddingRequest(
                    text=test_text,
                    model=test_model,
                    provider=test_provider
                )
                response = await embedding_service.generate_embedding(request)
                embedding = response.embedding
                
                response_time = time.time() - start_time
                
                test_results[config_key] = {
                    "status": "healthy",
                    "provider": test_provider,
                    "model": test_model,
                    "dimension": len(embedding),
                    "response_time_ms": round(response_time * 1000, 2),
                    "sample_values": embedding[:5] if embedding else [],
                    "message": "Service is operational"
                }
                
            except Exception as e:
                test_results[config_key] = {
                    "status": "unhealthy",
                    "provider": test_provider,
                    "model": test_model,
                    "dimension": 0,
                    "response_time_ms": 0,
                    "error": str(e),
                    "message": f"Service error: {str(e)}"
                }
        
        # Overall status
        healthy_services = sum(1 for result in test_results.values() if result["status"] == "healthy")
        total_services = len(test_results)
        
        overall_status = "healthy" if healthy_services == total_services else (
            "degraded" if healthy_services > 0 else "unhealthy"
        )
        
        return {
            "overall_status": overall_status,
            "healthy_services": healthy_services,
            "total_services": total_services,
            "test_results": test_results,
            "test_text": test_text,
            "timestamp": time.time()
        }
        
    except Exception as e:
        logger.error(f"Embedding service test failed: {str(e)}")
        return {
            "overall_status": "unhealthy",
            "healthy_services": 0,
            "total_services": 0,
            "error": str(e),
            "message": "Service test failed",
            "timestamp": time.time()
        }


@router.get("/models", tags=["Embedding"])
async def list_available_models(
    current_user: User = Depends(check_permission(Permission.AI_ASSESS)),
) -> Any:
    """
    List available embedding models and their specifications
    
    **Required Permission:** `ai:assess`
    
    **Returns:**
    - Available models by provider
    - Model specifications (dimensions, capabilities)
    - Provider status information
    """
    return {
        "providers": {
            "ollama": {
                "status": "available",
                "endpoint": "http://ollama:11434",
                "models": {
                    "bge-m3": {
                        "dimensions": 1024,
                        "max_tokens": 8192,
                        "languages": ["en", "zh", "multilingual"],
                        "description": "Beijing Academy of AI BGE-M3 model",
                        "recommended": True,
                        "performance": "High quality, good for semantic search"
                    }
                }
            },
            "openai": {
                "status": "available",
                "endpoint": "https://api.openai.com/v1/embeddings",
                "models": {
                    "text-embedding-3-small": {
                        "dimensions": 1536,
                        "max_tokens": 8191,
                        "languages": ["en", "multilingual"],
                        "description": "OpenAI text-embedding-3-small model",
                        "recommended": False,
                        "performance": "Good quality, requires API key"
                    }
                }
            }
        },
        "default_config": {
            "provider": "ollama",
            "model": "bge-m3",
            "fallback_provider": "openai",
            "fallback_model": "text-embedding-3-small"
        },
        "usage_recommendations": {
            "single_text": "Use default configuration (Ollama + BGE-M3)",
            "batch_processing": "Use Ollama for better throughput",
            "multilingual": "BGE-M3 provides better multilingual support",
            "performance": "Ollama provides lower latency for local processing"
        }
    }


@router.post("/similarity", tags=["Embedding"])
async def calculate_similarity(
    *,
    text1: str,
    text2: str,
    model: Optional[str] = "bge-m3",
    provider: Optional[str] = "ollama",
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.AI_ASSESS)),
) -> Any:
    """
    Calculate similarity between two texts using embeddings
    
    **Required Permission:** `ai:assess`
    
    **Request Body:**
    - `text1`: First text for comparison
    - `text2`: Second text for comparison
    - `model`: Embedding model (optional)
    - `provider`: Embedding provider (optional)
    
    **Returns:**
    - Cosine similarity score (0.0 to 1.0)
    - Individual embeddings
    - Model information
    """
    try:
        # Validate inputs
        if not text1 or not text2:
            raise bad_request("SIMILARITY_EMPTY_TEXT", "Both texts are required")
        
        if len(text1) > 2000 or len(text2) > 2000:
            raise bad_request("SIMILARITY_TEXT_TOO_LONG", "Text length must be under 2000 characters")
        
        logger.info(f"User {current_user.id} calculating similarity between texts")
        
        start_time = time.time()
        
        # Generate embeddings for both texts
        request1 = EmbeddingRequest(text=text1, model=model, provider=provider)
        request2 = EmbeddingRequest(text=text2, model=model, provider=provider)
        
        response1, response2 = await asyncio.gather(
            embedding_service.generate_embedding(request1),
            embedding_service.generate_embedding(request2)
        )
        
        # Calculate cosine similarity
        similarity = await embedding_service.calculate_similarity(response1.embedding, response2.embedding)
        
        processing_time = time.time() - start_time
        
        return {
            "similarity_score": round(similarity, 4),
            "text1_length": len(text1),
            "text2_length": len(text2),
            "embedding_dimension": len(response1.embedding),
            "model": model,
            "provider": provider,
            "processing_time_ms": round(processing_time * 1000, 2),
            "embeddings": {
                "text1": response1.embedding[:10],  # First 10 dimensions for reference
                "text2": response2.embedding[:10]
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Similarity calculation failed: {str(e)}")
        raise service_error("SIMILARITY_SERVICE_ERROR", "Failed to calculate text similarity")


@router.get("/health", tags=["Embedding"])
async def embedding_service_health(
    current_user: User = Depends(check_permission(Permission.AI_ASSESS)),
) -> Any:
    """
    Get embedding service health status
    
    **Required Permission:** `ai:assess`
    
    **Returns:**
    - Service health status
    - Provider availability
    - Performance metrics
    - Error rates and diagnostics
    """
    try:
        # Test basic embedding generation
        test_request = EmbeddingRequest(
            text="Health check test",
            model="bge-m3",
            provider="ollama"
        )
        test_response = await embedding_service.generate_embedding(test_request)
        test_embedding = test_response.embedding
        
        return {
            "status": "healthy",
            "message": "Embedding service is operational",
            "providers": {
                "ollama": {
                    "status": "available",
                    "last_check": time.time(),
                    "model_loaded": "bge-m3"
                }
            },
            "test_result": {
                "dimension": len(test_embedding),
                "sample_values": test_embedding[:3]
            }
        }
        
    except Exception as e:
        return {
            "status": "unhealthy",
            "message": f"Service error: {str(e)}",
            "providers": {
                "ollama": {
                    "status": "unavailable",
                    "error": str(e)
                }
            },
            "last_check": time.time()
        }