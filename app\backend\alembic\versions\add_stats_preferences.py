"""Add statistics preferences to user_preferences table

Revision ID: add_stats_prefs
Revises: 2fc538b21418
Create Date: 2025-08-12 09:15:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision: str = 'add_stats_prefs'
down_revision: Union[str, None] = '2fc538b21418'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Add statistics preferences columns to user_preferences table
    op.add_column('user_preferences', sa.Column('show_dashboard_stats', sa.<PERSON>(), nullable=False, server_default=sa.text('true')))
    op.add_column('user_preferences', sa.Column('show_candidate_stats', sa.Boolean(), nullable=False, server_default=sa.text('true')))
    op.add_column('user_preferences', sa.Column('show_user_management_stats', sa.<PERSON>(), nullable=False, server_default=sa.text('true')))
    op.add_column('user_preferences', sa.Column('show_notification_badges', sa.Boolean(), nullable=False, server_default=sa.text('true')))
    op.add_column('user_preferences', sa.Column('stats_refresh_interval', sa.Integer(), nullable=False, server_default=sa.text('300')))
    op.add_column('user_preferences', sa.Column('stats_display_mode', sa.String(length=20), nullable=False, server_default=sa.text("'auto'")))


def downgrade() -> None:
    # Remove statistics preferences columns from user_preferences table
    op.drop_column('user_preferences', 'stats_display_mode')
    op.drop_column('user_preferences', 'stats_refresh_interval')
    op.drop_column('user_preferences', 'show_notification_badges')
    op.drop_column('user_preferences', 'show_user_management_stats')
    op.drop_column('user_preferences', 'show_candidate_stats')
    op.drop_column('user_preferences', 'show_dashboard_stats')