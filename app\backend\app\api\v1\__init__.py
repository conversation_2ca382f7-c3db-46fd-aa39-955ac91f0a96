"""API v1 routes"""

from fastapi import APIRouter
from app.api.v1 import auth, users, candidates, user_preferences, positions, vectors, health, matching, assessments, questionnaires
from app.api.v1.endpoints import resume, embedding, matching as new_matching, assessment, statistics, recruitment, batch, websocket, ai_questionnaire, public_questionnaire
from app.api.v1.admin import api_router as admin_api_router
from app.api.v1.public import applications as public_applications

api_router = APIRouter()

# Public endpoints (no authentication required)
api_router.include_router(public_applications.router, prefix="/public/applications", tags=["Public - Applications"])
api_router.include_router(public_questionnaire.router, prefix="/public", tags=["Public - Questionnaires"])

# Include routers
api_router.include_router(health.router, prefix="", tags=["health"])  # Health endpoints at root level
api_router.include_router(auth.router, prefix="/auth", tags=["auth"])
api_router.include_router(users.router, prefix="/users", tags=["users"])
api_router.include_router(candidates.router, prefix="/candidates", tags=["candidates"])
api_router.include_router(user_preferences.router, prefix="", tags=["user-preferences"])
api_router.include_router(positions.router, prefix="/positions", tags=["positions"])
api_router.include_router(vectors.router, prefix="/vectors", tags=["vectors"])

# Legacy Sprint 4 endpoints (for backward compatibility)
api_router.include_router(matching.router, prefix="/matching-legacy", tags=["Legacy - Matching"])
api_router.include_router(assessments.router, prefix="/assessments-legacy", tags=["Legacy - Assessments"])

# Sprint 4: New Intelligent Recruitment System APIs
api_router.include_router(resume.router, prefix="/resume", tags=["Sprint 4 - Resume Processing"])
api_router.include_router(embedding.router, prefix="/embedding", tags=["Sprint 4 - Vector Embeddings"])
api_router.include_router(new_matching.router, prefix="/matching", tags=["Sprint 4 - Intelligent Matching"])
api_router.include_router(assessment.router, prefix="/assessment", tags=["Sprint 4 - Capability Assessment"])
api_router.include_router(recruitment.router, prefix="/recruitment", tags=["Sprint 4 - Recruitment Dashboard"])
api_router.include_router(batch.router, prefix="/batch", tags=["Sprint 4 - Batch Processing"])
api_router.include_router(websocket.router, prefix="/ws", tags=["Sprint 4 - WebSocket"])

# Statistics API
api_router.include_router(statistics.router, prefix="/stats", tags=["Statistics"])

# Questionnaires API
api_router.include_router(questionnaires.router, prefix="/questionnaires", tags=["Questionnaires"])

# AI Questionnaire API
api_router.include_router(ai_questionnaire.router, prefix="/ai", tags=["AI Questionnaire"])

# Include admin routers
api_router.include_router(admin_api_router, prefix="/admin", tags=["admin"])