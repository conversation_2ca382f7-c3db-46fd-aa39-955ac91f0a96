"""
User management routes
"""
from typing import Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession
from app.api import deps
from app.core.database import get_db
from app.core.exceptions import not_found, conflict, forbidden, bad_request
from app.core.permissions import check_permission, check_any_permission
from app.core.enums import Permission, UserRole
from app.core.security import is_strong_password, verify_password
from app.crud import user as user_crud
from app.models.user import User
from app.schemas.user import (
    UserCreate, 
    UserResponse, 
    UserUpdate, 
    UserRegister,
    PasswordChange,
    UserListResponse
)

router = APIRouter()


@router.post("/register", response_model=UserResponse)
async def register(
    *,
    db: AsyncSession = Depends(get_db),
    user_in: UserRegister,
) -> Any:
    """
    User registration (open endpoint)
    
    Default role is HR_SPECIALIST
    """
    # Check if email already exists
    user = await user_crud.get_by_email(db, email=user_in.email)
    if user:
        raise conflict("USER_EMAIL_ALREADY_EXISTS")
    
    # Check if username already exists
    user = await user_crud.get_by_username(db, username=user_in.username)
    if user:
        raise conflict("USER_USERNAME_ALREADY_EXISTS")
    
    # Validate password strength
    if not is_strong_password(user_in.password):
        raise bad_request("USER_PASSWORD_WEAK")
    
    # Create user with default role
    user_create = UserCreate(
        email=user_in.email,
        username=user_in.username,
        password=user_in.password,
        full_name=user_in.full_name,
        phone=user_in.phone,
        department=user_in.department,
        position=user_in.position,
        role=UserRole.HR_SPECIALIST.value,
        is_active=True,
        is_superuser=False
    )
    
    user = await user_crud.create(db, obj_in=user_create)
    return user


@router.get("/me", response_model=UserResponse)
async def read_user_me(
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """Get current user information"""
    return current_user


@router.put("/me", response_model=UserResponse)
async def update_user_me(
    *,
    db: AsyncSession = Depends(get_db),
    user_in: UserUpdate,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """Update current user information"""
    # Check if email is being changed
    if user_in.email and user_in.email != current_user.email:
        existing = await user_crud.get_by_email(db, email=user_in.email)
        if existing:
            raise conflict("USER_EMAIL_ALREADY_EXISTS")
    
    # Check if username is being changed
    if user_in.username and user_in.username != current_user.username:
        existing = await user_crud.get_by_username(db, username=user_in.username)
        if existing:
            raise conflict("USER_USERNAME_ALREADY_EXISTS")
    
    # Users cannot change their own role or active status
    update_data = user_in.model_dump(exclude_unset=True)
    update_data.pop("role", None)
    update_data.pop("is_active", None)
    update_data.pop("is_superuser", None)
    
    user = await user_crud.update(db, db_obj=current_user, obj_in=update_data)
    return user


@router.post("/me/change-password")
async def change_password(
    *,
    db: AsyncSession = Depends(get_db),
    password_data: PasswordChange,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """Change current user password"""
    # Verify current password
    if not verify_password(password_data.current_password, current_user.hashed_password):
        raise bad_request("USER_PASSWORD_INCORRECT")
    
    # Validate new password strength
    if not is_strong_password(password_data.new_password):
        raise bad_request("USER_PASSWORD_WEAK")
    
    # Update password
    await user_crud.update_password(
        db, 
        user=current_user, 
        new_password=password_data.new_password
    )
    
    return {"success": True, "message": "SUCCESS_PASSWORD_CHANGED"}


@router.get("/", response_model=UserListResponse)
async def read_users(
    db: AsyncSession = Depends(get_db),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    search: Optional[str] = Query(None, description="Search by email, username, or name"),
    role: Optional[UserRole] = Query(None, description="Filter by role"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    current_user: User = Depends(check_permission(Permission.USERS_READ)),
) -> Any:
    """
    Get user list (requires USERS_READ permission)
    
    Supports search and filtering by role and active status
    """
    users = await user_crud.search(
        db,
        skip=skip,
        limit=limit,
        search=search,
        role=role.value if role else None,
        is_active=is_active,
    )
    
    total = await user_crud.count(
        db,
        search=search,
        role=role.value if role else None,
        is_active=is_active,
    )
    
    return UserListResponse(
        items=users,
        total=total,
        skip=skip,
        limit=limit
    )


@router.get("/{user_id}", response_model=UserResponse)
async def read_user(
    user_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.USERS_READ)),
) -> Any:
    """Get specific user by ID (requires USERS_READ permission)"""
    user = await user_crud.get(db, id=user_id)
    if not user:
        raise not_found("USER_NOT_FOUND")
    return user


@router.put("/{user_id}", response_model=UserResponse)
async def update_user(
    user_id: int,
    user_in: UserUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.USERS_WRITE)),
) -> Any:
    """Update user information (requires USERS_WRITE permission)"""
    user = await user_crud.get(db, id=user_id)
    if not user:
        raise not_found("USER_NOT_FOUND")
    
    # Check if email is being changed
    if user_in.email and user_in.email != user.email:
        existing = await user_crud.get_by_email(db, email=user_in.email)
        if existing:
            raise conflict("USER_EMAIL_ALREADY_EXISTS")
    
    # Check if username is being changed
    if user_in.username and user_in.username != user.username:
        existing = await user_crud.get_by_username(db, username=user_in.username)
        if existing:
            raise conflict("USER_USERNAME_ALREADY_EXISTS")
    
    # Only super admin can change user roles
    if user_in.role and user_in.role != user.role:
        if current_user.role != UserRole.SUPER_ADMIN.value:
            raise forbidden("USER_ROLE_CHANGE_FORBIDDEN")
    
    user = await user_crud.update(db, db_obj=user, obj_in=user_in)
    return user


@router.delete("/{user_id}")
async def delete_user(
    user_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.USERS_DELETE)),
) -> Any:
    """Delete user (soft delete - requires USERS_DELETE permission)"""
    user = await user_crud.get(db, id=user_id)
    if not user:
        raise not_found("USER_NOT_FOUND")
    
    # Cannot delete yourself
    if user.id == current_user.id:
        raise bad_request("USER_DELETE_SELF_FORBIDDEN")
    
    # Soft delete - deactivate user
    user.is_active = False
    await db.commit()
    
    return {"success": True, "message": "SUCCESS_USER_DEACTIVATED"}


@router.post("/{user_id}/activate")
async def activate_user(
    user_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.USERS_WRITE)),
) -> Any:
    """Activate a deactivated user (requires USERS_WRITE permission)"""
    user = await user_crud.get(db, id=user_id)
    if not user:
        raise not_found("USER_NOT_FOUND")
    
    if user.is_active:
        raise bad_request("USER_ALREADY_ACTIVE")
    
    user.is_active = True
    await db.commit()
    
    return {"success": True, "message": "SUCCESS_USER_ACTIVATED"}


@router.post("/{user_id}/unlock")
async def unlock_user(
    user_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.USERS_WRITE)),
) -> Any:
    """Unlock a locked user account (requires USERS_WRITE permission)"""
    user = await user_crud.get(db, id=user_id)
    if not user:
        raise not_found("USER_NOT_FOUND")
    
    if not user.locked_until:
        raise bad_request("USER_NOT_LOCKED")
    
    user.locked_until = None
    user.failed_login_attempts = 0
    await db.commit()
    
    return {"success": True, "message": "SUCCESS_USER_UNLOCKED"}