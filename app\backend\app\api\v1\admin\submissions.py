"""
Admin API endpoints for managing application submissions
"""
from typing import Any, List, Optional
from datetime import datetime
from fastapi import APIRouter, Depends, Query, status
from sqlalchemy.ext.asyncio import AsyncSession
from app.api import deps
from app.core.database import get_db
from app.core.exceptions import not_found, forbidden, bad_request
from app.core.permissions import check_permission
from app.core.enums import Permission
from app.crud import submission as submission_crud
from app.crud import candidate as candidate_crud
from app.models.user import User
from app.models.candidate import ApprovalStatus
from app.schemas.submission import (
    ApplicationSubmissionDetail,
    ApplicationSubmissionListResponse,
    ApplicationReviewRequest,
    ApplicationReviewResponse,
    BulkReviewRequest,
    BulkReviewResponse
)
import logging

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/", response_model=ApplicationSubmissionListResponse)
async def list_submissions(
    db: AsyncSession = Depends(get_db),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    form_id: Optional[str] = Query(None),
    approval_status: Optional[ApprovalStatus] = Query(None),
    include_drafts: bool = Query(False),
    current_user: User = Depends(check_permission(Permission.CANDIDATES_READ))
) -> Any:
    """
    List application submissions with filters
    
    Requires CANDIDATES_READ permission.
    """
    if form_id:
        # Get submissions for specific form
        submissions = await submission_crud.get_by_form(
            db,
            form_id=form_id,
            skip=skip,
            limit=limit,
            include_drafts=include_drafts,
            approval_status=approval_status
        )
        
        # Count total for pagination
        all_submissions = await submission_crud.get_by_form(
            db,
            form_id=form_id,
            skip=0,
            limit=10000,
            include_drafts=include_drafts,
            approval_status=approval_status
        )
        total = len(all_submissions)
    else:
        # Get all submissions (pending review by default)
        if approval_status is None and not include_drafts:
            # Default to pending reviews
            submissions = await submission_crud.get_pending_reviews(
                db,
                skip=skip,
                limit=limit
            )
            all_submissions = await submission_crud.get_pending_reviews(
                db,
                skip=0,
                limit=10000
            )
            total = len(all_submissions)
        else:
            # Get all with filters
            submissions = await submission_crud.get_multi(db, skip=skip, limit=limit)
            total = await submission_crud.count(db)
    
    # Enhance with additional data
    enhanced_submissions = []
    for submission in submissions:
        detail = ApplicationSubmissionDetail(
            id=str(submission.id),
            form_id=str(submission.form_id),
            candidate_id=str(submission.candidate_id) if submission.candidate_id else None,
            form_data=submission.form_data,
            resume_file_url=submission.resume_file_url,
            resume_parsed_data=submission.resume_parsed_data,
            submitted_at=submission.submitted_at,
            submission_ip=submission.submission_ip,
            user_agent=submission.user_agent,
            is_draft=submission.is_draft,
            draft_saved_at=submission.draft_saved_at,
            completed_at=submission.completed_at,
            validation_passed=submission.validation_passed,
            validation_errors=submission.validation_errors,
            confirmation_email_sent=submission.confirmation_email_sent,
            confirmation_email_sent_at=submission.confirmation_email_sent_at,
            captcha_verified=submission.captcha_verified,
            referrer_url=submission.referrer_url,
            utm_source=submission.utm_source,
            utm_medium=submission.utm_medium,
            utm_campaign=submission.utm_campaign,
            form_title=submission.form.title if submission.form else None,
            candidate_name=submission.candidate.name if submission.candidate else None,
            candidate_email=submission.candidate.email if submission.candidate else None,
            approval_status=submission.candidate.approval_status if submission.candidate else None,
            created_at=submission.created_at,
            updated_at=submission.updated_at
        )
        enhanced_submissions.append(detail)
    
    return ApplicationSubmissionListResponse(
        items=enhanced_submissions,
        total=total,
        skip=skip,
        limit=limit
    )


@router.get("/pending", response_model=ApplicationSubmissionListResponse)
async def list_pending_submissions(
    db: AsyncSession = Depends(get_db),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    current_user: User = Depends(check_permission(Permission.CANDIDATES_READ))
) -> Any:
    """
    List submissions pending review
    
    Shortcut endpoint for reviewers.
    """
    submissions = await submission_crud.get_pending_reviews(
        db,
        skip=skip,
        limit=limit
    )
    
    # Count total
    all_pending = await submission_crud.get_pending_reviews(
        db,
        skip=0,
        limit=10000
    )
    total = len(all_pending)
    
    # Enhance with additional data
    enhanced_submissions = []
    for submission in submissions:
        detail = ApplicationSubmissionDetail(
            id=str(submission.id),
            form_id=str(submission.form_id),
            candidate_id=str(submission.candidate_id) if submission.candidate_id else None,
            form_data=submission.form_data,
            resume_file_url=submission.resume_file_url,
            resume_parsed_data=submission.resume_parsed_data,
            submitted_at=submission.submitted_at,
            submission_ip=submission.submission_ip,
            user_agent=submission.user_agent,
            is_draft=submission.is_draft,
            draft_saved_at=submission.draft_saved_at,
            completed_at=submission.completed_at,
            validation_passed=submission.validation_passed,
            validation_errors=submission.validation_errors,
            confirmation_email_sent=submission.confirmation_email_sent,
            confirmation_email_sent_at=submission.confirmation_email_sent_at,
            captcha_verified=submission.captcha_verified,
            referrer_url=submission.referrer_url,
            utm_source=submission.utm_source,
            utm_medium=submission.utm_medium,
            utm_campaign=submission.utm_campaign,
            form_title=submission.form.title if submission.form else None,
            candidate_name=submission.candidate.name if submission.candidate else None,
            candidate_email=submission.candidate.email if submission.candidate else None,
            approval_status=ApprovalStatus.PENDING,
            created_at=submission.created_at,
            updated_at=submission.updated_at
        )
        enhanced_submissions.append(detail)
    
    return ApplicationSubmissionListResponse(
        items=enhanced_submissions,
        total=total,
        skip=skip,
        limit=limit
    )


@router.get("/{submission_id}", response_model=ApplicationSubmissionDetail)
async def get_submission(
    *,
    db: AsyncSession = Depends(get_db),
    submission_id: str,
    current_user: User = Depends(check_permission(Permission.CANDIDATES_READ))
) -> Any:
    """
    Get submission details by ID
    
    Includes candidate information and questionnaire responses.
    """
    submission = await submission_crud.get(db, id=submission_id)
    
    if not submission:
        raise not_found("SUBMISSION_NOT_FOUND")
    
    # Load related data
    from sqlalchemy.orm import selectinload
    from sqlalchemy import select
    from app.models.application_submission import ApplicationSubmission
    
    query = select(ApplicationSubmission).where(
        ApplicationSubmission.id == submission.id
    ).options(
        selectinload(ApplicationSubmission.form),
        selectinload(ApplicationSubmission.candidate),
        selectinload(ApplicationSubmission.questionnaire_response)
    )
    
    result = await db.execute(query)
    submission = result.scalar_one_or_none()
    
    return ApplicationSubmissionDetail(
        id=str(submission.id),
        form_id=str(submission.form_id),
        candidate_id=str(submission.candidate_id) if submission.candidate_id else None,
        form_data=submission.form_data,
        resume_file_url=submission.resume_file_url,
        resume_parsed_data=submission.resume_parsed_data,
        submitted_at=submission.submitted_at,
        submission_ip=submission.submission_ip,
        user_agent=submission.user_agent,
        is_draft=submission.is_draft,
        draft_saved_at=submission.draft_saved_at,
        completed_at=submission.completed_at,
        validation_passed=submission.validation_passed,
        validation_errors=submission.validation_errors,
        confirmation_email_sent=submission.confirmation_email_sent,
        confirmation_email_sent_at=submission.confirmation_email_sent_at,
        captcha_verified=submission.captcha_verified,
        referrer_url=submission.referrer_url,
        utm_source=submission.utm_source,
        utm_medium=submission.utm_medium,
        utm_campaign=submission.utm_campaign,
        form_title=submission.form.title if submission.form else None,
        candidate_name=submission.candidate.name if submission.candidate else None,
        candidate_email=submission.candidate.email if submission.candidate else None,
        approval_status=submission.candidate.approval_status if submission.candidate else None,
        created_at=submission.created_at,
        updated_at=submission.updated_at
    )


@router.patch("/{submission_id}/review", response_model=ApplicationReviewResponse)
async def review_submission(
    *,
    db: AsyncSession = Depends(get_db),
    submission_id: str,
    review: ApplicationReviewRequest,
    current_user: User = Depends(check_permission(Permission.CANDIDATES_WRITE))
) -> Any:
    """
    Review an application submission (approve/reject)
    
    Updates the candidate's approval status and creates an audit trail.
    Requires CANDIDATES_WRITE permission.
    """
    try:
        submission = await submission_crud.review_submission(
            db,
            submission_id=submission_id,
            status=review.status,
            reviewed_by=current_user.id,
            notes=review.notes
        )
        
        # Send notification if requested
        if review.send_notification and submission.candidate:
            # TODO: Implement email notification service
            pass
        
        logger.info(
            f"Submission {submission_id} reviewed: {review.status} "
            f"by user {current_user.id}"
        )
        
        return ApplicationReviewResponse(
            submission_id=str(submission.id),
            candidate_id=str(submission.candidate_id),
            status=review.status,
            reviewed_by=str(current_user.id),
            reviewed_at=datetime.utcnow(),
            message=f"Application {review.status.value}"
        )
        
    except ValueError as e:
        raise bad_request(str(e))
    except Exception as e:
        logger.error(f"Error reviewing submission: {str(e)}")
        raise bad_request("REVIEW_FAILED")


@router.post("/bulk-review", response_model=BulkReviewResponse)
async def bulk_review_submissions(
    *,
    db: AsyncSession = Depends(get_db),
    bulk_review: BulkReviewRequest,
    current_user: User = Depends(check_permission(Permission.CANDIDATES_WRITE))
) -> Any:
    """
    Bulk review multiple submissions
    
    Efficient for processing multiple applications at once.
    Requires CANDIDATES_WRITE permission.
    """
    result = await submission_crud.bulk_review(
        db,
        submission_ids=bulk_review.submission_ids,
        status=bulk_review.status,
        reviewed_by=current_user.id,
        notes=bulk_review.notes
    )
    
    logger.info(
        f"Bulk review completed: {result['succeeded']} succeeded, "
        f"{result['failed']} failed by user {current_user.id}"
    )
    
    return BulkReviewResponse(**result)


@router.get("/{submission_id}/candidate", response_model=dict)
async def get_submission_candidate(
    *,
    db: AsyncSession = Depends(get_db),
    submission_id: str,
    current_user: User = Depends(check_permission(Permission.CANDIDATES_READ))
) -> Any:
    """
    Get candidate details for a submission
    
    Returns full candidate profile including questionnaire responses.
    """
    submission = await submission_crud.get(db, id=submission_id)
    
    if not submission:
        raise not_found("SUBMISSION_NOT_FOUND")
    
    if not submission.candidate_id:
        raise not_found("CANDIDATE_NOT_FOUND")
    
    # Get candidate with all related data
    candidate = await candidate_crud.get_with_assessments(
        db,
        id=submission.candidate_id
    )
    
    if not candidate:
        raise not_found("CANDIDATE_NOT_FOUND")
    
    # Get questionnaire responses
    from app.crud import questionnaire as questionnaire_crud
    
    questionnaire_responses = await questionnaire_crud.questionnaire_response.get_responses_for_candidate(
        db,
        candidate_id=candidate.id,
        skip=0,
        limit=100
    )
    
    return {
        "candidate": {
            "id": str(candidate.id),
            "name": candidate.name,
            "email": candidate.email,
            "phone": candidate.phone,
            "status": candidate.status,
            "approval_status": candidate.approval_status,
            "skills": candidate.skills,
            "years_of_experience": candidate.years_of_experience,
            "education": candidate.education,
            "work_experience": candidate.work_experience,
            "resume_url": candidate.resume_url,
            "created_at": candidate.created_at,
            "updated_at": candidate.updated_at
        },
        "questionnaire_responses": [
            {
                "id": str(r.id),
                "questionnaire_id": str(r.questionnaire_id),
                "responses": r.responses,
                "total_score": r.total_score,
                "passed": r.passed,
                "submitted_at": r.submitted_at,
                "time_taken_seconds": r.time_taken_seconds
            }
            for r in questionnaire_responses
        ]
    }


@router.delete("/{submission_id}")
async def delete_submission(
    *,
    db: AsyncSession = Depends(get_db),
    submission_id: str,
    current_user: User = Depends(check_permission(Permission.CANDIDATES_DELETE))
) -> Any:
    """
    Delete a submission (hard delete)
    
    Only for draft submissions or data cleanup.
    Requires ADMIN_MANAGE permission.
    """
    submission = await submission_crud.get(db, id=submission_id)
    
    if not submission:
        raise not_found("SUBMISSION_NOT_FOUND")
    
    # Check if it's a draft or has no candidate
    if not submission.is_draft and submission.candidate_id:
        raise bad_request("CANNOT_DELETE_COMPLETED_SUBMISSION")
    
    await submission_crud.remove(db, id=submission_id)
    
    logger.info(f"Submission deleted: {submission_id} by user {current_user.id}")
    
    return {"message": "Submission deleted successfully"}


@router.post("/cleanup-drafts")
async def cleanup_old_drafts(
    *,
    db: AsyncSession = Depends(get_db),
    days_old: int = Query(30, ge=1, le=365),
    current_user: User = Depends(check_permission(Permission.CANDIDATES_DELETE))
) -> Any:
    """
    Clean up old draft submissions
    
    Removes drafts older than specified days.
    Requires ADMIN_MANAGE permission.
    """
    count = await submission_crud.cleanup_old_drafts(
        db,
        days_old=days_old
    )
    
    logger.info(f"Cleaned up {count} old drafts by user {current_user.id}")
    
    return {
        "message": f"Cleaned up {count} draft submissions older than {days_old} days",
        "count": count
    }