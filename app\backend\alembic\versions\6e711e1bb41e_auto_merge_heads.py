"""auto_merge_heads

Revision ID: 6e711e1bb41e
Revises: add_stats_indexes
Create Date: 2025-08-13 01:34:31.364041

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '6e711e1bb41e'
down_revision: Union[str, None] = 'add_stats_indexes'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass