"""
AI Evaluation Report Model for Questionnaire Responses
"""
from datetime import datetime, timezone
from sqlalchemy import (
    Column, String, Text, BigInteger, Float, Boolean,
    ForeignKey, JSON, TIMESTAMP, Index
)
from sqlalchemy.orm import relationship

from app.core.database import Base
from app.core.id_types import generate_snowflake_id


class EvaluationReport(Base):
    """AI-generated evaluation report for questionnaire responses"""
    __tablename__ = "evaluation_reports"
    
    # Primary key
    id = Column(BigInteger, primary_key=True, default=generate_snowflake_id)
    
    # Foreign keys
    questionnaire_id = Column(BigInteger, ForeignKey("questionnaires.id", ondelete="CASCADE"), nullable=False)
    candidate_id = Column(BigInteger, ForeignKey("candidates.id", ondelete="CASCADE"), nullable=False)
    submission_id = Column(BigInteger, ForeignKey("questionnaire_responses.id", ondelete="CASCADE"))
    
    # Multi-dimensional scores
    dimension_scores = Column(JSON, nullable=False)  # {"工艺流程理解": 85, "质量控制意识": 92, ...}
    total_score = Column(Float, nullable=False)  # Overall score (0-100)
    percentile_rank = Column(Float)  # Percentile rank among all candidates
    
    # AI evaluation content
    overall_evaluation = Column(Text)  # Overall evaluation text (200-300 words)
    strengths = Column(JSON)  # List of identified strengths
    weaknesses = Column(JSON)  # List of areas for improvement
    recommendations = Column(Text)  # Improvement suggestions
    key_insights = Column(Text)  # Key insights about the candidate
    
    # Assessment results
    is_qualified = Column(Boolean, default=False)  # Pass/fail based on threshold
    match_score = Column(Float)  # Position match score (0-100)
    risk_level = Column(String(20))  # low/medium/high
    
    # Ranking and comparison
    rank_in_batch = Column(BigInteger)  # Rank among candidates in the same batch
    comparison_group = Column(String(100))  # Group identifier for comparison
    
    # Metadata
    evaluated_at = Column(TIMESTAMP(timezone=True), default=lambda: datetime.now(timezone.utc))
    evaluation_model = Column(String(50), default="deepseek-reasoner")  # AI model used
    evaluation_version = Column(String(20), default="1.0")  # Evaluation algorithm version
    
    # Relationships
    questionnaire = relationship("Questionnaire", backref="evaluation_reports")
    candidate = relationship("Candidate", backref="evaluation_reports")
    response = relationship("QuestionnaireResponse", backref="evaluation_report", uselist=False)
    
    # Indexes for performance
    __table_args__ = (
        Index("idx_evaluation_questionnaire_id", "questionnaire_id"),
        Index("idx_evaluation_candidate_id", "candidate_id"),
        Index("idx_evaluation_total_score", "total_score"),
        Index("idx_evaluation_is_qualified", "is_qualified"),
        Index("idx_evaluation_evaluated_at", "evaluated_at"),
        Index("idx_evaluation_rank", "questionnaire_id", "total_score"),  # For ranking queries
    )