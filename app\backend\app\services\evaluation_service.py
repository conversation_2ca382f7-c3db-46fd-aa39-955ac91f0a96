"""
AI Evaluation Service for Questionnaire Responses
"""
import logging
import json
from typing import Dict, Any, List, Optional
from datetime import datetime, timezone
from sqlalchemy.ext.asyncio import AsyncSession

from app.services.llm_service import llm_service
from app.models.evaluation_report import EvaluationReport
from app.models.questionnaire import Questionnaire, Question
from app.core.id_types import generate_snowflake_id

logger = logging.getLogger(__name__)


class EvaluationService:
    """Service for AI-powered evaluation of questionnaire responses"""
    
    async def evaluate_responses(
        self,
        questionnaire_id: int,
        candidate_id: int,
        submission_id: int,
        responses: Dict[str, Any],
        db: AsyncSession
    ) -> EvaluationReport:
        """
        Evaluate candidate responses using AI
        
        Args:
            questionnaire_id: ID of the questionnaire
            candidate_id: ID of the candidate
            submission_id: ID of the submission
            responses: Dictionary of question_id -> answer
            db: Database session
        
        Returns:
            EvaluationReport instance
        """
        try:
            # Get questionnaire details
            questionnaire = await db.get(Questionnaire, questionnaire_id)
            if not questionnaire:
                raise ValueError(f"Questionnaire {questionnaire_id} not found")
            
            # Calculate dimension scores
            dimension_scores = await self._calculate_dimension_scores(
                questionnaire, responses, db
            )
            
            # Calculate total score
            total_score = self._calculate_total_score(
                dimension_scores,
                questionnaire.dimensions_weight or {}
            )
            
            # Generate AI evaluation
            ai_evaluation = await self._generate_ai_evaluation(
                questionnaire,
                dimension_scores,
                total_score,
                responses
            )
            
            # Determine qualification status
            is_qualified = total_score >= (questionnaire.passing_score or 60.0)
            
            # Create evaluation report
            report = EvaluationReport(
                id=generate_snowflake_id(),
                questionnaire_id=questionnaire_id,
                candidate_id=candidate_id,
                submission_id=submission_id,
                dimension_scores=dimension_scores,
                total_score=total_score,
                overall_evaluation=ai_evaluation.get("overall_evaluation", ""),
                strengths=ai_evaluation.get("strengths", []),
                weaknesses=ai_evaluation.get("weaknesses", []),
                recommendations=ai_evaluation.get("recommendations", ""),
                key_insights=ai_evaluation.get("key_insights", ""),
                is_qualified=is_qualified,
                match_score=ai_evaluation.get("match_score", 0),
                risk_level=ai_evaluation.get("risk_level", "medium"),
                evaluated_at=datetime.now(timezone.utc)
            )
            
            # Calculate percentile rank (would need historical data)
            report.percentile_rank = await self._calculate_percentile_rank(
                questionnaire_id, total_score, db
            )
            
            return report
            
        except Exception as e:
            logger.error(f"Failed to evaluate responses: {str(e)}")
            raise
    
    async def _calculate_dimension_scores(
        self,
        questionnaire: Questionnaire,
        responses: Dict[str, Any],
        db: AsyncSession
    ) -> Dict[str, float]:
        """Calculate scores for each dimension"""
        
        dimension_scores = {}
        dimension_totals = {}
        dimension_max = {}
        
        # Get all questions for the questionnaire
        questions = questionnaire.questions
        
        for question in questions:
            # Get the dimension for this question
            dimension = question.dimension or "General"
            
            # Initialize dimension tracking
            if dimension not in dimension_scores:
                dimension_scores[dimension] = 0
                dimension_totals[dimension] = 0
                dimension_max[dimension] = 0
            
            # Get the response for this question
            response = responses.get(str(question.id))
            if response is None:
                continue
            
            # Calculate score based on question type
            score = self._calculate_question_score(question, response)
            max_score = self._get_max_question_score(question)
            
            dimension_scores[dimension] += score
            dimension_max[dimension] += max_score
            dimension_totals[dimension] += 1
        
        # Normalize scores to 0-100 scale
        normalized_scores = {}
        for dimension in dimension_scores:
            if dimension_max[dimension] > 0:
                normalized_scores[dimension] = (
                    dimension_scores[dimension] / dimension_max[dimension]
                ) * 100
            else:
                normalized_scores[dimension] = 0
        
        return normalized_scores
    
    def _calculate_question_score(self, question: Question, response: Any) -> float:
        """Calculate score for a single question response"""
        
        if question.type == "single_choice":
            # For single choice, find the selected option and return its score
            for option in question.options or []:
                if option.get("value") == response or option.get("text") == response:
                    return float(option.get("score", 0))
            return 0
        
        elif question.type == "multiple_choice":
            # For multiple choice, sum scores of selected options
            total_score = 0
            if isinstance(response, list):
                for selected in response:
                    for option in question.options or []:
                        if option.get("value") == selected or option.get("text") == selected:
                            total_score += float(option.get("score", 0))
            return total_score
        
        elif question.type == "rating_scale":
            # For rating scale, use the numeric value directly
            try:
                return float(response)
            except (ValueError, TypeError):
                return 0
        
        # For other types, return 0 (could be enhanced)
        return 0
    
    def _get_max_question_score(self, question: Question) -> float:
        """Get maximum possible score for a question"""
        
        if question.type in ["single_choice", "multiple_choice"]:
            # Return the maximum score among options
            max_score = 0
            for option in question.options or []:
                score = float(option.get("score", 0))
                if question.type == "single_choice":
                    max_score = max(max_score, score)
                else:  # multiple_choice
                    max_score += score if score > 0 else 0
            return max_score
        
        elif question.type == "rating_scale":
            # Assume 5-point scale by default
            return 5.0
        
        return 0
    
    def _calculate_total_score(
        self,
        dimension_scores: Dict[str, float],
        dimension_weights: Dict[str, float]
    ) -> float:
        """Calculate weighted total score"""
        
        if not dimension_weights:
            # If no weights provided, use equal weights
            if dimension_scores:
                return sum(dimension_scores.values()) / len(dimension_scores)
            return 0
        
        # Calculate weighted average
        total_weighted_score = 0
        total_weight = 0
        
        for dimension, score in dimension_scores.items():
            weight = dimension_weights.get(dimension, 1.0)
            total_weighted_score += score * weight
            total_weight += weight
        
        if total_weight > 0:
            return total_weighted_score / total_weight
        
        return 0
    
    async def _generate_ai_evaluation(
        self,
        questionnaire: Questionnaire,
        dimension_scores: Dict[str, float],
        total_score: float,
        responses: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate AI evaluation using LLM"""
        
        # Prepare answers summary
        answers_summary = self._prepare_answers_summary(questionnaire, responses)
        
        # Call LLM service for evaluation
        evaluation = await llm_service.evaluate_responses(
            questionnaire_title=questionnaire.title,
            position_type=questionnaire.position_type or "通用岗位",
            dimension_scores=dimension_scores,
            answers_summary=answers_summary
        )
        
        # Ensure match_score is within 0-100
        if "match_score" in evaluation:
            evaluation["match_score"] = min(100, max(0, evaluation["match_score"]))
        else:
            # Calculate match score based on total score
            evaluation["match_score"] = total_score
        
        # Ensure risk_level is valid
        valid_risk_levels = ["low", "medium", "high"]
        if evaluation.get("risk_level") not in valid_risk_levels:
            # Determine risk level based on score
            if total_score >= 80:
                evaluation["risk_level"] = "low"
            elif total_score >= 60:
                evaluation["risk_level"] = "medium"
            else:
                evaluation["risk_level"] = "high"
        
        return evaluation
    
    def _prepare_answers_summary(
        self,
        questionnaire: Questionnaire,
        responses: Dict[str, Any]
    ) -> str:
        """Prepare a summary of candidate's answers"""
        
        summary_parts = []
        
        for question in questionnaire.questions[:10]:  # Limit to first 10 for brevity
            response = responses.get(str(question.id))
            if response:
                summary_parts.append(f"Q: {question.title[:50]}... A: {str(response)[:100]}")
        
        return "\n".join(summary_parts)
    
    async def _calculate_percentile_rank(
        self,
        questionnaire_id: int,
        score: float,
        db: AsyncSession
    ) -> float:
        """Calculate percentile rank among all candidates"""
        
        # This would require querying historical evaluation data
        # For now, return a simple estimate based on score
        if score >= 90:
            return 95.0
        elif score >= 80:
            return 85.0
        elif score >= 70:
            return 70.0
        elif score >= 60:
            return 50.0
        else:
            return 30.0
    
    async def compare_candidates(
        self,
        questionnaire_id: int,
        candidate_ids: List[int],
        db: AsyncSession
    ) -> Dict[str, Any]:
        """
        Compare multiple candidates for the same questionnaire
        
        Args:
            questionnaire_id: ID of the questionnaire
            candidate_ids: List of candidate IDs to compare
            db: Database session
        
        Returns:
            Comparison data including rankings and insights
        """
        # Get evaluation reports for all candidates
        reports = []
        for candidate_id in candidate_ids:
            query = db.query(EvaluationReport).filter(
                EvaluationReport.questionnaire_id == questionnaire_id,
                EvaluationReport.candidate_id == candidate_id
            )
            report = await db.execute(query)
            report = report.scalar_one_or_none()
            if report:
                reports.append(report)
        
        if not reports:
            return {"error": "No evaluation reports found"}
        
        # Sort by total score
        reports.sort(key=lambda r: r.total_score, reverse=True)
        
        # Build comparison data
        comparison = {
            "questionnaire_id": questionnaire_id,
            "candidates": [],
            "dimension_comparison": {},
            "insights": []
        }
        
        # Add ranked candidates
        for i, report in enumerate(reports):
            comparison["candidates"].append({
                "rank": i + 1,
                "candidate_id": report.candidate_id,
                "total_score": report.total_score,
                "is_qualified": report.is_qualified,
                "match_score": report.match_score,
                "risk_level": report.risk_level
            })
        
        # Compare dimensions
        all_dimensions = set()
        for report in reports:
            if report.dimension_scores:
                all_dimensions.update(report.dimension_scores.keys())
        
        for dimension in all_dimensions:
            comparison["dimension_comparison"][dimension] = []
            for report in reports:
                score = report.dimension_scores.get(dimension, 0) if report.dimension_scores else 0
                comparison["dimension_comparison"][dimension].append({
                    "candidate_id": report.candidate_id,
                    "score": score
                })
        
        # Generate insights
        if len(reports) >= 2:
            top_candidate = reports[0]
            comparison["insights"].append(
                f"Top candidate scores {top_candidate.total_score:.1f}%, "
                f"{top_candidate.total_score - reports[1].total_score:.1f} points ahead of second place"
            )
        
        return comparison


# Global instance
evaluation_service = EvaluationService()