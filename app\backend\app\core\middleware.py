"""
Performance Monitoring and Optimization Middleware
Tracks API performance metrics and optimizes response times
"""
import time
import logging
from typing import Callable, Dict, List
from collections import deque
from datetime import datetime
import json
import asyncio

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGI<PERSON>pp

from app.core.redis import redis_client

logger = logging.getLogger(__name__)


class PerformanceMonitoringMiddleware(BaseHTTPMiddleware):
    """Middleware for tracking API performance metrics"""
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
        self.metrics_buffer = deque(maxlen=1000)  # Keep last 1000 requests
        self.slow_query_threshold = 1.0  # 1 second
        self.cache_ttl = 300  # 5 minutes
        
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process request and track performance"""
        
        # Start timing
        start_time = time.time()
        
        # Check if response can be cached
        cache_key = None
        if request.method == "GET" and self._is_cacheable(request.url.path):
            cache_key = f"response_cache:{request.url.path}:{request.url.query}"
            cached = await self._get_cached_response(cache_key)
            if cached:
                logger.debug(f"Cache hit for {request.url.path}")
                return Response(
                    content=cached['content'],
                    status_code=cached['status_code'],
                    headers=cached['headers'],
                    media_type=cached['media_type']
                )
        
        # Process request
        response = await call_next(request)
        
        # Calculate metrics
        process_time = time.time() - start_time
        
        # Track metrics
        metric = {
            "path": request.url.path,
            "method": request.method,
            "status": response.status_code,
            "duration": process_time,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        # Add to buffer
        self.metrics_buffer.append(metric)
        
        # Log slow queries
        if process_time > self.slow_query_threshold:
            logger.warning(
                f"Slow API call: {request.method} {request.url.path} "
                f"took {process_time:.2f}s"
            )
            await self._record_slow_query(request, process_time)
        
        # Cache successful GET responses
        if cache_key and response.status_code == 200:
            await self._cache_response(cache_key, response)
        
        # Add performance headers
        response.headers["X-Process-Time"] = str(process_time)
        response.headers["X-Cache-Status"] = "MISS" if cache_key else "SKIP"
        
        # Async persist metrics periodically
        if len(self.metrics_buffer) >= 100:
            asyncio.create_task(self._persist_metrics())
        
        return response
    
    def _is_cacheable(self, path: str) -> bool:
        """Check if path should be cached"""
        cacheable_patterns = [
            "/api/v1/positions/",
            "/api/v1/candidates/",
            "/api/v1/assessments/",
            "/api/v1/statistics/"
        ]
        return any(path.startswith(pattern) for pattern in cacheable_patterns)
    
    async def _get_cached_response(self, key: str) -> Optional[Dict]:
        """Get cached response"""
        try:
            data = await redis_client.get(key)
            if data:
                return json.loads(data)
        except Exception as e:
            logger.error(f"Cache retrieval failed: {str(e)}")
        return None
    
    async def _cache_response(self, key: str, response: Response):
        """Cache response for future use"""
        try:
            # Read response body
            body = b""
            async for chunk in response.body_iterator:
                body += chunk
            
            # Cache the response
            cache_data = {
                "content": body.decode('utf-8'),
                "status_code": response.status_code,
                "headers": dict(response.headers),
                "media_type": response.media_type
            }
            
            await redis_client.setex(
                key,
                self.cache_ttl,
                json.dumps(cache_data)
            )
            
            # Reset response body
            response.body_iterator = self._generate_body(body)
            
        except Exception as e:
            logger.error(f"Response caching failed: {str(e)}")
    
    async def _generate_body(self, body: bytes):
        """Generator for response body"""
        yield body
    
    async def _record_slow_query(self, request: Request, duration: float):
        """Record slow query for analysis"""
        try:
            slow_query = {
                "path": request.url.path,
                "method": request.method,
                "duration": duration,
                "timestamp": datetime.utcnow().isoformat(),
                "query_params": str(request.url.query)
            }
            
            # Store in Redis list
            await redis_client.lpush(
                "slow_queries",
                json.dumps(slow_query)
            )
            
            # Keep only last 100 slow queries
            await redis_client.ltrim("slow_queries", 0, 99)
            
        except Exception as e:
            logger.error(f"Failed to record slow query: {str(e)}")
    
    async def _persist_metrics(self):
        """Persist metrics to storage"""
        try:
            if not self.metrics_buffer:
                return
            
            # Calculate statistics
            durations = [m['duration'] for m in self.metrics_buffer]
            durations.sort()
            
            stats = {
                "timestamp": datetime.utcnow().isoformat(),
                "total_requests": len(durations),
                "avg_duration": sum(durations) / len(durations),
                "min_duration": min(durations),
                "max_duration": max(durations),
                "p50_duration": self._percentile(durations, 50),
                "p95_duration": self._percentile(durations, 95),
                "p99_duration": self._percentile(durations, 99)
            }
            
            # Store in Redis
            await redis_client.hset(
                "performance_stats",
                datetime.utcnow().strftime("%Y%m%d%H"),
                json.dumps(stats)
            )
            
            logger.info(
                f"Performance stats - Avg: {stats['avg_duration']:.3f}s, "
                f"P95: {stats['p95_duration']:.3f}s"
            )
            
        except Exception as e:
            logger.error(f"Failed to persist metrics: {str(e)}")
    
    def _percentile(self, values: List[float], percentile: int) -> float:
        """Calculate percentile value"""
        if not values:
            return 0.0
        
        index = int(len(values) * percentile / 100)
        if index >= len(values):
            index = len(values) - 1
        
        return values[index]
    
    async def get_performance_report(self) -> Dict:
        """Generate performance report"""
        
        if not self.metrics_buffer:
            return {"message": "No metrics available"}
        
        # Group by endpoint
        endpoint_stats = {}
        for metric in self.metrics_buffer:
            path = metric['path']
            if path not in endpoint_stats:
                endpoint_stats[path] = []
            endpoint_stats[path].append(metric['duration'])
        
        # Calculate stats per endpoint
        report = {
            "summary": {
                "total_requests": len(self.metrics_buffer),
                "endpoints_monitored": len(endpoint_stats),
                "monitoring_period": "Last 1000 requests"
            },
            "endpoints": {}
        }
        
        for path, durations in endpoint_stats.items():
            durations.sort()
            report["endpoints"][path] = {
                "requests": len(durations),
                "avg_ms": int(sum(durations) * 1000 / len(durations)),
                "min_ms": int(min(durations) * 1000),
                "max_ms": int(max(durations) * 1000),
                "p50_ms": int(self._percentile(durations, 50) * 1000),
                "p95_ms": int(self._percentile(durations, 95) * 1000),
                "p99_ms": int(self._percentile(durations, 99) * 1000)
            }
        
        # Overall statistics
        all_durations = [m['duration'] for m in self.metrics_buffer]
        all_durations.sort()
        
        report["overall"] = {
            "avg_ms": int(sum(all_durations) * 1000 / len(all_durations)),
            "p50_ms": int(self._percentile(all_durations, 50) * 1000),
            "p95_ms": int(self._percentile(all_durations, 95) * 1000),
            "p99_ms": int(self._percentile(all_durations, 99) * 1000)
        }
        
        return report


class RequestOptimizationMiddleware(BaseHTTPMiddleware):
    """Middleware for optimizing request processing"""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Optimize request processing"""
        
        # Add request ID for tracing
        request_id = f"{time.time():.6f}"
        request.state.request_id = request_id
        
        # Enable connection pooling hints
        if hasattr(request.state, 'db'):
            request.state.db.execute("SET statement_timeout = '30s'")
        
        # Process request
        response = await call_next(request)
        
        # Add optimization headers
        response.headers["X-Request-ID"] = request_id
        
        # Enable compression for large responses
        if int(response.headers.get("content-length", 0)) > 1024:
            response.headers["Content-Encoding"] = "gzip"
        
        return response


class SecurityMiddleware(BaseHTTPMiddleware):
    """Middleware for security enhancements"""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Add security headers and checks"""
        
        # Process request
        response = await call_next(request)
        
        # Add security headers
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
        
        return response