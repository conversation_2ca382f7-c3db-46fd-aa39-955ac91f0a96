"""
ID类型定义和转换工具
解决系统中Snowflake ID与UUID不一致的问题
"""
from typing import Union, Any, Optional, Annotated
from pydantic import Field, validator, conint


# Use Annotated with conint for Pydantic v2 compatibility
SnowflakeID = Annotated[int, conint(ge=0, le=2**63 - 1)]


def normalize_id(id_value: Union[int, str, None]) -> Optional[int]:
    """
    标准化ID值为整数
    
    Args:
        id_value: ID值，可以是int、str或None
        
    Returns:
        标准化后的整数ID，如果输入为None则返回None
        
    Raises:
        ValueError: 如果字符串无法转换为整数
    """
    if id_value is None:
        return None
    
    if isinstance(id_value, int):
        return id_value
    
    if isinstance(id_value, str):
        # 移除空格
        id_value = id_value.strip()
        
        # 空字符串返回None
        if not id_value:
            return None
        
        try:
            # 尝试转换为整数
            return int(id_value)
        except ValueError:
            # 如果不是纯数字，生成一个基于字符串的ID
            # 使用hash函数生成一个稳定的整数ID
            import hashlib
            hash_obj = hashlib.sha256(id_value.encode())
            # 取前8字节作为ID
            return int.from_bytes(hash_obj.digest()[:8], byteorder='big') & 0x7FFFFFFFFFFFFFFF
    
    # 其他类型尝试强制转换
    try:
        return int(id_value)
    except (ValueError, TypeError):
        return None


class IDField:
    """
    Pydantic字段工厂，用于创建ID字段
    """
    
    @staticmethod
    def create(
        description: str = "Entity ID",
        example: Union[int, str] = "7123456789012345678",
        alias: Optional[str] = None
    ):
        """
        创建一个ID字段定义
        
        Args:
            description: 字段描述
            example: 示例值
            alias: 字段别名
            
        Returns:
            Pydantic Field定义
        """
        return Field(
            default=None,
            description=description,
            example=example,
            alias=alias,
            # JSON Schema定义
            json_schema_extra={
                "type": ["string", "integer"],
                "pattern": "^[0-9]+$",  # 如果是字符串，必须是数字
                "minimum": 0,
                "maximum": 2**63 - 1
            }
        )


class IDMixin:
    """
    ID字段Mixin，提供ID验证和转换功能
    """
    
    @validator('*', pre=True)
    def validate_id_fields(cls, v, field):
        """
        自动验证和转换所有ID字段
        """
        # 检查字段名是否包含'id'
        if field.name.endswith('_id') or field.name == 'id':
            return normalize_id(v)
        return v
    
    class Config:
        # JSON编码器，将大整数转换为字符串
        json_encoders = {
            int: lambda v: str(v) if v and v > 2**53 else v
        }
        
        # 允许字段使用别名
        allow_population_by_field_name = True
        
        # Schema示例
        schema_extra = {
            "example": {
                "id": "7123456789012345678",
                "name": "Example Entity"
            }
        }


def is_valid_snowflake_id(value: Any) -> bool:
    """
    检查值是否为有效的Snowflake ID
    
    Args:
        value: 要检查的值
        
    Returns:
        是否为有效的Snowflake ID
    """
    if value is None:
        return False
    
    try:
        id_int = normalize_id(value)
        return id_int is not None and 0 <= id_int <= 2**63 - 1
    except:
        return False


def generate_snowflake_id() -> int:
    """
    生成简单的Snowflake ID
    
    注意：这是一个简化版本，仅用于开发环境
    生产环境应该使用真正的分布式Snowflake生成器
    
    Returns:
        生成的Snowflake ID
    """
    import time
    import random
    
    # 时间戳 (41位) + 机器ID (10位) + 序列号 (12位)
    timestamp = int(time.time() * 1000) - 1609459200000  # 从2021-01-01开始
    machine_id = random.randint(0, 1023)  # 10位机器ID
    sequence = random.randint(0, 4095)    # 12位序列号
    
    # 组合生成Snowflake ID
    snowflake_id = (timestamp << 22) | (machine_id << 12) | sequence
    
    return snowflake_id


def create_id_validator(field_name: str = "id"):
    """
    创建ID验证器装饰器
    
    Args:
        field_name: 要验证的字段名
        
    Returns:
        验证器装饰器
    """
    def validator_func(cls, v):
        """验证ID字段"""
        if v is None:
            return None
        
        normalized = normalize_id(v)
        if normalized is None:
            raise ValueError(f"Invalid {field_name}: {v}")
        
        return normalized
    
    return validator(field_name, pre=True, allow_reuse=True)(validator_func)


# 导出常用类型定义
IDType = Union[int, str]
OptionalID = Optional[IDType]


__all__ = [
    'SnowflakeID',
    'normalize_id',
    'generate_snowflake_id',
    'IDField',
    'IDMixin',
    'is_valid_snowflake_id',
    'create_id_validator',
    'IDType',
    'OptionalID'
]