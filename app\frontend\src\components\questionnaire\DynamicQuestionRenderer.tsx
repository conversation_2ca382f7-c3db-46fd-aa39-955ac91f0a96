'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Checkbox } from '@/components/ui/checkbox';
import { Slider } from '@/components/ui/slider';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  ChevronLeft, 
  ChevronRight, 
  Save,
  AlertCircle,
  CheckCircle,
  Clock,
  Star,
  ThumbsUp,
  ThumbsDown,
} from 'lucide-react';
import { cn } from '@/lib/utils';

export interface Question {
  id: string;
  title: string;
  description?: string;
  type: 'single_choice' | 'multiple_choice' | 'text_input' | 'text_area' | 
        'rating_scale' | 'ranking' | 'matrix' | 'date' | 'number' | 'yes_no' |
        'likert_scale' | 'slider' | 'file_upload';
  required: boolean;
  options?: Array<{
    value: string;
    label: string;
    text: string;
    score?: number;
  }>;
  validation?: {
    min?: number;
    max?: number;
    minLength?: number;
    maxLength?: number;
    pattern?: string;
    customMessage?: string;
  };
  conditionalLogic?: {
    showIf?: {
      questionId: string;
      operator: 'equals' | 'not_equals' | 'contains' | 'greater_than' | 'less_than';
      value: any;
    };
    skipTo?: string;
  };
  dimension?: string;
  order?: number;
  helpText?: string;
  placeholder?: string;
  matrixRows?: string[];
  matrixColumns?: string[];
  allowOther?: boolean;
  otherLabel?: string;
  randomizeOptions?: boolean;
}

interface DynamicQuestionRendererProps {
  questions: Question[];
  onSubmit: (responses: Record<string, any>) => void;
  onSave?: (responses: Record<string, any>) => void;
  initialResponses?: Record<string, any>;
  showProgress?: boolean;
  allowNavigation?: boolean;
  autoSave?: boolean;
  autoSaveInterval?: number;
  mode?: 'single' | 'paged' | 'sections';
  onPageChange?: (page: number) => void;
  className?: string;
}

export function DynamicQuestionRenderer({
  questions,
  onSubmit,
  onSave,
  initialResponses = {},
  showProgress = true,
  allowNavigation = true,
  autoSave = false,
  autoSaveInterval = 30000, // 30 seconds
  mode = 'paged',
  onPageChange,
  className,
}: DynamicQuestionRendererProps) {
  const [responses, setResponses] = useState<Record<string, any>>(initialResponses);
  const [currentPage, setCurrentPage] = useState(0);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [touched, setTouched] = useState<Record<string, boolean>>({});
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [visibleQuestions, setVisibleQuestions] = useState<Question[]>([]);

  // Apply conditional logic to determine visible questions
  useEffect(() => {
    const evaluateConditions = () => {
      const visible = questions.filter(question => {
        if (!question.conditionalLogic?.showIf) return true;
        
        const { questionId, operator, value } = question.conditionalLogic.showIf;
        const response = responses[questionId];
        
        switch (operator) {
          case 'equals':
            return response === value;
          case 'not_equals':
            return response !== value;
          case 'contains':
            return response?.includes?.(value);
          case 'greater_than':
            return Number(response) > Number(value);
          case 'less_than':
            return Number(response) < Number(value);
          default:
            return true;
        }
      });
      
      setVisibleQuestions(visible);
    };
    
    evaluateConditions();
  }, [questions, responses]);

  // Auto-save functionality
  useEffect(() => {
    if (!autoSave || !onSave) return;
    
    const interval = setInterval(() => {
      handleSave();
    }, autoSaveInterval);
    
    return () => clearInterval(interval);
  }, [autoSave, autoSaveInterval, responses]);

  const handleSave = useCallback(async () => {
    if (!onSave || isSaving) return;
    
    setIsSaving(true);
    try {
      await onSave(responses);
      setLastSaved(new Date());
    } catch (error) {
      console.error('Failed to save responses:', error);
    } finally {
      setIsSaving(false);
    }
  }, [responses, onSave, isSaving]);

  const validateQuestion = (question: Question, value: any): string | null => {
    if (question.required && !value) {
      return question.validation?.customMessage || '此题为必答题';
    }
    
    if (question.validation) {
      const { min, max, minLength, maxLength, pattern } = question.validation;
      
      if (min !== undefined && Number(value) < min) {
        return `最小值为 ${min}`;
      }
      if (max !== undefined && Number(value) > max) {
        return `最大值为 ${max}`;
      }
      if (minLength !== undefined && value?.length < minLength) {
        return `最少需要 ${minLength} 个字符`;
      }
      if (maxLength !== undefined && value?.length > maxLength) {
        return `最多允许 ${maxLength} 个字符`;
      }
      if (pattern && !new RegExp(pattern).test(value)) {
        return question.validation.customMessage || '格式不正确';
      }
    }
    
    return null;
  };

  const handleResponseChange = (questionId: string, value: any) => {
    setResponses(prev => ({ ...prev, [questionId]: value }));
    setTouched(prev => ({ ...prev, [questionId]: true }));
    
    // Clear error when user starts typing
    if (errors[questionId]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[questionId];
        return newErrors;
      });
    }
  };

  const handleNext = () => {
    // Validate current page questions
    const currentQuestions = getQuestionsForPage(currentPage);
    const newErrors: Record<string, string> = {};
    
    currentQuestions.forEach(question => {
      const error = validateQuestion(question, responses[question.id]);
      if (error) {
        newErrors[question.id] = error;
      }
    });
    
    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }
    
    if (currentPage < getTotalPages() - 1) {
      setCurrentPage(currentPage + 1);
      onPageChange?.(currentPage + 1);
    }
  };

  const handlePrevious = () => {
    if (currentPage > 0) {
      setCurrentPage(currentPage - 1);
      onPageChange?.(currentPage - 1);
    }
  };

  const handleSubmitForm = () => {
    // Validate all questions
    const allErrors: Record<string, string> = {};
    
    visibleQuestions.forEach(question => {
      const error = validateQuestion(question, responses[question.id]);
      if (error) {
        allErrors[question.id] = error;
      }
    });
    
    if (Object.keys(allErrors).length > 0) {
      setErrors(allErrors);
      // Go to first page with error
      const firstErrorQuestion = visibleQuestions.find(q => allErrors[q.id]);
      if (firstErrorQuestion && mode === 'paged') {
        const pageIndex = Math.floor(visibleQuestions.indexOf(firstErrorQuestion) / getQuestionsPerPage());
        setCurrentPage(pageIndex);
      }
      return;
    }
    
    onSubmit(responses);
  };

  const getQuestionsPerPage = () => {
    if (mode === 'single') return visibleQuestions.length;
    if (mode === 'paged') return 1;
    return 5; // For sections mode
  };

  const getTotalPages = () => {
    return Math.ceil(visibleQuestions.length / getQuestionsPerPage());
  };

  const getQuestionsForPage = (page: number) => {
    const questionsPerPage = getQuestionsPerPage();
    const start = page * questionsPerPage;
    const end = start + questionsPerPage;
    return visibleQuestions.slice(start, end);
  };

  const renderQuestion = (question: Question) => {
    const value = responses[question.id];
    const error = errors[question.id];
    const isTouched = touched[question.id];
    
    return (
      <div key={question.id} className="space-y-4">
        <div>
          <Label htmlFor={question.id} className="text-base font-medium">
            {question.title}
            {question.required && <span className="text-red-500 ml-1">*</span>}
          </Label>
          {question.description && (
            <p className="text-sm text-muted-foreground mt-1">{question.description}</p>
          )}
          {question.helpText && (
            <p className="text-xs text-muted-foreground mt-1 italic">{question.helpText}</p>
          )}
        </div>
        
        <div className="space-y-2">
          {renderQuestionInput(question, value)}
          
          {error && isTouched && (
            <Alert variant="destructive" className="py-2">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
        </div>
      </div>
    );
  };

  const renderQuestionInput = (question: Question, value: any) => {
    switch (question.type) {
      case 'single_choice':
        return (
          <RadioGroup
            value={value || ''}
            onValueChange={(val) => handleResponseChange(question.id, val)}
          >
            {question.options?.map((option) => (
              <div key={option.value} className="flex items-center space-x-2">
                <RadioGroupItem value={option.value} id={`${question.id}-${option.value}`} />
                <Label
                  htmlFor={`${question.id}-${option.value}`}
                  className="font-normal cursor-pointer"
                >
                  {option.text}
                </Label>
              </div>
            ))}
            {question.allowOther && (
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="other" id={`${question.id}-other`} />
                <Input
                  placeholder={question.otherLabel || '其他'}
                  value={value === 'other' ? responses[`${question.id}_other`] : ''}
                  onChange={(e) => {
                    handleResponseChange(question.id, 'other');
                    handleResponseChange(`${question.id}_other`, e.target.value);
                  }}
                  className="flex-1"
                />
              </div>
            )}
          </RadioGroup>
        );
        
      case 'multiple_choice':
        return (
          <div className="space-y-2">
            {question.options?.map((option) => (
              <div key={option.value} className="flex items-center space-x-2">
                <Checkbox
                  id={`${question.id}-${option.value}`}
                  checked={(value || []).includes(option.value)}
                  onCheckedChange={(checked) => {
                    const currentValues = value || [];
                    const newValues = checked
                      ? [...currentValues, option.value]
                      : currentValues.filter((v: string) => v !== option.value);
                    handleResponseChange(question.id, newValues);
                  }}
                />
                <Label
                  htmlFor={`${question.id}-${option.value}`}
                  className="font-normal cursor-pointer"
                >
                  {option.text}
                </Label>
              </div>
            ))}
          </div>
        );
        
      case 'text_input':
        return (
          <Input
            id={question.id}
            value={value || ''}
            onChange={(e) => handleResponseChange(question.id, e.target.value)}
            placeholder={question.placeholder}
            maxLength={question.validation?.maxLength}
          />
        );
        
      case 'text_area':
        return (
          <Textarea
            id={question.id}
            value={value || ''}
            onChange={(e) => handleResponseChange(question.id, e.target.value)}
            placeholder={question.placeholder}
            rows={4}
            maxLength={question.validation?.maxLength}
          />
        );
        
      case 'rating_scale':
        return (
          <div className="flex items-center space-x-2">
            {[1, 2, 3, 4, 5].map((rating) => (
              <Button
                key={rating}
                type="button"
                variant={value === rating ? 'default' : 'outline'}
                size="sm"
                onClick={() => handleResponseChange(question.id, rating)}
                className="w-10 h-10"
              >
                {rating}
              </Button>
            ))}
          </div>
        );
        
      case 'likert_scale':
        return (
          <RadioGroup
            value={value || ''}
            onValueChange={(val) => handleResponseChange(question.id, val)}
            className="grid grid-cols-5 gap-2"
          >
            {[
              { value: '1', label: '非常不同意' },
              { value: '2', label: '不同意' },
              { value: '3', label: '中立' },
              { value: '4', label: '同意' },
              { value: '5', label: '非常同意' },
            ].map((item) => (
              <div key={item.value} className="text-center">
                <RadioGroupItem
                  value={item.value}
                  id={`${question.id}-${item.value}`}
                  className="mx-auto"
                />
                <Label
                  htmlFor={`${question.id}-${item.value}`}
                  className="text-xs mt-1 block cursor-pointer"
                >
                  {item.label}
                </Label>
              </div>
            ))}
          </RadioGroup>
        );
        
      case 'slider':
        const sliderMin = question.validation?.min || 0;
        const sliderMax = question.validation?.max || 100;
        return (
          <div className="space-y-2">
            <Slider
              value={[value || sliderMin]}
              onValueChange={(values) => handleResponseChange(question.id, values[0])}
              min={sliderMin}
              max={sliderMax}
              step={1}
              className="w-full"
            />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>{sliderMin}</span>
              <span className="font-medium text-foreground">{value || sliderMin}</span>
              <span>{sliderMax}</span>
            </div>
          </div>
        );
        
      case 'yes_no':
        return (
          <RadioGroup
            value={value || ''}
            onValueChange={(val) => handleResponseChange(question.id, val)}
            className="flex space-x-4"
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="yes" id={`${question.id}-yes`} />
              <Label htmlFor={`${question.id}-yes`} className="cursor-pointer">
                是
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="no" id={`${question.id}-no`} />
              <Label htmlFor={`${question.id}-no`} className="cursor-pointer">
                否
              </Label>
            </div>
          </RadioGroup>
        );
        
      case 'number':
        return (
          <Input
            id={question.id}
            type="number"
            value={value || ''}
            onChange={(e) => handleResponseChange(question.id, e.target.value)}
            placeholder={question.placeholder}
            min={question.validation?.min}
            max={question.validation?.max}
          />
        );
        
      case 'date':
        return (
          <Input
            id={question.id}
            type="date"
            value={value || ''}
            onChange={(e) => handleResponseChange(question.id, e.target.value)}
          />
        );
        
      case 'matrix':
        return (
          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr>
                  <th className="text-left p-2 border-b"></th>
                  {question.matrixColumns?.map((col) => (
                    <th key={col} className="text-center p-2 border-b text-sm">
                      {col}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {question.matrixRows?.map((row) => (
                  <tr key={row}>
                    <td className="p-2 border-b text-sm">{row}</td>
                    {question.matrixColumns?.map((col) => (
                      <td key={col} className="text-center p-2 border-b">
                        <RadioGroup
                          value={value?.[row] || ''}
                          onValueChange={(val) => {
                            const newValue = { ...(value || {}), [row]: val };
                            handleResponseChange(question.id, newValue);
                          }}
                        >
                          <RadioGroupItem
                            value={col}
                            className="mx-auto"
                          />
                        </RadioGroup>
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        );
        
      case 'ranking':
        // Simplified ranking - would need drag-and-drop for full implementation
        return (
          <div className="space-y-2">
            {question.options?.map((option, index) => (
              <div key={option.value} className="flex items-center space-x-2">
                <Input
                  type="number"
                  min="1"
                  max={question.options?.length}
                  value={value?.[option.value] || ''}
                  onChange={(e) => {
                    const newValue = { ...(value || {}), [option.value]: e.target.value };
                    handleResponseChange(question.id, newValue);
                  }}
                  className="w-16"
                />
                <Label className="flex-1">{option.text}</Label>
              </div>
            ))}
          </div>
        );
        
      default:
        return <div>Unsupported question type: {question.type}</div>;
    }
  };

  const currentQuestions = getQuestionsForPage(currentPage);
  const totalPages = getTotalPages();
  const progress = ((currentPage + 1) / totalPages) * 100;

  return (
    <div className={cn('space-y-6', className)}>
      {/* Progress Bar */}
      {showProgress && mode === 'paged' && (
        <div className="space-y-2">
          <div className="flex justify-between text-sm text-muted-foreground">
            <span>进度</span>
            <span>
              {currentPage + 1} / {totalPages}
            </span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>
      )}
      
      {/* Auto-save indicator */}
      {autoSave && lastSaved && (
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <Clock className="h-3 w-3" />
          <span>
            {isSaving ? '保存中...' : `已于 ${lastSaved.toLocaleTimeString()} 自动保存`}
          </span>
        </div>
      )}
      
      {/* Questions */}
      <Card>
        <CardContent className="pt-6">
          <div className="space-y-6">
            {currentQuestions.map((question) => renderQuestion(question))}
          </div>
        </CardContent>
      </Card>
      
      {/* Navigation */}
      <div className="flex justify-between">
        <Button
          variant="outline"
          onClick={handlePrevious}
          disabled={currentPage === 0}
          className={cn(!allowNavigation && 'invisible')}
        >
          <ChevronLeft className="h-4 w-4 mr-1" />
          上一页
        </Button>
        
        <div className="flex gap-2">
          {onSave && (
            <Button variant="outline" onClick={handleSave} disabled={isSaving}>
              <Save className="h-4 w-4 mr-1" />
              保存
            </Button>
          )}
          
          {currentPage === totalPages - 1 ? (
            <Button onClick={handleSubmitForm}>
              <CheckCircle className="h-4 w-4 mr-1" />
              提交
            </Button>
          ) : (
            <Button onClick={handleNext}>
              下一页
              <ChevronRight className="h-4 w-4 ml-1" />
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}