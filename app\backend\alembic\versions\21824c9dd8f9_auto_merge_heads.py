"""auto_merge_heads

Revision ID: 21824c9dd8f9
Revises: 13ce126adc2e
Create Date: 2025-08-12 05:01:54.134145

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '21824c9dd8f9'
down_revision: Union[str, None] = '13ce126adc2e'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass