"""Add user_preferences table for i18n settings

Revision ID: b37b7a4e0c04
Revises: 20250804_120000
Create Date: 2025-08-06 09:12:44.430031

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'b37b7a4e0c04'
down_revision: Union[str, None] = '20250804_120000'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('user_preferences',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('locale', sa.String(length=10), nullable=False),
    sa.Column('timezone', sa.String(length=50), nullable=False),
    sa.Column('date_format', sa.String(length=20), nullable=False),
    sa.Column('time_format', sa.String(length=10), nullable=False),
    sa.Column('number_format', sa.String(length=10), nullable=False),
    sa.Column('currency', sa.String(length=3), nullable=False),
    sa.Column('theme', sa.String(length=10), nullable=False),
    sa.Column('email_notifications', sa.Boolean(), nullable=False),
    sa.Column('push_notifications', sa.Boolean(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], name=op.f('fk_user_preferences_user_id_users'), ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_user_preferences')),
    sa.UniqueConstraint('user_id', name=op.f('uq_user_preferences_user_id'))
    )
    op.create_index(op.f('ix_user_preferences_id'), 'user_preferences', ['id'], unique=False)
    op.alter_column('candidates', 'birth_date',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True)
    op.alter_column('candidates', 'status',
               existing_type=postgresql.ENUM('new', 'screening', 'interview', 'offer', 'hired', 'rejected', 'withdrawn', name='candidatestatus'),
               nullable=True)
    op.alter_column('candidates', 'data_permission',
               existing_type=postgresql.ENUM('PRIVATE', 'SHARED', 'TEAM', 'PUBLIC', name='datapermission'),
               nullable=True)
    op.alter_column('candidates', 'is_deleted',
               existing_type=sa.BOOLEAN(),
               nullable=True)
    op.drop_index(op.f('idx_candidate_name'), table_name='candidates')
    op.drop_index(op.f('idx_candidate_shared_with'), table_name='candidates', postgresql_using='gin')
    op.drop_index(op.f('idx_candidate_tags'), table_name='candidates', postgresql_using='gin')
    op.create_index('idx_candidate_name_search', 'candidates', ['name'], unique=False)
    op.create_index(op.f('ix_candidates_email'), 'candidates', ['email'], unique=False)
    op.create_index(op.f('ix_candidates_name'), 'candidates', ['name'], unique=False)
    op.create_index(op.f('ix_candidates_phone'), 'candidates', ['phone'], unique=False)
    op.alter_column('users', 'role',
               existing_type=sa.VARCHAR(length=50),
               server_default=None,
               existing_nullable=False)
    op.alter_column('users', 'is_active',
               existing_type=sa.BOOLEAN(),
               server_default=None,
               existing_nullable=False)
    op.alter_column('users', 'is_superuser',
               existing_type=sa.BOOLEAN(),
               server_default=None,
               existing_nullable=False)
    op.alter_column('users', 'is_verified',
               existing_type=sa.BOOLEAN(),
               server_default=None,
               existing_nullable=False)
    op.alter_column('users', 'failed_login_attempts',
               existing_type=sa.INTEGER(),
               server_default=None,
               existing_nullable=False)
    op.drop_index(op.f('idx_users_created_at'), table_name='users')
    op.drop_index(op.f('idx_users_email'), table_name='users')
    op.drop_index(op.f('idx_users_is_active'), table_name='users')
    op.drop_index(op.f('idx_users_last_login_at'), table_name='users')
    op.drop_index(op.f('idx_users_locked_until'), table_name='users')
    op.drop_index(op.f('idx_users_role'), table_name='users')
    op.drop_index(op.f('idx_users_username'), table_name='users')
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=True)
    op.create_index(op.f('ix_users_username'), 'users', ['username'], unique=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_users_username'), table_name='users')
    op.drop_index(op.f('ix_users_email'), table_name='users')
    op.create_index(op.f('idx_users_username'), 'users', ['username'], unique=True)
    op.create_index(op.f('idx_users_role'), 'users', ['role'], unique=False)
    op.create_index(op.f('idx_users_locked_until'), 'users', ['locked_until'], unique=False)
    op.create_index(op.f('idx_users_last_login_at'), 'users', ['last_login_at'], unique=False)
    op.create_index(op.f('idx_users_is_active'), 'users', ['is_active'], unique=False)
    op.create_index(op.f('idx_users_email'), 'users', ['email'], unique=True)
    op.create_index(op.f('idx_users_created_at'), 'users', ['created_at'], unique=False)
    op.alter_column('users', 'failed_login_attempts',
               existing_type=sa.INTEGER(),
               server_default=sa.text('0'),
               existing_nullable=False)
    op.alter_column('users', 'is_verified',
               existing_type=sa.BOOLEAN(),
               server_default=sa.text('false'),
               existing_nullable=False)
    op.alter_column('users', 'is_superuser',
               existing_type=sa.BOOLEAN(),
               server_default=sa.text('false'),
               existing_nullable=False)
    op.alter_column('users', 'is_active',
               existing_type=sa.BOOLEAN(),
               server_default=sa.text('true'),
               existing_nullable=False)
    op.alter_column('users', 'role',
               existing_type=sa.VARCHAR(length=50),
               server_default=sa.text("'hr_specialist'::character varying"),
               existing_nullable=False)
    op.drop_index(op.f('ix_candidates_phone'), table_name='candidates')
    op.drop_index(op.f('ix_candidates_name'), table_name='candidates')
    op.drop_index(op.f('ix_candidates_email'), table_name='candidates')
    op.drop_index('idx_candidate_name_search', table_name='candidates')
    op.create_index(op.f('idx_candidate_tags'), 'candidates', ['tags'], unique=False, postgresql_using='gin')
    op.create_index(op.f('idx_candidate_shared_with'), 'candidates', ['shared_with'], unique=False, postgresql_using='gin')
    op.create_index(op.f('idx_candidate_name'), 'candidates', ['name'], unique=False)
    op.alter_column('candidates', 'is_deleted',
               existing_type=sa.BOOLEAN(),
               nullable=False)
    op.alter_column('candidates', 'data_permission',
               existing_type=postgresql.ENUM('PRIVATE', 'SHARED', 'TEAM', 'PUBLIC', name='datapermission'),
               nullable=False)
    op.alter_column('candidates', 'status',
               existing_type=postgresql.ENUM('new', 'screening', 'interview', 'offer', 'hired', 'rejected', 'withdrawn', name='candidatestatus'),
               nullable=False)
    op.alter_column('candidates', 'birth_date',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               existing_nullable=True)
    op.drop_index(op.f('ix_user_preferences_id'), table_name='user_preferences')
    op.drop_table('user_preferences')
    # ### end Alembic commands ###