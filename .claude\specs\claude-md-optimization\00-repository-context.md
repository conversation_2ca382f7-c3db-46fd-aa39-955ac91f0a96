# Repository Context Analysis - TalentForge Pro

## Project Overview

**TalentForge Pro** is an intelligent talent assessment and position matching system using a hybrid architecture (rule engine + LLM). This is a comprehensive full-stack application with microservices architecture, containerized deployment, and advanced ML/AI capabilities.

### Project Type and Purpose
- **Type**: Enterprise SaaS Web Application
- **Domain**: Human Resources Technology (HR Tech)
- **Purpose**: Intelligent talent evaluation, candidate-position matching, and recruitment optimization
- **Architecture**: Microservices with AI/ML integration
- **Deployment**: Docker-based containerization with production-ready infrastructure

## Technology Stack Summary

### Backend Technologies
- **Runtime**: Python 3.12
- **Framework**: FastAPI 0.110+ (async/await)
- **Database**: PostgreSQL 17 + pgvector 0.8.0 (vector database)
- **Cache/Queue**: Redis 7.4.4-alpine
- **ORM**: SQLAlchemy 2.0 (async)
- **Migrations**: Alembic 1.13.0
- **Authentication**: JWT (python-jose) + bcrypt
- **Task Queue**: Celery 5.4.0
- **Package Manager**: Poetry

### Frontend Technologies
- **Framework**: Next.js 15.4.1 (App Router)
- **Runtime**: React 19.0.0
- **Language**: TypeScript 5.5.0
- **Styling**: Tailwind CSS 3.4.1
- **UI Components**: Radix UI + shadcn/ui
- **State Management**: Redux Toolkit + TanStack Query 5.50.0
- **Forms**: React Hook Form + Zod validation
- **Package Manager**: pnpm 10.13.1
- **Icons**: Lucide React
- **Internationalization**: next-intl 4.3.4

### AI/ML Stack
- **Vector Database**: pgvector (PostgreSQL extension)
- **Embedding Service**: Ollama 0.11.4 (containerized)
- **Models**: BGE-M3 (1024-dim), OpenAI text-embedding-3-small (1536-dim backup)
- **LLM Integration**: Ollama (local), OpenAI API (backup)
- **Vector Operations**: Cosine similarity, HNSW indexing

### Infrastructure & DevOps
- **Containerization**: Docker + Docker Compose
- **Reverse Proxy**: Nginx (Alpine)
- **Object Storage**: MinIO (S3-compatible)
- **Monitoring**: Prometheus 3.5.0 (optional)
- **GPU Support**: NVIDIA Docker runtime (for Ollama)
- **Build System**: Multi-stage Docker builds
- **Environment Management**: Environment-aware Makefile

### Development Tools
- **Backend Linting**: Ruff, Black, MyPy
- **Frontend Linting**: ESLint, Prettier
- **Testing**: pytest (backend), Jest + Playwright (frontend)
- **Code Quality**: Pre-commit hooks, Type checking
- **Documentation**: OpenAPI/Swagger, Comprehensive markdown docs

## Project Structure and Organization

### Root Directory Structure
```
talent_forge_pro/
├── app/                    # Main application directory
│   ├── backend/           # FastAPI backend service
│   ├── frontend/          # Next.js frontend application
│   ├── configs/           # Configuration files (nginx, prometheus)
│   ├── scripts/           # Development and deployment scripts
│   └── docker-compose.yml # Container orchestration
├── docs/                  # Project documentation
├── examples/              # Code examples and patterns
├── archive/               # Archived/legacy files
├── PRPs/                  # Project Requirements and Process files
├── tools/                 # Testing and utility tools
├── use-cases/             # Specific use case implementations
├── CLAUDE.md             # Main project documentation
├── Makefile              # Development workflow automation
└── LICENSE               # Project license
```

### Backend Architecture (app/backend/)
```
app/
├── api/                   # API layer
├── core/                  # Core configuration and utilities
├── crud/                  # Data access layer
├── models/                # SQLAlchemy models
├── schemas/               # Pydantic schemas
├── services/              # Business logic layer
├── tasks.py              # Celery tasks
├── main.py               # FastAPI application entry
└── worker.py             # Celery worker
```

### Frontend Architecture (app/frontend/)
```
app/
├── (auth)/               # Authentication pages
├── (dashboard)/          # Dashboard layouts
├── [locale]/             # Internationalization routing
├── i18n/                 # Internationalization system
├── components/           # React components
├── services/             # API client services
├── stores/               # Redux state management
├── types/                # TypeScript type definitions
├── hooks/                # Custom React hooks
├── lib/                  # Utility libraries
├── messages/             # Translation files
└── middleware.ts         # Next.js middleware
```

## Code Organization Patterns

### Backend Patterns

#### Layered Architecture
- **API Layer** (`api/`): Route handlers, request/response processing
- **Service Layer** (`services/`): Business logic, orchestration
- **CRUD Layer** (`crud/`): Data access patterns
- **Model Layer** (`models/`): Database entities

#### Established Conventions
```python
# Service pattern
class CandidateService:
    async def create_candidate(self, data: CandidateCreate) -> CandidateResponse:
        # Business logic implementation
        pass

# CRUD pattern  
class CRUDCandidate:
    async def get_multi_with_permission(self, skip: int, limit: int) -> List[Candidate]:
        # Data access implementation
        pass

# Schema pattern
class CandidateResponse(BaseModel):
    id: SnowflakeID  # Custom ID type for consistency
    email: str
    full_name: Optional[str] = None
```

#### ID Management Pattern
- **Database**: Snowflake ID (BigInteger)
- **API Schema**: SnowflakeID type (accepts int/str, outputs string)
- **Frontend**: string type (JSON serialization safe)

### Frontend Patterns

#### Component Architecture
```typescript
// UI Components (shadcn/ui based)
import { Card, CardContent, CardHeader } from '@/components/ui/card';

// Service pattern
export class CandidateService {
  async getList(): Promise<CandidateListResponse> {
    return apiClient.get<CandidateListResponse>('/candidates/');
  }
}

// Hook pattern
export function useCandidates() {
  return useQuery({
    queryKey: ['candidates'],
    queryFn: () => candidateService.getList(),
  });
}
```

#### State Management
- **Redux Toolkit**: Global application state
- **TanStack Query**: Server state management
- **Local State**: React hooks for component state

### Internationalization (i18n) System
```typescript
// Translation usage
const t = useTranslations();
return <h1>{t('common.appName')}</h1>;

// Translation files structure
// messages/en.json, messages/zh.json
{
  "common": { "appName": "TalentForge Pro" },
  "auth": { "loginTitle": "Sign In" }
}
```

## API Structure and Endpoints

### RESTful API Design
- **Base URL**: `/api/v1/`
- **Authentication**: JWT Bearer tokens
- **Pagination**: `?skip=0&limit=10` format
- **Error Format**: `{error_code, detail, details?}`

### Key Endpoint Categories
- **Authentication**: `/auth/` (login, refresh, me)
- **Users**: `/users/` (CRUD, permissions)
- **Candidates**: `/candidates/` (management, statistics)
- **Positions**: `/positions/` (job postings, matching)
- **Assessments**: `/assessments/` (evaluations, scoring)
- **Health**: `/health/` (system monitoring)

### API Standards
- **Consistent Response Format**: All list endpoints return `{items, total, skip, limit}`
- **Error Codes**: Structured format `[MODULE]_[CATEGORY]_[ERROR]`
- **ID Types**: Unified SnowflakeID handling
- **Trailing Slashes**: Required for list endpoints (FastAPI convention)

## Development Workflow

### Git Workflow
- **Branches**: `feature/*`, `bugfix/*`, `hotfix/*`
- **Commits**: Conventional commits (`feat:`, `fix:`, `docs:`)
- **Requirements**: PR code review + CI tests

### Build and Deployment
```bash
# Development (recommended)
make setup          # First-time setup
make up             # Start all services
make logs           # View logs
make down           # Stop services

# Legacy scripts
./app/scripts/development/start-unified.sh  # Unified proxy mode
./app/scripts/development/start-dev.sh      # Direct access mode
```

### Testing Strategy
- **Backend**: pytest + pytest-asyncio (>80% coverage target)
- **Frontend**: Jest + Testing Library + Playwright E2E
- **API Testing**: Tavern (Docker environment)
- **Test Pyramid**: Unit > Integration > E2E

### Environment Management
- **Development**: Hot reload, all ports exposed, debug enabled
- **Production**: Security hardened, monitoring enabled
- **Environment Variables**: Docker Compose + .env files
- **Data Persistence**: `~/dev_data/hephaestus/` directory

## Docker Architecture

### Service Dependencies
```mermaid
graph TB
    A[nginx] --> B[frontend]
    A --> C[backend]
    C --> D[postgres]
    C --> E[redis]
    C --> F[minio]
    C --> G[ollama]
    H[celery_worker] --> D
    H --> E
```

### Container Configuration
- **Unified Proxy**: Single entry point (localhost:8088)
- **GPU Support**: NVIDIA runtime for Ollama
- **Volume Strategy**: Named volumes + bind mounts
- **Network**: Custom bridge network (**********/16)
- **Health Checks**: All services have health monitoring

## Development Patterns and Conventions

### Code Quality Standards
- **Python**: Type hints, async/await, Pydantic validation
- **TypeScript**: Strict mode, no `any` types
- **Testing**: Comprehensive test coverage (>80% target)
- **Documentation**: Inline comments, API documentation

### Performance Considerations
- **API Response**: <200ms target
- **Frontend Load**: <3s first contentful paint
- **Vector Search**: <100ms for similarity queries
- **Database**: Optimized indexes, connection pooling

### Security Implementation
- **Authentication**: JWT + refresh tokens
- **Authorization**: Role-based access control (RBAC)
- **Data Validation**: Pydantic schemas
- **Environment**: Development bypass token for testing

## Integration Points and Dependencies

### External Services
- **AI Services**: OpenAI API (backup), Ollama (primary)
- **Email**: Configuration ready (not implemented)
- **File Storage**: MinIO S3-compatible storage
- **Monitoring**: Prometheus metrics collection

### Internal Service Communication
- **Backend ↔ Database**: SQLAlchemy async ORM
- **Backend ↔ Cache**: Redis for sessions, caching
- **Backend ↔ Queue**: Celery for async tasks
- **Frontend ↔ Backend**: Axios HTTP client with interceptors
- **Services ↔ Storage**: MinIO client for file operations

### Cross-Cutting Concerns
- **Logging**: Structured logging with correlation IDs
- **Monitoring**: Health checks, metrics endpoints
- **Caching**: Redis-based caching strategy
- **Error Handling**: Centralized exception handling

## Constraints and Considerations

### Technical Constraints
- **ID System**: Must use SnowflakeID consistently
- **Database**: PostgreSQL with pgvector extension required
- **Node Version**: >=18.17.0 for frontend
- **Python Version**: 3.12 for backend compatibility

### Architectural Decisions
- **Vector Database**: Unified pgvector vs. separate ChromaDB
- **Container Strategy**: Shared image for backend services
- **Proxy Architecture**: Nginx unified entry point
- **Package Management**: Poetry (backend), pnpm (frontend)

### Development Guidelines
- **Root Directory**: Strict file organization rules
- **Dependency Management**: Container isolation with refresh commands
- **i18n**: Mandatory error code localization
- **API Design**: Consistent pagination and error formats

### Performance Requirements
- **API Latency**: <200ms for standard requests
- **Vector Queries**: <100ms for similarity search
- **UI Responsiveness**: <3s initial load, <1s navigation
- **Concurrent Users**: Designed for enterprise scale

This repository represents a mature, production-ready application with comprehensive documentation, testing, and development workflows optimized for team collaboration and enterprise deployment.