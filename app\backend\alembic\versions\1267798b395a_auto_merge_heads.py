"""auto_merge_heads

Revision ID: 1267798b395a
Revises: 62b682c202c3, d1a2b3c4e5f6
Create Date: 2025-08-11 03:45:33.762836

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '1267798b395a'
down_revision: Union[str, None] = ('62b682c202c3', 'd1a2b3c4e5f6')
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass