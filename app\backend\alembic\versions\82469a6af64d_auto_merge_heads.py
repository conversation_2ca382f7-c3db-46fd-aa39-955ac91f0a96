"""auto_merge_heads

Revision ID: 82469a6af64d
Revises: 1267798b395a
Create Date: 2025-08-11 03:51:58.507573

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '82469a6af64d'
down_revision: Union[str, None] = '1267798b395a'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass