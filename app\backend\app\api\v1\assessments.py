"""
Assessment API endpoints for Sprint 4 Intelligent Recruitment System
Handles five-dimensional candidate assessments, JFS scoring, and AI-powered recommendations
"""
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta

from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks
from fastapi.responses import JSONResponse
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel, Field, validator

from app.api.deps import get_db, get_current_active_user
from app.models import User
from app.schemas.assessment import (
    AssessmentResponse, 
    JFSCalculationRequest,
    JFSCalculationResponse
)
from app.services.assessment_service import assessment_service
from app.services.recommendation_engine import recommendation_engine
from app.tasks import generate_candidate_assessment, generate_batch_recommendations
from app.services.celery_monitoring import celery_monitoring
from app.core.exceptions import bad_request, service_error, not_found
from app.api.deps import PermissionChecker
from app.crud import candidate as candidate_crud, assessment as assessment_crud

logger = logging.getLogger(__name__)
router = APIRouter()

# Request/Response Models

class AssessmentGenerateRequest(BaseModel):
    """Request model for assessment generation"""
    candidate_id: int = Field(..., description="Candidate ID to assess")
    force_regenerate: bool = Field(False, description="Force regeneration if assessment exists")
    include_recommendations: bool = Field(True, description="Generate improvement recommendations")
    async_processing: bool = Field(False, description="Process asynchronously for complex assessments")


class AssessmentGenerateResponse(BaseModel):
    """Response model for assessment generation"""
    success: bool
    assessment_id: Optional[int] = None
    task_id: Optional[str] = None  # For async processing
    assessment: Optional[AssessmentResponse] = None
    recommendations: Optional[Dict[str, Any]] = None
    message: str


class AssessmentCompareRequest(BaseModel):
    """Request model for candidate assessment comparison"""
    candidate_ids: List[int] = Field(..., min_items=2, max_items=10, description="Candidate IDs to compare")
    position_id: Optional[int] = Field(None, description="Position to calculate JFS scores against")
    include_recommendations: bool = Field(False, description="Include individual recommendations")
    comparison_dimensions: List[str] = Field(
        default=["digital_literacy", "industry_skills", "position_skills", "innovation_capability", "learning_potential"],
        description="Dimensions to compare"
    )


class AssessmentCompareResponse(BaseModel):
    """Response model for assessment comparison"""
    success: bool
    position_id: Optional[int] = None
    candidates_compared: int
    comparison_summary: Dict[str, Any]
    candidate_rankings: List[Dict[str, Any]]
    detailed_comparison: Dict[str, Any]


class BatchAssessmentRequest(BaseModel):
    """Request model for batch assessment generation"""
    candidate_ids: List[int] = Field(..., min_items=1, max_items=50, description="Candidate IDs to assess")
    force_regenerate: bool = Field(False, description="Force regeneration of existing assessments")
    include_recommendations: bool = Field(True, description="Generate improvement recommendations")
    max_concurrent: int = Field(5, ge=1, le=10, description="Maximum concurrent assessments")


class BatchAssessmentResponse(BaseModel):
    """Response model for batch assessment generation"""
    success: bool
    task_id: str
    candidate_count: int
    estimated_completion: str
    message: str


class JFSCalculationBatchRequest(BaseModel):
    """Request model for batch JFS calculation"""
    candidate_ids: List[int] = Field(..., min_items=1, max_items=100, description="Candidate IDs")
    position_id: int = Field(..., description="Position ID for JFS calculation")
    include_detailed_breakdown: bool = Field(False, description="Include detailed scoring breakdown")


class JFSCalculationBatchResponse(BaseModel):
    """Response model for batch JFS calculation"""
    success: bool
    position_id: int
    jfs_scores: List[Dict[str, Any]]
    calculation_metadata: Dict[str, Any]


class RecommendationRequest(BaseModel):
    """Request model for AI-powered recommendations"""
    candidate_id: int = Field(..., description="Candidate ID for recommendations")
    position_id: Optional[int] = Field(None, description="Position for skill gap analysis")
    include_career_paths: bool = Field(True, description="Include career path recommendations")
    include_skill_gaps: bool = Field(None, description="Include skill gap analysis (auto if position provided)")
    recommendation_focus: Optional[str] = Field(
        None, 
        pattern="^(improvement|career|skills|comprehensive)$",
        description="Focus area for recommendations"
    )


class RecommendationResponse(BaseModel):
    """Response model for recommendations"""
    success: bool
    candidate_id: int
    position_id: Optional[int] = None
    recommendations: Dict[str, Any]
    generated_at: str


# API Endpoints

@router.post("/generate", response_model=AssessmentGenerateResponse, tags=["Assessment Generation"])
async def generate_assessment(
    request: AssessmentGenerateRequest,
    background_tasks: BackgroundTasks = None,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Generate five-dimensional assessment for candidate with AI-powered scoring
    
    Supports both synchronous and asynchronous processing:
    - Sync: Returns immediate results (target <2s)
    - Async: Returns task ID for complex assessments requiring extensive AI analysis
    """
    try:
        # Verify candidate exists and user has access
        candidate = await candidate_crud.get(db, id=request.candidate_id)
        if not candidate:
            raise not_found(f"CANDIDATE_NOT_FOUND: {request.candidate_id}")
        
        if not permission_checker.can_view_candidate(current_user.id, candidate):
            raise service_error("CANDIDATE_VIEW_PERMISSION_DENIED")
        
        # Check if assessment already exists (unless force regeneration)
        if not request.force_regenerate:
            existing_assessment = await assessment_service.get_latest_assessment(
                db=db, candidate_id=request.candidate_id
            )
            if existing_assessment:
                # Return existing assessment with optional new recommendations
                response_data = {
                    "success": True,
                    "assessment_id": existing_assessment.id,
                    "assessment": AssessmentResponse.from_orm(existing_assessment),
                    "message": "ASSESSMENT_EXISTING_RETURNED"
                }
                
                # Generate fresh recommendations if requested
                if request.include_recommendations:
                    try:
                        recommendations = await recommendation_engine.generate_comprehensive_recommendations(
                            db=db,
                            candidate_id=request.candidate_id,
                            include_career_paths=True,
                            include_skill_gaps=False
                        )
                        response_data["recommendations"] = recommendations
                    except Exception as e:
                        logger.warning(f"Failed to generate recommendations: {str(e)}")
                
                return AssessmentGenerateResponse(**response_data)
        
        if request.async_processing:
            # Async processing for complex/batch operations
            task = generate_candidate_assessment.delay(
                candidate_id=request.candidate_id,
                assessor_id=current_user.id,
                force_regenerate=request.force_regenerate,
                include_recommendations=request.include_recommendations
            )
            
            # Record task start
            await celery_monitoring.record_task_event(
                task.id, 'task-submitted', 'generate_candidate_assessment', 'assessment'
            )
            
            return AssessmentGenerateResponse(
                success=True,
                task_id=task.id,
                message="ASSESSMENT_ASYNC_TASK_STARTED"
            )
        else:
            # Synchronous processing
            start_time = datetime.utcnow()
            
            # Generate assessment
            assessment_result = await assessment_service.generate_assessment(
                db=db,
                candidate_id=request.candidate_id,
                force_regenerate=request.force_regenerate
            )
            
            response_data = {
                "success": True,
                "assessment_id": assessment_result.id,
                "assessment": assessment_result,
                "message": "ASSESSMENT_SYNC_GENERATED_SUCCESSFULLY"
            }
            
            # Generate recommendations if requested and within time budget
            generation_time = (datetime.utcnow() - start_time).total_seconds()
            if request.include_recommendations and generation_time < 1.5:  # Leave time for recommendations
                try:
                    recommendations = await recommendation_engine.generate_comprehensive_recommendations(
                        db=db,
                        candidate_id=request.candidate_id,
                        include_career_paths=True,
                        include_skill_gaps=False
                    )
                    response_data["recommendations"] = recommendations
                except Exception as e:
                    logger.warning(f"Failed to generate recommendations within time budget: {str(e)}")
                    response_data["message"] = "ASSESSMENT_GENERATED_RECOMMENDATIONS_DEFERRED"
            
            return AssessmentGenerateResponse(**response_data)
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Assessment generation failed: {str(e)}")
        raise service_error(f"ASSESSMENT_GENERATION_FAILED: {str(e)}")


@router.get("/candidate/{candidate_id}", response_model=AssessmentResponse, tags=["Assessment Retrieval"])
async def get_candidate_assessment(
    candidate_id: int,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
    include_history: bool = Query(False, description="Include assessment history")
):
    """
    Get latest assessment for candidate with optional historical data
    """
    try:
        # Verify candidate exists and user has access
        candidate = await candidate_crud.get(db, id=candidate_id)
        if not candidate:
            raise not_found(f"CANDIDATE_NOT_FOUND: {candidate_id}")
        
        if not permission_checker.can_view_candidate(current_user.id, candidate):
            raise service_error("CANDIDATE_VIEW_PERMISSION_DENIED")
        
        # Get latest assessment
        assessment = await assessment_service.get_latest_assessment(db=db, candidate_id=candidate_id)
        if not assessment:
            raise not_found(f"ASSESSMENT_NOT_FOUND_FOR_CANDIDATE: {candidate_id}")
        
        # Convert to response format
        assessment_response = AssessmentResponse.from_orm(assessment)
        
        # Add historical data if requested
        if include_history:
            history = await assessment_crud.get_candidate_history(db, candidate_id=candidate_id, limit=10)
            assessment_response.history = [
                {
                    "id": h.id,
                    "assessed_at": h.assessed_at.isoformat(),
                    "dci_score": h.dci_score,
                    "assessor_id": h.assessor_id
                } for h in history
            ]
        
        return assessment_response
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get assessment for candidate {candidate_id}: {str(e)}")
        raise service_error(f"ASSESSMENT_RETRIEVAL_FAILED: {str(e)}")


@router.post("/calculate-jfs", response_model=JFSCalculationResponse, tags=["JFS Scoring"])
async def calculate_jfs_score(
    request: JFSCalculationRequest,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Calculate Job Fitness Score (JFS) for candidate against specific position
    
    JFS combines assessment scores with position requirements for precise matching
    """
    try:
        # Verify candidate and position exist and user has access
        candidate = await candidate_crud.get(db, id=request.candidate_id)
        if not candidate:
            raise not_found(f"CANDIDATE_NOT_FOUND: {request.candidate_id}")
        
        from app.crud import position as position_crud
        position = await position_crud.get(db, id=request.position_id)
        if not position:
            raise not_found(f"POSITION_NOT_FOUND: {request.position_id}")
        
        if not permission_checker.can_view_candidate(current_user.id, candidate):
            raise service_error("CANDIDATE_VIEW_PERMISSION_DENIED")
        
        if not permission_checker.can_view_position(current_user.id, position):
            raise service_error("POSITION_VIEW_PERMISSION_DENIED")
        
        # Calculate JFS score
        jfs_result = await assessment_service.calculate_jfs_score(
            db=db,
            candidate_id=request.candidate_id,
            position_id=request.position_id
        )
        
        return jfs_result
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"JFS calculation failed: {str(e)}")
        raise service_error(f"JFS_CALCULATION_FAILED: {str(e)}")


@router.post("/calculate-jfs-batch", response_model=JFSCalculationBatchResponse, tags=["JFS Scoring"])
async def calculate_jfs_batch(
    request: JFSCalculationBatchRequest,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Calculate JFS scores for multiple candidates against single position
    
    Efficient batch processing for large-scale candidate evaluation
    """
    try:
        # Verify position exists and user has access
        from app.crud import position as position_crud
        position = await position_crud.get(db, id=request.position_id)
        if not position:
            raise not_found(f"POSITION_NOT_FOUND: {request.position_id}")
        
        if not permission_checker.can_view_position(current_user.id, position):
            raise service_error("POSITION_VIEW_PERMISSION_DENIED")
        
        # Filter candidates user can access
        accessible_candidates = []
        for candidate_id in request.candidate_ids:
            candidate = await candidate_crud.get(db, id=candidate_id)
            if candidate and permission_checker.can_view_candidate(current_user.id, candidate):
                accessible_candidates.append(candidate_id)
        
        if not accessible_candidates:
            raise bad_request("JFS_BATCH_NO_ACCESSIBLE_CANDIDATES")
        
        # Calculate JFS scores for all accessible candidates
        jfs_scores = []
        calculation_errors = []
        
        for candidate_id in accessible_candidates:
            try:
                jfs_result = await assessment_service.calculate_jfs_score(
                    db=db,
                    candidate_id=candidate_id,
                    position_id=request.position_id
                )
                
                score_data = {
                    "candidate_id": candidate_id,
                    "jfs_score": jfs_result.jfs_score,
                    "match_confidence": jfs_result.match_confidence,
                    "calculated_at": datetime.utcnow().isoformat()
                }
                
                if request.include_detailed_breakdown:
                    score_data["dimension_scores"] = jfs_result.dimension_scores
                    score_data["score_breakdown"] = jfs_result.score_breakdown
                
                jfs_scores.append(score_data)
                
            except Exception as e:
                logger.warning(f"JFS calculation failed for candidate {candidate_id}: {str(e)}")
                calculation_errors.append({
                    "candidate_id": candidate_id,
                    "error": str(e)
                })
        
        # Sort by JFS score (highest first)
        jfs_scores.sort(key=lambda x: x["jfs_score"], reverse=True)
        
        return JFSCalculationBatchResponse(
            success=True,
            position_id=request.position_id,
            jfs_scores=jfs_scores,
            calculation_metadata={
                "requested_candidates": len(request.candidate_ids),
                "accessible_candidates": len(accessible_candidates),
                "successful_calculations": len(jfs_scores),
                "calculation_errors": len(calculation_errors),
                "errors": calculation_errors if calculation_errors else None,
                "calculated_at": datetime.utcnow().isoformat()
            }
        )
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Batch JFS calculation failed: {str(e)}")
        raise service_error(f"JFS_BATCH_CALCULATION_FAILED: {str(e)}")


@router.post("/compare", response_model=AssessmentCompareResponse, tags=["Assessment Comparison"])
async def compare_candidates(
    request: AssessmentCompareRequest,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Compare multiple candidates across assessment dimensions
    
    Provides detailed comparison analysis and ranking for decision making
    """
    try:
        # Verify all candidates exist and user has access
        candidates = []
        candidate_assessments = {}
        
        for candidate_id in request.candidate_ids:
            candidate = await candidate_crud.get(db, id=candidate_id)
            if not candidate:
                raise not_found(f"CANDIDATE_NOT_FOUND: {candidate_id}")
            
            if not permission_checker.can_view_candidate(current_user.id, candidate):
                logger.warning(f"User {current_user.id} lacks permission for candidate {candidate_id}")
                continue
            
            # Get latest assessment
            assessment = await assessment_service.get_latest_assessment(db=db, candidate_id=candidate_id)
            if assessment:
                candidates.append(candidate)
                candidate_assessments[candidate_id] = assessment
        
        if len(candidates) < 2:
            raise bad_request("ASSESSMENT_COMPARE_INSUFFICIENT_CANDIDATES")
        
        # Calculate JFS scores if position provided
        jfs_scores = {}
        if request.position_id:
            from app.crud import position as position_crud
            position = await position_crud.get(db, id=request.position_id)
            if not position:
                raise not_found(f"POSITION_NOT_FOUND: {request.position_id}")
            
            if not permission_checker.can_view_position(current_user.id, position):
                raise service_error("POSITION_VIEW_PERMISSION_DENIED")
            
            for candidate_id in candidate_assessments.keys():
                try:
                    jfs_result = await assessment_service.calculate_jfs_score(
                        db=db, candidate_id=candidate_id, position_id=request.position_id
                    )
                    jfs_scores[candidate_id] = jfs_result.jfs_score
                except Exception as e:
                    logger.warning(f"JFS calculation failed for candidate {candidate_id}: {str(e)}")
        
        # Perform comparison analysis
        dimension_stats = {}
        candidate_rankings = []
        
        # Calculate statistics for each dimension
        for dimension in request.comparison_dimensions:
            scores = []
            for candidate_id, assessment in candidate_assessments.items():
                score = getattr(assessment, f"{dimension}_score", None)
                if score is not None:
                    scores.append(score)
            
            if scores:
                dimension_stats[dimension] = {
                    "avg": round(sum(scores) / len(scores), 2),
                    "min": min(scores),
                    "max": max(scores),
                    "std_dev": round((sum((x - sum(scores)/len(scores))**2 for x in scores) / len(scores))**0.5, 2)
                }
        
        # Rank candidates
        for candidate in candidates:
            candidate_id = candidate.id
            assessment = candidate_assessments[candidate_id]
            
            # Calculate composite score based on comparison dimensions
            dimension_scores = {}
            total_score = 0
            valid_dimensions = 0
            
            for dimension in request.comparison_dimensions:
                score = getattr(assessment, f"{dimension}_score", None)
                if score is not None:
                    dimension_scores[dimension] = score
                    total_score += score
                    valid_dimensions += 1
            
            avg_score = total_score / valid_dimensions if valid_dimensions > 0 else 0
            
            ranking_data = {
                "candidate_id": candidate_id,
                "candidate_name": candidate.name,
                "assessment_id": assessment.id,
                "dci_score": assessment.dci_score,
                "average_score": round(avg_score, 2),
                "dimension_scores": dimension_scores,
                "assessed_at": assessment.assessed_at.isoformat()
            }
            
            if candidate_id in jfs_scores:
                ranking_data["jfs_score"] = jfs_scores[candidate_id]
            
            candidate_rankings.append(ranking_data)
        
        # Sort by JFS score if available, otherwise by average score
        if jfs_scores:
            candidate_rankings.sort(key=lambda x: x.get("jfs_score", 0), reverse=True)
        else:
            candidate_rankings.sort(key=lambda x: x["average_score"], reverse=True)
        
        # Generate comparison summary
        comparison_summary = {
            "candidates_compared": len(candidates),
            "dimensions_analyzed": len(request.comparison_dimensions),
            "top_performer": candidate_rankings[0]["candidate_id"] if candidate_rankings else None,
            "average_dci_score": round(sum(r["dci_score"] for r in candidate_rankings) / len(candidate_rankings), 2),
            "score_range": {
                "highest": max(r["average_score"] for r in candidate_rankings) if candidate_rankings else 0,
                "lowest": min(r["average_score"] for r in candidate_rankings) if candidate_rankings else 0
            }
        }
        
        if jfs_scores:
            comparison_summary["jfs_score_range"] = {
                "highest": max(jfs_scores.values()),
                "lowest": min(jfs_scores.values()),
                "average": round(sum(jfs_scores.values()) / len(jfs_scores), 2)
            }
        
        return AssessmentCompareResponse(
            success=True,
            position_id=request.position_id,
            candidates_compared=len(candidates),
            comparison_summary=comparison_summary,
            candidate_rankings=candidate_rankings,
            detailed_comparison={
                "dimension_statistics": dimension_stats,
                "comparison_criteria": {
                    "dimensions": request.comparison_dimensions,
                    "position_based": bool(request.position_id),
                    "jfs_calculated": bool(jfs_scores)
                },
                "generated_at": datetime.utcnow().isoformat()
            }
        )
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Assessment comparison failed: {str(e)}")
        raise service_error(f"ASSESSMENT_COMPARISON_FAILED: {str(e)}")


@router.post("/recommendations", response_model=RecommendationResponse, tags=["AI Recommendations"])
async def get_recommendations(
    request: RecommendationRequest,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Generate AI-powered improvement recommendations for candidate
    
    Provides personalized development suggestions based on assessment scores
    """
    try:
        # Verify candidate exists and user has access
        candidate = await candidate_crud.get(db, id=request.candidate_id)
        if not candidate:
            raise not_found(f"CANDIDATE_NOT_FOUND: {request.candidate_id}")
        
        if not permission_checker.can_view_candidate(current_user.id, candidate):
            raise service_error("CANDIDATE_VIEW_PERMISSION_DENIED")
        
        # Check if candidate has assessment
        assessment = await assessment_service.get_latest_assessment(db=db, candidate_id=request.candidate_id)
        if not assessment:
            raise bad_request("RECOMMENDATIONS_REQUIRE_ASSESSMENT")
        
        # Auto-enable skill gaps if position provided but not specified
        include_skill_gaps = request.include_skill_gaps
        if include_skill_gaps is None:
            include_skill_gaps = bool(request.position_id)
        
        # Verify position if provided
        if request.position_id:
            from app.crud import position as position_crud
            position = await position_crud.get(db, id=request.position_id)
            if not position:
                raise not_found(f"POSITION_NOT_FOUND: {request.position_id}")
            
            if not permission_checker.can_view_position(current_user.id, position):
                raise service_error("POSITION_VIEW_PERMISSION_DENIED")
        
        # Generate comprehensive recommendations
        recommendations = await recommendation_engine.generate_comprehensive_recommendations(
            db=db,
            candidate_id=request.candidate_id,
            position_id=request.position_id,
            include_career_paths=request.include_career_paths,
            include_skill_gaps=include_skill_gaps
        )
        
        return RecommendationResponse(
            success=True,
            candidate_id=request.candidate_id,
            position_id=request.position_id,
            recommendations=recommendations,
            generated_at=datetime.utcnow().isoformat()
        )
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Recommendation generation failed: {str(e)}")
        raise service_error(f"RECOMMENDATION_GENERATION_FAILED: {str(e)}")


@router.post("/batch-assessments", response_model=BatchAssessmentResponse, tags=["Batch Operations"])
async def start_batch_assessments(
    request: BatchAssessmentRequest,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Start batch assessment generation for multiple candidates
    
    Efficient processing of large candidate pools with async task monitoring
    """
    try:
        # Verify candidates exist and user has access
        accessible_candidates = []
        for candidate_id in request.candidate_ids:
            candidate = await candidate_crud.get(db, id=candidate_id)
            if candidate and permission_checker.can_view_candidate(current_user.id, candidate):
                accessible_candidates.append(candidate_id)
        
        if not accessible_candidates:
            raise bad_request("BATCH_ASSESSMENT_NO_ACCESSIBLE_CANDIDATES")
        
        # Start batch recommendation task (which includes assessments)
        task = generate_batch_recommendations.delay(
            candidate_ids=accessible_candidates,
            include_career_paths=request.include_recommendations,
            max_concurrent=request.max_concurrent
        )
        
        # Record task start
        await celery_monitoring.record_task_event(
            task.id, 'task-submitted', 'generate_batch_recommendations', 'assessment'
        )
        
        # Estimate completion time
        estimated_minutes = max(10, min(60, len(accessible_candidates) // request.max_concurrent * 2))
        estimated_completion = datetime.utcnow()
        estimated_completion = estimated_completion.replace(minute=estimated_completion.minute + estimated_minutes)
        
        return BatchAssessmentResponse(
            success=True,
            task_id=task.id,
            candidate_count=len(accessible_candidates),
            estimated_completion=estimated_completion.isoformat(),
            message="BATCH_ASSESSMENT_TASK_STARTED"
        )
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Batch assessment start failed: {str(e)}")
        raise service_error(f"BATCH_ASSESSMENT_START_FAILED: {str(e)}")


@router.get("/statistics", tags=["Analytics"])
async def get_assessment_statistics(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
    days: int = Query(30, ge=1, le=365, description="Days of history to analyze")
):
    """
    Get assessment system statistics and analytics
    """
    try:
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        
        # Get basic statistics
        total_assessments = await assessment_crud.count_total(db)
        recent_assessments = await assessment_crud.count_since_date(db, cutoff_date)
        
        # Get dimension score statistics
        dimension_stats = await assessment_crud.get_dimension_statistics(db, since_date=cutoff_date)
        
        # Get DCI score distribution
        dci_distribution = await assessment_crud.get_dci_distribution(db, since_date=cutoff_date)
        
        return JSONResponse({
            "success": True,
            "statistics": {
                "total_assessments": total_assessments,
                "recent_assessments": recent_assessments,
                "analysis_period_days": days,
                "dimension_statistics": dimension_stats,
                "dci_distribution": dci_distribution,
                "system_performance": {
                    "avg_generation_time_target": "< 2 seconds",
                    "accuracy_target": "> 95%",
                    "ai_provider": "deepseek_primary_ollama_fallback"
                }
            },
            "generated_at": datetime.utcnow().isoformat()
        })
    
    except Exception as e:
        logger.error(f"Failed to get assessment statistics: {str(e)}")
        raise service_error(f"ASSESSMENT_STATISTICS_FAILED: {str(e)}")