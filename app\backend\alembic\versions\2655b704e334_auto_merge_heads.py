"""auto_merge_heads

Revision ID: 2655b704e334
Revises: b51adae5546b
Create Date: 2025-08-13 02:23:29.853258

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2655b704e334'
down_revision: Union[str, None] = 'b51adae5546b'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass