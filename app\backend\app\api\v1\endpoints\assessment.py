"""
Assessment Service API Endpoints for Sprint 4
Handles five-dimensional capability assessment, JFS calculation, and candidate comparison
"""
import logging
from typing import Any, List, Dict, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from uuid import UUID
import time
import asyncio

from app.core.database import get_db
from app.core.exceptions import bad_request, service_error, not_found
from app.core.permissions import check_permission
from app.core.enums import Permission
from app.models.user import User
from app.schemas.assessment import (
    AssessmentRequest,
    AssessmentResponse,
    JFSCalculationRequest,
    JFSCalculationResponse,
    BatchJFSRequest,
    BatchJFSResponse,
    ComparisonRequest,
    ComparisonResponse,
    RecommendationRequest,
    RecommendationResponse,
    BatchAssessmentRequest,
    BatchAssessmentResponse,
    AssessmentStatistics
)
from app.services.assessment_service import assessment_service
from app.crud import candidate as candidate_crud
from app.crud import position as position_crud

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post("/generate", response_model=AssessmentResponse, tags=["Assessment"])
async def generate_assessment(
    *,
    request: AssessmentRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.AI_ASSESS)),
) -> Any:
    """
    Generate five-dimensional capability assessment for a candidate
    
    **Required Permission:** `ai:assess`
    
    **Five Dimensions:**
    - Digital Literacy (20%): Technology proficiency and digital skills
    - Industry Skills (25%): Domain-specific expertise and knowledge
    - Position Skills (30%): Job-specific competencies
    - Innovation (15%): Creative thinking and problem-solving
    - Learning Potential (10%): Adaptability and growth mindset
    
    **Request Body:**
    - `candidate_id`: UUID of the candidate
    - `include_recommendations`: Include improvement recommendations
    - `compare_with_position`: Optional position ID for job fit analysis
    - `force_regenerate`: Force new assessment even if cached
    
    **Returns:**
    - Five-dimensional scores with evidence
    - DCI (Digital Capability Index) score (0-100)
    - Percentile ranking among all candidates
    - Personalized recommendations for improvement
    - Radar chart data for visualization
    """
    try:
        # Validate candidate exists
        candidate = await candidate_crud.get(db, id=request.candidate_id)
        if not candidate:
            raise not_found("ASSESSMENT_CANDIDATE_NOT_FOUND", f"Candidate {request.candidate_id} not found")
        
        # Validate position if provided
        if request.compare_with_position:
            position = await position_crud.get(db, id=request.compare_with_position)
            if not position:
                raise not_found("ASSESSMENT_POSITION_NOT_FOUND", f"Position {request.compare_with_position} not found")
        
        logger.info(f"User {current_user.id} generating assessment for candidate {request.candidate_id}")
        
        start_time = time.time()
        
        # Generate assessment using service
        result = await assessment_service.generate_assessment(
            request=request,
            db=db
        )
        
        processing_time = (time.time() - start_time) * 1000
        
        logger.info(f"Assessment generated for candidate {request.candidate_id}: DCI={result.dci_score:.1f}, processing_time={processing_time:.1f}ms")
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Assessment generation failed: {str(e)}")
        raise service_error("ASSESSMENT_SERVICE_ERROR", "Failed to generate capability assessment")


@router.post("/jfs", response_model=JFSCalculationResponse, tags=["Assessment"])
async def calculate_jfs_score(
    *,
    request: JFSCalculationRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.AI_ASSESS)),
) -> Any:
    """
    Calculate Job Fit Score (JFS) between candidate and position
    
    **Required Permission:** `ai:assess`
    
    **JFS Algorithm:**
    - Weighted combination of five-dimensional scores
    - Position-specific skill matching
    - Experience level alignment
    - Industry background compatibility
    
    **Request Body:**
    - `candidate_id`: UUID of the candidate
    - `position_id`: UUID of the position
    - `include_details`: Include detailed match breakdown
    
    **Returns:**
    - JFS score (0-100)
    - Dimension-by-dimension match scores
    - Match summary and explanation
    - Improvement recommendations
    """
    try:
        # Validate entities exist
        candidate = await candidate_crud.get(db, id=request.candidate_id)
        if not candidate:
            raise not_found("JFS_CANDIDATE_NOT_FOUND")
        
        position = await position_crud.get(db, id=request.position_id)
        if not position:
            raise not_found("JFS_POSITION_NOT_FOUND")
        
        logger.info(f"User {current_user.id} calculating JFS for candidate {request.candidate_id} and position {request.position_id}")
        
        # Calculate JFS
        result = await assessment_service.calculate_jfs(
            request=request,
            db=db
        )
        
        logger.info(f"JFS calculated: {result.jfs_score:.1f}")
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"JFS calculation failed: {str(e)}")
        raise service_error("JFS_SERVICE_ERROR", "Failed to calculate Job Fit Score")


@router.post("/batch-jfs", response_model=BatchJFSResponse, tags=["Assessment"])
async def calculate_batch_jfs(
    *,
    request: BatchJFSRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.AI_ASSESS)),
) -> Any:
    """
    Calculate JFS scores for multiple candidates against one position
    
    **Required Permission:** `ai:assess`
    
    **Request Body:**
    - `candidate_ids`: List of candidate UUIDs (max 50)
    - `position_id`: UUID of the target position
    - `min_score`: Minimum JFS threshold for inclusion
    
    **Use Cases:**
    - Candidate ranking for a position
    - Batch screening for job openings
    - Talent pool analysis
    
    **Returns:**
    - JFS scores for all candidates
    - Ranked results above threshold
    - Statistical summary (average, distribution)
    - Optional async task ID for large batches
    """
    try:
        # Validate batch size
        if len(request.candidate_ids) > 50:
            raise bad_request("JFS_BATCH_TOO_LARGE", "Maximum 50 candidates per batch request")
        
        if not request.candidate_ids:
            raise bad_request("JFS_BATCH_EMPTY", "At least one candidate required")
        
        # Validate position exists
        position = await position_crud.get(db, id=request.position_id)
        if not position:
            raise not_found("JFS_POSITION_NOT_FOUND")
        
        # Validate candidates exist
        for candidate_id in request.candidate_ids:
            candidate = await candidate_crud.get(db, id=candidate_id)
            if not candidate:
                raise not_found("JFS_CANDIDATE_NOT_FOUND", f"Candidate {candidate_id} not found")
        
        logger.info(f"User {current_user.id} calculating batch JFS for {len(request.candidate_ids)} candidates")
        
        start_time = time.time()
        
        # Calculate batch JFS
        result = await assessment_service.calculate_batch_jfs(
            candidate_ids=request.candidate_ids,
            position_id=request.position_id,
            min_score=request.min_score,
            db=db
        )
        
        processing_time = (time.time() - start_time) * 1000
        
        logger.info(f"Batch JFS completed: {result.total_processed} processed, avg JFS: {result.average_jfs:.1f}")
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Batch JFS calculation failed: {str(e)}")
        raise service_error("BATCH_JFS_SERVICE_ERROR", "Failed to calculate batch JFS scores")


@router.post("/compare", response_model=ComparisonResponse, tags=["Assessment"])
async def compare_candidates(
    *,
    request: ComparisonRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.AI_ASSESS)),
) -> Any:
    """
    Compare multiple candidates across five dimensions
    
    **Required Permission:** `ai:assess`
    
    **Request Body:**
    - `candidate_ids`: List of candidate UUIDs to compare (2-10 candidates)
    - `position_id`: Optional position for contextualized comparison
    - `comparison_dimensions`: Specific dimensions to focus on
    
    **Comparison Features:**
    - Side-by-side capability comparison
    - Strength and weakness analysis
    - Ranking by overall score or specific dimensions
    - Position-specific fit analysis if position provided
    
    **Returns:**
    - Comparative analysis across dimensions
    - Winner identification
    - Individual strengths per candidate
    - Visualization data for comparison charts
    """
    try:
        # Validate candidate count
        if len(request.candidate_ids) < 2:
            raise bad_request("COMPARISON_TOO_FEW", "At least 2 candidates required for comparison")
        
        if len(request.candidate_ids) > 10:
            raise bad_request("COMPARISON_TOO_MANY", "Maximum 10 candidates per comparison")
        
        # Validate candidates exist
        for candidate_id in request.candidate_ids:
            candidate = await candidate_crud.get(db, id=candidate_id)
            if not candidate:
                raise not_found("COMPARISON_CANDIDATE_NOT_FOUND", f"Candidate {candidate_id} not found")
        
        # Validate position if provided
        if request.position_id:
            position = await position_crud.get(db, id=request.position_id)
            if not position:
                raise not_found("COMPARISON_POSITION_NOT_FOUND")
        
        logger.info(f"User {current_user.id} comparing {len(request.candidate_ids)} candidates")
        
        # Perform comparison
        result = await assessment_service.compare_candidates(
            request=request,
            db=db
        )
        
        logger.info(f"Candidate comparison completed, winner: {result.winner_id}")
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Candidate comparison failed: {str(e)}")
        raise service_error("COMPARISON_SERVICE_ERROR", "Failed to compare candidates")


@router.post("/recommendations", response_model=RecommendationResponse, tags=["Assessment"])
async def generate_recommendations(
    *,
    request: RecommendationRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.AI_ASSESS)),
) -> Any:
    """
    Generate personalized improvement recommendations for a candidate
    
    **Required Permission:** `ai:assess`
    
    **Request Body:**
    - `candidate_id`: UUID of the candidate
    - `focus_areas`: Specific dimensions to focus on (optional)
    - `career_goal`: Target career objective (optional)
    - `language`: Response language ("zh" or "en")
    
    **Recommendation Types:**
    - Skill development suggestions
    - Learning resource recommendations
    - Career advancement pathways
    - Specific improvement actions
    
    **Returns:**
    - Personalized improvement recommendations
    - Structured learning path with timeline
    - Estimated improvement impact per dimension
    - Curated learning resources and materials
    """
    try:
        # Validate candidate exists
        candidate = await candidate_crud.get(db, id=request.candidate_id)
        if not candidate:
            raise not_found("RECOMMENDATION_CANDIDATE_NOT_FOUND")
        
        # Validate language
        if request.language not in ["zh", "en"]:
            raise bad_request("RECOMMENDATION_INVALID_LANGUAGE", "Language must be 'zh' or 'en'")
        
        logger.info(f"User {current_user.id} generating recommendations for candidate {request.candidate_id}")
        
        # Generate recommendations
        result = await assessment_service.generate_recommendations(
            candidate_id=request.candidate_id,
            focus_areas=request.focus_areas,
            career_goal=request.career_goal,
            language=request.language,
            db=db
        )
        
        logger.info(f"Recommendations generated for candidate {request.candidate_id}")
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Recommendation generation failed: {str(e)}")
        raise service_error("RECOMMENDATION_SERVICE_ERROR", "Failed to generate recommendations")


@router.post("/batch", response_model=BatchAssessmentResponse, tags=["Assessment"])
async def batch_generate_assessments(
    *,
    request: BatchAssessmentRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.AI_ASSESS)),
) -> Any:
    """
    Generate assessments for multiple candidates in batch
    
    **Required Permission:** `ai:assess`
    
    **Request Body:**
    - `candidate_ids`: List of candidate UUIDs (max 20)
    - `include_recommendations`: Include recommendations for all
    
    **Batch Processing:**
    - Async processing for large batches
    - Progress tracking and status updates
    - Partial results on individual failures
    - Estimated completion time
    
    **Returns:**
    - Batch processing status
    - Task ID for progress monitoring
    - Estimated completion time
    - Partial results if available
    """
    try:
        # Validate batch size
        if len(request.candidate_ids) > 20:
            raise bad_request("ASSESSMENT_BATCH_TOO_LARGE", "Maximum 20 candidates per batch")
        
        if not request.candidate_ids:
            raise bad_request("ASSESSMENT_BATCH_EMPTY", "At least one candidate required")
        
        # Validate candidates exist
        for candidate_id in request.candidate_ids:
            candidate = await candidate_crud.get(db, id=candidate_id)
            if not candidate:
                raise not_found("ASSESSMENT_CANDIDATE_NOT_FOUND", f"Candidate {candidate_id} not found")
        
        logger.info(f"User {current_user.id} starting batch assessment for {len(request.candidate_ids)} candidates")
        
        # Start batch assessment
        result = await assessment_service.batch_generate_assessments(
            candidate_ids=request.candidate_ids,
            include_recommendations=request.include_recommendations,
            db=db
        )
        
        logger.info(f"Batch assessment started: task_id={result.task_id}")
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Batch assessment failed: {str(e)}")
        raise service_error("BATCH_ASSESSMENT_SERVICE_ERROR", "Failed to start batch assessment")


@router.get("/statistics", response_model=AssessmentStatistics, tags=["Assessment"])
async def get_assessment_statistics(
    *,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.AI_ASSESS)),
) -> Any:
    """
    Get assessment system statistics and insights
    
    **Required Permission:** `ai:assess`
    
    **Statistics Include:**
    - Total assessments performed
    - Average DCI scores across population
    - Score distribution by dimension
    - Top performers identification
    - Assessment trends over time
    
    **Returns:**
    - Comprehensive statistics
    - Population benchmarks
    - Score distributions
    - Performance trends
    """
    try:
        logger.info(f"User {current_user.id} requested assessment statistics")
        
        # Get statistics from service
        stats = await assessment_service.get_statistics(db=db)
        
        return stats
        
    except Exception as e:
        logger.error(f"Failed to get assessment statistics: {str(e)}")
        raise service_error("STATISTICS_SERVICE_ERROR", "Failed to retrieve assessment statistics")


@router.get("/benchmark", tags=["Assessment"])
async def get_assessment_benchmark(
    *,
    candidate_id: UUID,
    dimension: Optional[str] = Query(None, description="Specific dimension to benchmark"),
    industry: Optional[str] = Query(None, description="Industry for contextualized benchmark"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.AI_ASSESS)),
) -> Any:
    """
    Get benchmark comparison for a candidate's assessment
    
    **Required Permission:** `ai:assess`
    
    **Query Parameters:**
    - `candidate_id`: UUID of the candidate
    - `dimension`: Focus on specific dimension (optional)
    - `industry`: Industry context for benchmarking (optional)
    
    **Returns:**
    - Percentile rankings
    - Industry comparisons
    - Peer group analysis
    - Performance indicators
    """
    try:
        # Validate candidate exists
        candidate = await candidate_crud.get(db, id=candidate_id)
        if not candidate:
            raise not_found("BENCHMARK_CANDIDATE_NOT_FOUND")
        
        logger.info(f"User {current_user.id} requested benchmark for candidate {candidate_id}")
        
        # Get benchmark data
        benchmark = await assessment_service.get_benchmark_comparison(
            candidate_id=candidate_id,
            dimension=dimension,
            industry=industry,
            db=db
        )
        
        return benchmark
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Benchmark retrieval failed: {str(e)}")
        raise service_error("BENCHMARK_SERVICE_ERROR", "Failed to retrieve benchmark data")


@router.get("/health", tags=["Assessment"])
async def assessment_service_health(
    current_user: User = Depends(check_permission(Permission.AI_ASSESS)),
) -> Any:
    """
    Get assessment service health status
    
    **Required Permission:** `ai:assess`
    
    **Returns:**
    - Service health indicators
    - ML model status
    - Performance metrics
    - Error diagnostics
    """
    try:
        health_status = await assessment_service.health_check()
        
        return {
            "status": health_status.get("status", "unknown"),
            "ml_models": health_status.get("ml_models", {}),
            "assessment_engine": health_status.get("assessment_engine", {}),
            "performance": health_status.get("performance", {}),
            "last_check": time.time()
        }
        
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e),
            "ml_models": {"status": "unknown"},
            "assessment_engine": {"status": "unknown"},
            "last_check": time.time()
        }


@router.post("/recalibrate", tags=["Assessment"])
async def recalibrate_assessment_models(
    *,
    force_retrain: bool = False,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.SYSTEM_CONFIG)),
) -> Any:
    """
    Trigger recalibration of assessment models
    
    **Required Permission:** `system:config`
    
    **Request Body:**
    - `force_retrain`: Force complete model retraining
    
    **Calibration Process:**
    - Update scoring algorithms
    - Refresh percentile calculations
    - Recalibrate dimension weights
    - Update benchmark data
    
    **Returns:**
    - Calibration status
    - Task ID for monitoring
    - Estimated completion time
    """
    try:
        logger.info(f"User {current_user.id} triggered assessment model recalibration")
        
        # Start recalibration process
        result = await assessment_service.recalibrate_models(
            force_retrain=force_retrain,
            db=db
        )
        
        return {
            "status": "started",
            "task_id": result.get("task_id"),
            "force_retrain": force_retrain,
            "estimated_duration_minutes": result.get("estimated_duration", 30),
            "message": "Assessment model recalibration started",
            "started_by": current_user.id
        }
        
    except Exception as e:
        logger.error(f"Assessment recalibration failed: {str(e)}")
        raise service_error("RECALIBRATION_SERVICE_ERROR", "Failed to start model recalibration")