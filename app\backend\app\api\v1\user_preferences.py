"""
User preference API endpoints
"""
from typing import Any
from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from app.api import deps
from app.core.database import get_db
from app.core.exceptions import not_found
from app.crud import user_preference as user_preference_crud
from app.models.user import User
from app.schemas.user_preference import (
    UserPreferenceCreate,
    UserPreferenceUpdate,
    UserPreferenceResponse,
)

router = APIRouter()


@router.get("/me/preferences", response_model=UserPreferenceResponse)
async def get_user_preferences(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(deps.get_current_active_user)
) -> Any:
    """
    Get current user's preferences.
    Creates default preferences if none exist.
    """
    preferences = await user_preference_crud.get_or_create_for_user(
        db, user_id=current_user.id
    )
    return preferences


@router.put("/me/preferences", response_model=UserPreferenceResponse)
async def update_user_preferences(
    preferences_update: UserPreferenceUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(deps.get_current_active_user)
) -> Any:
    """
    Update current user's preferences.
    Creates default preferences first if none exist.
    """
    # Ensure preferences exist
    existing_preferences = await user_preference_crud.get_or_create_for_user(
        db, user_id=current_user.id
    )
    
    # Update preferences
    updated_preferences = await user_preference_crud.update_by_user_id(
        db, user_id=current_user.id, obj_in=preferences_update
    )
    
    if not updated_preferences:
        raise not_found("USER_PREFERENCES_NOT_FOUND", "User preferences not found")
    
    return updated_preferences


@router.post("/me/preferences", response_model=UserPreferenceResponse)
async def create_user_preferences(
    preferences_in: UserPreferenceCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(deps.get_current_active_user)
) -> Any:
    """
    Create user preferences with specific values.
    This endpoint is mainly for testing or admin purposes.
    Use GET endpoint to create with defaults.
    """
    # Check if preferences already exist
    existing_preferences = await user_preference_crud.get_by_user_id(
        db, user_id=current_user.id
    )
    
    if existing_preferences:
        # Update existing preferences instead
        preferences_update = UserPreferenceUpdate(**preferences_in.model_dump())
        updated_preferences = await user_preference_crud.update_by_user_id(
            db, user_id=current_user.id, obj_in=preferences_update
        )
        return updated_preferences
    
    # Create new preferences
    preferences = await user_preference_crud.create_for_user(
        db, user_id=current_user.id, obj_in=preferences_in
    )
    return preferences