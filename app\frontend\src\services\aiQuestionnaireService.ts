/**
 * AI Questionnaire Service
 * Handles all AI-powered questionnaire generation and evaluation operations
 */

import { apiClient } from '@/lib/api-client';

export interface QuestionnaireGenerateRequest {
  position_type: string;
  dimensions?: string[];
  question_count?: number;
  industry?: string;
  custom_requirements?: string;
  difficulty_level?: 'easy' | 'medium' | 'hard';
}

export interface QuestionnaireEvaluateRequest {
  candidate_id: string;
  submission_id?: string;
  responses: Record<string, any>;
}

export interface EvaluationReport {
  id: string;
  questionnaire_id: string;
  candidate_id?: string;
  submission_id?: string;
  dimension_scores?: Record<string, number>;
  total_score?: number;
  overall_evaluation?: string;
  strengths?: string[];
  weaknesses?: string[];
  recommendations?: string;
  key_insights?: string;
  is_qualified?: boolean;
  match_score?: number;
  risk_level?: string;
  percentile_rank?: number;
  evaluated_at?: string;
  created_at?: string;
  updated_at?: string;
}

export interface QuestionnaireTemplate {
  position_type: string;
  description: string;
  dimensions: string[];
  question_types: string[];
  focus_areas: string[];
}

export interface PublishRequest {
  slug: string;
  valid_until?: string;
  access_type: 'private' | 'public' | 'password';
  max_submissions?: number;
  password?: string;
}

export interface ReviewRequest {
  approved: boolean;
  comments?: string;
  rejection_reason?: string;
}

export interface CompareRequest {
  questionnaire_id: string;
  candidate_ids: string[];
}

class AIQuestionnaireService {
  private baseUrl = '/api/v1/ai/ai-questionnaire';

  /**
   * Generate an AI-powered questionnaire
   */
  async generateQuestionnaire(request: QuestionnaireGenerateRequest) {
    const response = await apiClient.post(`${this.baseUrl}/generate`, request);
    return response;
  }

  /**
   * Evaluate candidate responses using AI
   */
  async evaluateResponses(
    questionnaireId: string,
    request: QuestionnaireEvaluateRequest
  ): Promise<EvaluationReport> {
    const response = await apiClient.post(
      `${this.baseUrl}/evaluate/${questionnaireId}`,
      request
    );
    return response;
  }

  /**
   * Get available questionnaire templates
   */
  async getTemplates(): Promise<QuestionnaireTemplate[]> {
    const response = await apiClient.get(`${this.baseUrl}/templates`);
    return response;
  }

  /**
   * Publish a questionnaire for public access
   */
  async publishQuestionnaire(questionnaireId: string, request: PublishRequest) {
    const response = await apiClient.post(
      `${this.baseUrl}/${questionnaireId}/publish`,
      request
    );
    return response;
  }

  /**
   * Review and approve/reject a questionnaire
   */
  async reviewQuestionnaire(questionnaireId: string, request: ReviewRequest) {
    const response = await apiClient.post(
      `${this.baseUrl}/${questionnaireId}/review`,
      request
    );
    return response;
  }

  /**
   * Compare multiple candidates for the same questionnaire
   */
  async compareCandidates(request: CompareRequest) {
    const response = await apiClient.post(
      `${this.baseUrl}/compare-candidates`,
      request
    );
    return response;
  }

  /**
   * Generate a shareable link for a published questionnaire
   */
  generatePublicLink(slug: string): string {
    return `${window.location.origin}/questionnaire/${slug}`;
  }

  /**
   * Get status display text
   */
  getStatusDisplay(status: string): { text: string; color: string } {
    const statusMap: Record<string, { text: string; color: string }> = {
      draft: { text: '草稿', color: 'gray' },
      reviewing: { text: '审核中', color: 'yellow' },
      approved: { text: '已批准', color: 'green' },
      rejected: { text: '已拒绝', color: 'red' },
      published: { text: '已发布', color: 'blue' },
      archived: { text: '已归档', color: 'gray' },
    };
    return statusMap[status] || { text: status, color: 'gray' };
  }

  /**
   * Get risk level display
   */
  getRiskLevelDisplay(level: string): { text: string; color: string } {
    const riskMap: Record<string, { text: string; color: string }> = {
      low: { text: '低风险', color: 'green' },
      medium: { text: '中风险', color: 'yellow' },
      high: { text: '高风险', color: 'red' },
    };
    return riskMap[level] || { text: level, color: 'gray' };
  }

  /**
   * Format score for display
   */
  formatScore(score: number): string {
    return `${score.toFixed(1)}%`;
  }

  /**
   * Get dimension display name
   */
  getDimensionDisplay(dimension: string): string {
    const dimensionMap: Record<string, string> = {
      '工艺流程理解': '工艺流程',
      '质量控制意识': '质量控制',
      '设备操作能力': '设备操作',
      '安全生产意识': '安全生产',
      '问题解决能力': '问题解决',
      '专卖法规知识': '法规知识',
      '市场分析能力': '市场分析',
      '客户服务意识': '客户服务',
      '营销策略制定': '营销策略',
      '数据分析能力': '数据分析',
      '检验标准掌握': '检验标准',
      '检测技术能力': '检测技术',
      '问题追溯能力': '问题追溯',
      '规范执行力': '规范执行',
      '物流规划能力': '物流规划',
      '库存管理能力': '库存管理',
      '信息系统操作': '系统操作',
      '应急处理能力': '应急处理',
      '服务意识': '服务意识',
      '企业文化认同': '文化认同',
      '合规意识': '合规意识',
      '团队协作': '团队协作',
      '创新思维': '创新思维',
      '执行力': '执行力',
    };
    return dimensionMap[dimension] || dimension;
  }

  /**
   * Get public questionnaire by slug
   */
  async getPublicQuestionnaire(slug: string): Promise<any> {
    try {
      const response = await apiClient.get<any>(
        `/api/v1/public/questionnaires/${slug}`
      );
      return response;
    } catch (error) {
      console.error('Failed to get public questionnaire:', error);
      return null;
    }
  }

  /**
   * Verify questionnaire password
   */
  async verifyQuestionnairePassword(slug: string, password: string): Promise<boolean> {
    try {
      const response = await apiClient.post<{ valid: boolean }>(
        `/api/v1/public/questionnaires/${slug}/verify-password`,
        { password }
      );
      return response.valid;
    } catch (error) {
      console.error('Failed to verify password:', error);
      return false;
    }
  }

  /**
   * Submit questionnaire response
   */
  async submitQuestionnaireResponse(data: any): Promise<any> {
    try {
      const response = await apiClient.post<any>(
        `/api/v1/questionnaire-responses/submit`,
        data
      );
      return response;
    } catch (error) {
      console.error('Failed to submit response:', error);
      throw error;
    }
  }
}

export const aiQuestionnaireService = new AIQuestionnaireService();