"""
Vector management API endpoints for similarity search and vector operations
"""
from typing import Any, List, Optional, Dict
from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession
from app.api import deps
from app.api.deps import get_current_active_user
from app.core.database import get_db
from app.core.exceptions import not_found, bad_request, service_error, forbidden
from app.core.permissions import check_permission, check_any_permission
from app.core.enums import Permission
from app.crud.job_vector import job_vector as job_vector_crud
from app.crud.position import position as position_crud
from app.models.user import User
from app.schemas.job_vector import JobVectorResponse, JobVectorCreate, JobVectorUpdate
from app.services.embedding_service import embedding_service
from app.services.matching_service import matching_service
import logging

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/", response_model=List[JobVectorResponse])
async def list_vectors(
    db: AsyncSession = Depends(get_db),
    skip: int = Query(0, ge=0, description="Skip records"),
    limit: int = Query(20, ge=1, le=100, description="Limit records"),
    position_ids: Optional[List[int]] = Query(None, description="Filter by position IDs"),
    embedding_providers: Optional[List[str]] = Query(None, description="Filter by embedding providers"),
    current_user: User = Depends(check_permission(Permission.POSITIONS_READ))
) -> Any:
    """
    Get list of job vectors with filtering
    """
    try:
        # Get vectors with filtering
        vectors = await job_vector_crud.get_multi_with_vectors(
            db,
            skip=skip,
            limit=limit,
            position_ids=position_ids,
            embedding_providers=embedding_providers
        )
        
        # Filter vectors by user's permission to access positions
        accessible_vectors = []
        for vector in vectors:
            position = await position_crud.get_with_permission(
                db,
                id=vector.position_id,
                user_id=current_user.id,
                user_role=current_user.role
            )
            if position:
                accessible_vectors.append(vector)
        
        logger.info(f"Retrieved {len(accessible_vectors)} vectors for user {current_user.id}")
        return accessible_vectors
        
    except Exception as e:
        logger.error(f"Vector list retrieval failed: {str(e)}")
        raise service_error(f"VECTOR_LIST_RETRIEVAL_FAILED: {str(e)}")


@router.get("/stats")
async def get_vector_stats(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Get simplified vector statistics for the dashboard
    """
    try:
        from app.crud.job_vector import job_vector as job_vector_crud
        from app.crud.position import position as position_crud
        
        # Get accessible positions
        accessible_positions = await position_crud.get_multi_with_permission(
            db,
            user_id=current_user.id,
            user_role=current_user.role,
            limit=1000
        )
        position_ids = [pos.id for pos in accessible_positions]
        
        if not position_ids:
            return {
                "total_vectors": 0,
                "with_embeddings": 0,
                "without_embeddings": 0,
                "ollama_vectors": 0,
                "openai_vectors": 0,
                "coverage_percentage": 0
            }
        
        # Get vector stats
        vector_stats = await job_vector_crud.get_vector_stats(db, position_ids)
        
        # Calculate coverage
        total_positions = len(position_ids)
        with_vectors = vector_stats.get("total_vectors", 0)
        without_vectors = total_positions - with_vectors
        coverage_percentage = (with_vectors / total_positions * 100) if total_positions > 0 else 0
        
        # Get provider distribution
        provider_dist = vector_stats.get("provider_distribution", {})
        
        return {
            "total_vectors": with_vectors,
            "with_embeddings": with_vectors,
            "without_embeddings": without_vectors,
            "ollama_vectors": provider_dist.get("ollama", 0),
            "openai_vectors": provider_dist.get("openai", 0),
            "coverage_percentage": round(coverage_percentage, 1)
        }
        
    except Exception as e:
        logger.error(f"Failed to get vector stats: {str(e)}")
        # Return empty stats to avoid breaking frontend
        return {
            "total_vectors": 0,
            "with_embeddings": 0,
            "without_embeddings": 0,
            "ollama_vectors": 0,
            "openai_vectors": 0,
            "coverage_percentage": 0
        }


@router.get("/stats/overview")
async def get_vector_statistics(
    position_ids: Optional[List[int]] = Query(None, description="Filter by position IDs"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.POSITIONS_READ))
) -> Any:
    """
    Get vector statistics and overview
    """
    try:
        # Get accessible position IDs if not specified
        if position_ids is None:
            accessible_positions = await position_crud.get_multi_with_permission(
                db,
                user_id=current_user.id,
                user_role=current_user.role,
                limit=1000  # Large limit to get all accessible positions
            )
            position_ids = [pos.id for pos in accessible_positions]
        else:
            # Validate access to specified positions
            accessible_ids = []
            for pos_id in position_ids:
                position = await position_crud.get_with_permission(
                    db,
                    id=pos_id,
                    user_id=current_user.id,
                    user_role=current_user.role
                )
                if position:
                    accessible_ids.append(pos_id)
            position_ids = accessible_ids
        
        if not position_ids:
            return {
                "accessible_positions": 0,
                "vector_stats": {
                    "total_vectors": 0,
                    "avg_generation_time": 0,
                    "earliest_generated": None,
                    "latest_generated": None,
                    "provider_distribution": {}
                },
                "system_info": await _get_system_embedding_info()
            }
        
        # Get vector statistics
        vector_stats = await job_vector_crud.get_vector_stats(db, position_ids)
        
        # Get system embedding info
        system_info = await _get_system_embedding_info()
        
        return {
            "accessible_positions": len(position_ids),
            "vector_stats": vector_stats,
            "system_info": system_info
        }
        
    except Exception as e:
        logger.error(f"Vector statistics retrieval failed: {str(e)}")
        raise service_error(f"VECTOR_STATS_RETRIEVAL_FAILED: {str(e)}")


@router.get("/{vector_id}", response_model=JobVectorResponse)
async def get_vector(
    vector_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.POSITIONS_READ))
) -> Any:
    """
    Get specific vector by ID
    """
    # Get vector
    vector = await job_vector_crud.get(db, id=vector_id)
    if not vector:
        raise not_found("VECTOR_NOT_FOUND")
    
    # Check permission through associated position
    position = await position_crud.get_with_permission(
        db,
        id=vector.position_id,
        user_id=current_user.id,
        user_role=current_user.role
    )
    
    if not position:
        raise forbidden("VECTOR_ACCESS_DENIED")
    
    return vector


@router.delete("/{vector_id}")
async def delete_vector(
    vector_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.POSITIONS_WRITE))
) -> Any:
    """
    Delete a vector by ID
    """
    # Get vector
    vector = await job_vector_crud.get(db, id=vector_id)
    if not vector:
        raise not_found("VECTOR_NOT_FOUND")
    
    # Check permission through associated position
    position = await position_crud.get_with_permission(
        db,
        id=vector.position_id,
        user_id=current_user.id,
        user_role=current_user.role
    )
    
    if not position:
        raise forbidden("VECTOR_DELETE_ACCESS_DENIED")
    
    # Check if user can update the position (required for vector operations)
    if not position_crud._can_update(position, current_user.id):
        raise forbidden("VECTOR_DELETE_PERMISSION_DENIED")
    
    try:
        success = await job_vector_crud.delete_by_position_id(db, vector.position_id)
        if not success:
            raise not_found("VECTOR_DELETE_FAILED")
        
        logger.info(f"Deleted vector {vector_id} by user {current_user.id}")
        return {"success": True, "message": "VECTOR_DELETED_SUCCESS"}
        
    except Exception as e:
        logger.error(f"Vector deletion failed: {str(e)}")
        raise service_error(f"VECTOR_DELETE_FAILED: {str(e)}")


@router.post("/search/similar")
async def search_similar_vectors(
    query_vector: List[float],
    limit: int = Query(10, ge=1, le=50, description="Limit results"),
    similarity_threshold: float = Query(0.7, ge=0, le=1, description="Minimum similarity score"),
    position_ids: Optional[List[int]] = Query(None, description="Filter by position IDs"),
    exclude_position_id: Optional[int] = Query(None, description="Exclude position ID"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.POSITIONS_READ))
) -> Any:
    """
    Search for similar vectors using direct vector input
    """
    try:
        # Validate vector dimension
        if len(query_vector) not in [1024, 1536]:  # BGE-M3 or OpenAI dimensions
            raise bad_request("INVALID_VECTOR_DIMENSION")
        
        # Perform similarity search
        similar_results = await job_vector_crud.search_similar_positions(
            db,
            query_vector=query_vector,
            limit=limit * 2,  # Get more results to filter by permissions
            similarity_threshold=similarity_threshold,
            position_ids=position_ids,
            exclude_position_id=exclude_position_id
        )
        
        # Filter results by user permissions
        accessible_results = []
        for result in similar_results:
            position = await position_crud.get_with_permission(
                db,
                id=result["position_id"],
                user_id=current_user.id,
                user_role=current_user.role
            )
            if position:
                # Add position metadata to result
                result["position_title"] = position.title
                result["position_department"] = position.department
                result["position_status"] = position.status.value
                accessible_results.append(result)
                
                # Apply final limit
                if len(accessible_results) >= limit:
                    break
        
        logger.info(f"Vector similarity search returned {len(accessible_results)} results")
        return {
            "query_info": {
                "vector_dimension": len(query_vector),
                "similarity_threshold": similarity_threshold,
                "limit": limit
            },
            "results": accessible_results,
            "total_found": len(accessible_results)
        }
        
    except Exception as e:
        logger.error(f"Vector similarity search failed: {str(e)}")
        raise service_error(f"VECTOR_SIMILARITY_SEARCH_FAILED: {str(e)}")


@router.post("/search/by-text")
async def search_similar_by_text(
    text_query: str,
    limit: int = Query(10, ge=1, le=50, description="Limit results"),
    similarity_threshold: float = Query(0.7, ge=0, le=1, description="Minimum similarity score"),
    embedding_provider: Optional[str] = Query(None, description="Embedding provider (ollama/openai)"),
    position_ids: Optional[List[int]] = Query(None, description="Filter by position IDs"),
    exclude_position_id: Optional[int] = Query(None, description="Exclude position ID"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.POSITIONS_READ))
) -> Any:
    """
    Search for similar vectors using text query (generates embedding)
    """
    try:
        # Validate text input
        if not text_query or len(text_query.strip()) < 3:
            raise bad_request("TEXT_QUERY_TOO_SHORT")
        
        # Generate embedding for text query
        query_embedding, metadata = await embedding_service.generate_single_embedding(
            text_query.strip(),
            provider=embedding_provider
        )
        
        # Perform similarity search
        similar_results = await job_vector_crud.search_similar_positions(
            db,
            query_vector=query_embedding,
            limit=limit * 2,  # Get more results to filter by permissions
            similarity_threshold=similarity_threshold,
            position_ids=position_ids,
            exclude_position_id=exclude_position_id
        )
        
        # Filter results by user permissions
        accessible_results = []
        for result in similar_results:
            position = await position_crud.get_with_permission(
                db,
                id=result["position_id"],
                user_id=current_user.id,
                user_role=current_user.role
            )
            if position:
                # Add position metadata to result
                result["position_title"] = position.title
                result["position_department"] = position.department
                result["position_status"] = position.status.value
                accessible_results.append(result)
                
                # Apply final limit
                if len(accessible_results) >= limit:
                    break
        
        logger.info(f"Text-based vector search returned {len(accessible_results)} results")
        return {
            "query_info": {
                "text_query": text_query,
                "embedding_provider": metadata.get("embedding_provider"),
                "embedding_model": metadata.get("embedding_model"),
                "vector_dimension": metadata.get("embedding_dimension"),
                "generation_time_ms": metadata.get("generation_time_ms"),
                "similarity_threshold": similarity_threshold,
                "limit": limit
            },
            "results": accessible_results,
            "total_found": len(accessible_results)
        }
        
    except Exception as e:
        logger.error(f"Text-based vector search failed: {str(e)}")
        if "embedding" in str(e).lower():
            raise service_error(f"EMBEDDING_GENERATION_FAILED: {str(e)}")
        else:
            raise service_error(f"TEXT_VECTOR_SEARCH_FAILED: {str(e)}")


@router.get("/providers/info")
async def get_embedding_providers_info(
    current_user: User = Depends(check_permission(Permission.POSITIONS_READ))
) -> Any:
    """
    Get information about available embedding providers
    """
    try:
        providers = embedding_service.get_supported_providers()
        provider_details = []
        
        for provider in providers:
            info = embedding_service.get_provider_info(provider)
            provider_details.append(info)
        
        return {
            "supported_providers": providers,
            "provider_details": provider_details,
            "default_provider": "auto",
            "system_config": {
                "default_dimension": 1024,
                "similarity_threshold": 0.7,
                "max_concurrent_embeddings": 5
            }
        }
        
    except Exception as e:
        logger.error(f"Provider info retrieval failed: {str(e)}")
        raise service_error(f"PROVIDER_INFO_RETRIEVAL_FAILED: {str(e)}")


@router.post("/generate")
async def generate_embedding(
    text: str,
    provider: Optional[str] = Query(None, description="Embedding provider (ollama/openai)"),
    return_full_vector: bool = Query(False, description="Return full vector or just preview"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.POSITIONS_READ))
) -> Any:
    """
    Generate embedding for given text
    """
    try:
        # Validate text input
        if not text or len(text.strip()) < 3:
            raise bad_request("TEXT_TOO_SHORT_FOR_EMBEDDING")
        
        # Generate embedding
        embedding, metadata = await embedding_service.generate_single_embedding(
            text.strip(),
            provider=provider
        )
        
        return {
            "text": text.strip(),
            "embedding": embedding if return_full_vector else embedding[:10],  # Full or preview
            "metadata": metadata,
            "dimension": len(embedding),
            "success": True
        }
        
    except Exception as e:
        logger.error(f"Embedding generation failed: {str(e)}")
        raise service_error(f"EMBEDDING_GENERATION_FAILED: {str(e)}")


@router.post("/test/embedding")
async def test_embedding_generation(
    text: str,
    provider: Optional[str] = Query(None, description="Embedding provider (ollama/openai)"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.POSITIONS_WRITE))
) -> Any:
    """
    Test embedding generation with a sample text
    """
    try:
        # Validate text input
        if not text or len(text.strip()) < 3:
            raise bad_request("TEXT_TOO_SHORT_FOR_EMBEDDING")
        
        # Generate embedding
        embedding, metadata = await embedding_service.generate_single_embedding(
            text.strip(),
            provider=provider
        )
        
        return {
            "text": text.strip(),
            "embedding_preview": embedding[:10],  # Show first 10 dimensions
            "metadata": metadata,
            "success": True
        }
        
    except Exception as e:
        logger.error(f"Embedding test failed: {str(e)}")
        raise service_error(f"EMBEDDING_TEST_FAILED: {str(e)}")


async def _get_system_embedding_info() -> Dict[str, Any]:
    """Get system embedding information"""
    try:
        providers = embedding_service.get_supported_providers()
        system_info = {
            "available_providers": providers,
            "embedding_dimensions": {
                "ollama_bge_m3": 1024,
                "openai_small": 1536
            },
            "similarity_method": "cosine",
            "vector_index_type": "hnsw"
        }
        
        return system_info
        
    except Exception:
        return {
            "available_providers": [],
            "embedding_dimensions": {},
            "similarity_method": "cosine",
            "vector_index_type": "hnsw"
        }