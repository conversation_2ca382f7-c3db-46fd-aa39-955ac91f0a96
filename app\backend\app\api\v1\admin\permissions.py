"""
Permission management API endpoints
"""
from typing import Any, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.api import deps
from app.core.database import get_db
from app.core.exceptions import ConflictError, NotFoundError
from app.core.exceptions import PermissionError as AppPermissionError
from app.core.permissions import check_permission
from app.core.enums import Permission
from app.models.user import User
from app.schemas.permission import (
    PermissionCreate,
    PermissionFilter,
    PermissionListResponse,
    PermissionResponse,
    PermissionUpdate,
)
from app.services.permission import permission_service
from app.services.role import role_service

router = APIRouter()


@router.get("/", response_model=PermissionListResponse)
async def list_permissions(
    *,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.PERMISSIONS_READ)),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    module: str | None = None,
    resource: str | None = None,
    action: str | None = None,
    access_level: str | None = None,
    is_active: bool | None = None,
    is_system: bool | None = None,
    search: str | None = None
) -> Any:
    """
    List all permissions with optional filtering.
    
    Requires permission: permissions.read
    """

    # Build filter
    filters = PermissionFilter(
        module=module,
        resource=resource,
        action=action,
        access_level=access_level,
        is_active=is_active,
        is_system=is_system,
        search=search
    )

    return await permission_service.list_permissions(
        db,
        skip=skip,
        limit=limit,
        filters=filters
    )


@router.get("/grouped", response_model=dict)
async def get_permissions_grouped(
    *,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.PERMISSIONS_READ))
) -> Any:
    """
    Get permissions grouped by module.
    
    Requires permission: permissions.read
    """
    return await permission_service.group_permissions_by_module(db)


@router.get("/{permission_id}", response_model=PermissionResponse)
async def get_permission(
    *,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.PERMISSIONS_READ)),
    permission_id: int
) -> Any:
    """
    Get a specific permission by ID.
    
    Requires permission: permissions.read
    """

    try:
        return await permission_service.get_permission(db, permission_id)
    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )


@router.post("/", response_model=PermissionResponse, status_code=status.HTTP_201_CREATED)
async def create_permission(
    *,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(deps.get_current_active_user),
    permission_in: PermissionCreate
) -> Any:
    """
    Create a new permission.
    
    Requires permission: permissions.manage
    """
    # Check permission
    has_permission = await role_service.check_user_permission(
        db, current_user.id, Permission.PERMISSIONS_WRITE.value
    )
    if not has_permission:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to create permissions"
        )

    try:
        return await permission_service.create_permission(db, permission_in)
    except ConflictError as e:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=str(e)
        )


@router.put("/{permission_id}", response_model=PermissionResponse)
async def update_permission(
    *,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(deps.get_current_active_user),
    permission_id: int,
    permission_in: PermissionUpdate
) -> Any:
    """
    Update a permission.
    
    Requires permission: permissions.manage
    """
    # Check permission
    has_permission = await role_service.check_user_permission(
        db, current_user.id, Permission.PERMISSIONS_WRITE.value
    )
    if not has_permission:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to update permissions"
        )

    try:
        return await permission_service.update_permission(db, permission_id, permission_in)
    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except AppPermissionError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )
    except ConflictError as e:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=str(e)
        )


@router.delete("/{permission_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_permission(
    *,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(deps.get_current_active_user),
    permission_id: int
) -> None:
    """
    Delete a permission.
    
    Requires permission: permissions.manage
    """
    # Check permission
    has_permission = await role_service.check_user_permission(
        db, current_user.id, Permission.PERMISSIONS_DELETE.value
    )
    if not has_permission:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to delete permissions"
        )

    try:
        await permission_service.delete_permission(db, permission_id)
    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except AppPermissionError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )


@router.post("/bulk", response_model=list[PermissionResponse], status_code=status.HTTP_201_CREATED)
async def bulk_create_permissions(
    *,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(deps.get_current_active_user),
    permissions_in: list[PermissionCreate]
) -> Any:
    """
    Create multiple permissions at once.
    
    Requires permission: permissions.manage
    """
    # Check permission
    has_permission = await role_service.check_user_permission(
        db, current_user.id, Permission.PERMISSIONS_WRITE.value
    )
    if not has_permission:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to create permissions"
        )

    return await permission_service.bulk_create_permissions(db, permissions_in)


@router.post("/initialize", response_model=list[PermissionResponse])
async def initialize_permissions(
    *,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(deps.get_current_active_superuser)
) -> Any:
    """
    Initialize default system permissions.
    
    Requires super admin access.
    """
    return await permission_service.initialize_default_permissions(db)
