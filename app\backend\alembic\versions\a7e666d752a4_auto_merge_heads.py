"""auto_merge_heads

Revision ID: a7e666d752a4
Revises: 6e711e1bb41e
Create Date: 2025-08-13 01:48:27.981317

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'a7e666d752a4'
down_revision: Union[str, None] = '6e711e1bb41e'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass