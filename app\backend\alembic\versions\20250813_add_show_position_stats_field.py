"""Add show_position_stats field to user_preferences table

Revision ID: 20250813_show_position_stats
Revises: 20250813_add_candidate_vectors
Create Date: 2025-08-13 16:30:00.000000

"""
from typing import Sequence, Union
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision: str = '20250813_show_position_stats'
down_revision: Union[str, None] = '20250813_add_candidate_vectors'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Add show_position_stats column to user_preferences table"""
    
    # Add the new column with default value True
    op.add_column('user_preferences', 
        sa.Column('show_position_stats', sa.Boolean(), 
                 nullable=False, server_default=sa.text('true'))
    )
    
    # Remove server_default after adding the column (best practice)
    op.alter_column('user_preferences', 'show_position_stats', 
                   server_default=None)


def downgrade() -> None:
    """Remove show_position_stats column from user_preferences table"""
    
    op.drop_column('user_preferences', 'show_position_stats')