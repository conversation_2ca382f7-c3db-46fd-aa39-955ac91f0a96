"""auto_merge_heads

Revision ID: c448a8da7fe6
Revises: 2f94ae4fc9da
Create Date: 2025-08-11 03:54:19.654269

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'c448a8da7fe6'
down_revision: Union[str, None] = '2f94ae4fc9da'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass