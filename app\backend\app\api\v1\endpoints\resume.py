"""
Resume Parsing API Endpoints for Sprint 4
Handles resume parsing with AI integration and authentication
"""
import logging
from typing import Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, File, UploadFile, Form
from sqlalchemy.ext.asyncio import AsyncSession
from uuid import UUID
import base64
import asyncio

from app.core.database import get_db
from app.core.exceptions import bad_request, internal_server_error, service_error, not_found
from app.core.permissions import check_permission
from app.core.enums import Permission
from app.core.id_resolver import validate_candidate_id  # 添加ID解析器
from app.models.user import User
from app.models.resume import ResumeFile, ResumeVersionLog
from app.schemas.matching import (
    ResumeParseRequest,
    ResumeParseResponse, 
    ResumeParsePreviewRequest,
    TaskStatusResponse
)
from app.services.resume_parser import resume_parser
from app.services.resume_version_service import ResumeVersionService
from app.services.storage_service import storage_service
from sqlalchemy import select, desc
from fastapi.responses import RedirectResponse

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post("/parse", response_model=ResumeParseResponse, tags=["Resume"])
async def parse_resume(
    *,
    request: ResumeParseRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.AI_ASSESS)),
) -> Any:
    """
    Parse a single resume from base64-encoded file
    
    **Required Permission:** `ai:assess`
    
    **Supported formats:** PDF, DOCX, DOC, JPG, PNG, TXT
    
    **Request Body:**
    - `file_content`: Base64 encoded file content
    - `file_type`: File extension (pdf, docx, doc, jpg, png, txt)
    - `candidate_id`: Optional UUID to associate with candidate
    
    **Returns:**
    - Parsed structured data
    - Raw extracted text
    - Confidence score (0.0-1.0)
    - Parser version and metadata
    """
    try:
        logger.info(f"User {current_user.id} initiated resume parsing for candidate {request.candidate_id}")
        
        # Parse resume using service
        result = await resume_parser.parse_resume(request)
        
        logger.info(f"Resume parsing completed with confidence {result.confidence}")
        return result
        
    except ValueError as e:
        logger.error(f"Invalid resume parsing request: {str(e)}")
        raise bad_request("RESUME_PARSE_INVALID_INPUT", str(e))
        
    except Exception as e:
        logger.error(f"Resume parsing failed: {str(e)}")
        raise service_error("RESUME_PARSE_SERVICE_ERROR", "Failed to parse resume")


@router.post("/preview", response_model=ResumeParseResponse, tags=["Resume"])
async def preview_resume_parsing(
    *,
    request: ResumeParsePreviewRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.AI_ASSESS)),
) -> Any:
    """
    Preview resume parsing without saving to database
    
    **Required Permission:** `ai:assess`
    
    **Purpose:** Test parsing before committing to save the result
    
    **Request Body:**
    - `file_content`: Base64 encoded file content
    - `file_type`: File extension
    - `include_confidence`: Whether to include confidence calculation
    
    **Returns:**
    - Parsed data preview
    - Confidence score if requested
    - No database association created
    """
    try:
        logger.info(f"User {current_user.id} requested resume parsing preview")
        
        # Parse resume without saving
        result = await resume_parser.preview_resume(request)
        
        logger.info(f"Resume preview completed with confidence {result.confidence}")
        return result
        
    except ValueError as e:
        logger.error(f"Invalid preview request: {str(e)}")
        raise bad_request("RESUME_PREVIEW_INVALID_INPUT", str(e))
        
    except Exception as e:
        logger.error(f"Resume preview failed: {str(e)}")
        raise service_error("RESUME_PREVIEW_SERVICE_ERROR", "Failed to preview resume parsing")


@router.post("/upload", tags=["Resume"]) 
async def upload_and_parse_resume(
    *,
    file: UploadFile = File(...),
    candidate_id: Optional[str] = Form(None),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.AI_ASSESS)),
) -> Any:
    """
    Upload and parse resume file directly
    
    **Required Permission:** `ai:assess`
    
    **Form Data:**
    - `file`: Resume file to upload (PDF, DOCX, DOC, JPG, PNG, TXT)
    - `candidate_id`: Optional UUID string to associate with candidate
    
    **File Size Limit:** 10MB
    
    **Returns:**
    - Parsed structured data
    - Raw extracted text
    - Confidence score
    """
    try:
        # Validate file size (10MB limit)
        if file.size and file.size > 10 * 1024 * 1024:
            raise bad_request("RESUME_FILE_TOO_LARGE", "File size must be less than 10MB")
        
        # Validate file type
        if not file.filename:
            raise bad_request("RESUME_FILENAME_MISSING", "Filename is required")
            
        file_extension = file.filename.split('.')[-1].lower()
        allowed_extensions = ['pdf', 'docx', 'doc', 'jpg', 'png', 'txt']
        
        if file_extension not in allowed_extensions:
            raise bad_request(
                "RESUME_INVALID_FILE_TYPE", 
                f"File type .{file_extension} not supported. Allowed: {allowed_extensions}"
            )
        
        logger.info(f"User {current_user.id} uploading resume file: {file.filename}")
        
        # Read file content
        file_content = await file.read()
        
        # Encode to base64
        base64_content = base64.b64encode(file_content).decode('utf-8')
        
        # Parse candidate_id if provided - 支持Snowflake ID和UUID
        resolved_candidate_id = None
        if candidate_id:
            resolved_candidate_id = validate_candidate_id(candidate_id)
            if resolved_candidate_id is None:
                raise bad_request("RESUME_INVALID_CANDIDATE_ID", "Invalid candidate ID format")
        
        # Create parse request
        parse_request = ResumeParseRequest(
            file_content=base64_content,
            file_type=file_extension,
            candidate_id=resolved_candidate_id  # 使用解析后的ID
        )
        
        # Create version service and upload file
        version_service = ResumeVersionService(db)
        
        # Create new version
        resume_file = await version_service.create_new_version(
            candidate_id=resolved_candidate_id,
            file_data=file_content,
            filename=file.filename,
            file_type=file_extension,
            user_id=current_user.id,
            upload_method="manual"
        )
        
        if not resume_file:
            raise service_error("RESUME_UPLOAD_FAILED", "Failed to upload resume file")
        
        # Parse resume asynchronously (optional)
        try:
            result = await resume_parser.parse_resume(parse_request)
            # Update resume file with parsing results
            resume_file.parsed_data = result.structured_data
            resume_file.raw_text = result.raw_text
            resume_file.parsing_confidence = result.confidence
            resume_file.parsing_status = "completed"
            from datetime import datetime, timezone
            resume_file.parsed_at = datetime.now(timezone.utc)
            await db.commit()
        except Exception as e:
            logger.warning(f"Failed to parse resume: {e}")
            resume_file.parsing_status = "failed"
            await db.commit()
        
        logger.info(f"Resume upload completed: {file.filename} v{resume_file.version}")
        
        return {
            "id": resume_file.id,
            "candidate_id": str(resume_file.candidate_id),
            "version": resume_file.version,
            "filename": resume_file.filename,
            "file_size": resume_file.file_size,
            "status": resume_file.parsing_status,
            "confidence": resume_file.parsing_confidence,
            "created_at": resume_file.created_at.isoformat() if resume_file.created_at else None
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Resume upload failed: {str(e)}")
        raise service_error("RESUME_UPLOAD_SERVICE_ERROR", "Failed to upload and parse resume")


@router.post("/batch", response_model=List[ResumeParseResponse], tags=["Resume"])
async def batch_parse_resumes(
    *,
    requests: List[ResumeParseRequest],
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.AI_ASSESS)),
) -> Any:
    """
    Parse multiple resumes in batch
    
    **Required Permission:** `ai:assess`
    
    **Request Body:** Array of resume parse requests
    
    **Batch Limit:** 10 resumes per request
    
    **Processing:** Parallel processing for better performance
    
    **Returns:**
    - Array of parsed results
    - Failed parsings return with confidence 0.0 and empty data
    """
    try:
        # Validate batch size
        if len(requests) > 10:
            raise bad_request("RESUME_BATCH_TOO_LARGE", "Maximum 10 resumes per batch request")
        
        if not requests:
            raise bad_request("RESUME_BATCH_EMPTY", "At least one resume required")
        
        logger.info(f"User {current_user.id} initiated batch parsing of {len(requests)} resumes")
        
        # Process batch
        results = await resume_parser.batch_parse(requests)
        
        successful_count = sum(1 for r in results if r.confidence > 0.0)
        logger.info(f"Batch parsing completed: {successful_count}/{len(requests)} successful")
        
        return results
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Batch resume parsing failed: {str(e)}")
        raise service_error("RESUME_BATCH_SERVICE_ERROR", "Failed to process batch resume parsing")


@router.get("/test-ai-connection", tags=["Resume"])
async def test_ai_connection(
    *,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.AI_ASSESS)),
) -> Any:
    """
    Test AI service connections for resume parsing
    
    **Required Permission:** `ai:assess`
    
    **Returns:**
    - OpenAI/DeepSeek connection status
    - Ollama connection status  
    - Available models
    - Service health information
    """
    try:
        logger.info(f"User {current_user.id} testing AI connections")
        
        # Test basic functionality with simple text
        test_request = ResumeParsePreviewRequest(
            file_content=base64.b64encode(b"John Doe\nSoftware Engineer\<EMAIL>").decode(),
            file_type="txt",
            include_confidence=False
        )
        
        # Try parsing with current AI setup
        try:
            result = await resume_parser.preview_resume(test_request)
            ai_status = {
                "status": "healthy",
                "message": "AI services are operational",
                "test_confidence": result.confidence
            }
        except Exception as e:
            ai_status = {
                "status": "degraded",
                "message": f"AI service issue: {str(e)}",
                "test_confidence": 0.0
            }
        
        return {
            "ai_services": ai_status,
            "parser_version": "v1.0",
            "supported_formats": ["pdf", "docx", "doc", "jpg", "png", "txt"],
            "timestamp": "2024-01-10T00:00:00Z"
        }
        
    except Exception as e:
        logger.error(f"AI connection test failed: {str(e)}")
        return {
            "ai_services": {
                "status": "unhealthy",
                "message": f"Connection test failed: {str(e)}",
                "test_confidence": 0.0
            },
            "parser_version": "v1.0",
            "error": str(e)
        }


@router.get("/supported-formats", tags=["Resume"])
async def get_supported_formats(
    current_user: User = Depends(check_permission(Permission.AI_ASSESS)),
) -> Any:
    """
    Get supported file formats and limitations
    
    **Required Permission:** `ai:assess`
    
    **Returns:**
    - Supported file formats
    - File size limits
    - Processing capabilities
    - AI model information
    """
    return {
        "supported_formats": {
            "pdf": {
                "description": "Portable Document Format",
                "max_size_mb": 10,
                "text_extraction": "PyPDF2",
                "ai_processing": True
            },
            "docx": {
                "description": "Microsoft Word Document",
                "max_size_mb": 10, 
                "text_extraction": "python-docx",
                "ai_processing": True
            },
            "doc": {
                "description": "Legacy Microsoft Word Document", 
                "max_size_mb": 10,
                "text_extraction": "python-docx",
                "ai_processing": True
            },
            "jpg": {
                "description": "JPEG Image",
                "max_size_mb": 10,
                "text_extraction": "OCR (Tesseract)",
                "ai_processing": True,
                "languages": ["chi_sim", "eng"]
            },
            "png": {
                "description": "PNG Image", 
                "max_size_mb": 10,
                "text_extraction": "OCR (Tesseract)",
                "ai_processing": True,
                "languages": ["chi_sim", "eng"]
            },
            "txt": {
                "description": "Plain Text",
                "max_size_mb": 10,
                "text_extraction": "Direct",
                "ai_processing": True
            }
        },
        "ai_capabilities": {
            "structured_extraction": True,
            "multilingual_support": True,
            "confidence_scoring": True,
            "fallback_regex": True
        },
        "batch_limits": {
            "max_files_per_request": 10,
            "recommended_concurrent": 3
        }
    }


@router.get("/candidates/{candidate_id}/resumes/", tags=["Resume"])
async def get_candidate_resumes(
    candidate_id: str,
    include_deleted: bool = False,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.AI_ASSESS)),
) -> Any:
    """
    获取候选人的所有简历版本
    
    **Required Permission:** `ai:assess`
    
    **Parameters:**
    - `candidate_id`: 候选人ID (支持Snowflake ID或UUID)
    - `include_deleted`: 是否包含已删除的简历
    
    **Returns:**
    - 简历文件列表，包含版本信息
    """
    # 解析候选人ID
    resolved_id = validate_candidate_id(candidate_id)
    if not resolved_id:
        raise bad_request("INVALID_CANDIDATE_ID", "Invalid candidate ID format")
    
    # 创建版本服务
    version_service = ResumeVersionService(db)
    
    # 获取简历版本列表
    resumes = await version_service.get_versions(
        candidate_id=resolved_id,
        include_deleted=include_deleted
    )
    
    logger.info(f"获取候选人 {resolved_id} 的简历列表: {len(resumes)} 个版本")
    
    # 构建响应
    return [
        {
            "id": r.id,
            "version": r.version,
            "filename": r.filename,
            "file_size": r.file_size,
            "file_type": r.file_type,
            "is_active": r.is_active,
            "upload_time": r.created_at.isoformat() if r.created_at else None,
            "parsing_status": r.parsing_status,
            "confidence": r.parsing_confidence,
            "upload_method": r.upload_method,
            "parsed_at": r.parsed_at.isoformat() if r.parsed_at else None
        }
        for r in resumes
    ]


@router.get("/resume/{resume_id}/download/", tags=["Resume"])
async def download_resume(
    resume_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.AI_ASSESS)),
) -> Any:
    """
    下载简历文件
    
    **Required Permission:** `ai:assess`
    
    **Parameters:**
    - `resume_id`: 简历文件ID
    
    **Returns:**
    - 重定向到预签名下载URL
    """
    # 获取简历记录
    resume = await db.get(ResumeFile, resume_id)
    if not resume or resume.deleted_at:
        raise not_found("RESUME_NOT_FOUND", "Resume file not found")
    
    # 检查权限（可选）
    # TODO: 添加权限检查逻辑
    
    # 生成预签名URL（1小时有效）
    presigned_url = storage_service.generate_presigned_url(
        object_key=resume.file_key,
        expires_in=3600
    )
    
    if not presigned_url:
        raise service_error("DOWNLOAD_FAILED", "Failed to generate download URL")
    
    logger.info(f"用户 {current_user.id} 下载简历 {resume_id}")
    
    # 返回重定向响应
    return RedirectResponse(url=presigned_url, status_code=302)


@router.post("/resume/{resume_id}/activate/", tags=["Resume"])
async def activate_resume_version(
    resume_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.AI_ASSESS)),
) -> Any:
    """
    激活指定版本的简历
    
    **Required Permission:** `ai:assess`
    
    **Parameters:**
    - `resume_id`: 简历文件ID
    
    **Returns:**
    - 操作结果
    """
    version_service = ResumeVersionService(db)
    
    success = await version_service.activate_version(
        resume_id=resume_id,
        user_id=current_user.id
    )
    
    if not success:
        raise service_error("ACTIVATION_FAILED", "Failed to activate resume version")
    
    logger.info(f"用户 {current_user.id} 激活简历版本 {resume_id}")
    
    return {"success": True, "message": "Resume version activated successfully"}


@router.delete("/resume/{resume_id}/", tags=["Resume"])
async def delete_resume_version(
    resume_id: int,
    permanent: bool = False,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.AI_ASSESS)),
) -> Any:
    """
    删除指定版本的简历
    
    **Required Permission:** `ai:assess`
    
    **Parameters:**
    - `resume_id`: 简历文件ID
    - `permanent`: 是否永久删除（默认为软删除）
    
    **Returns:**
    - 操作结果
    """
    version_service = ResumeVersionService(db)
    
    success = await version_service.delete_version(
        resume_id=resume_id,
        user_id=current_user.id,
        permanent=permanent
    )
    
    if not success:
        raise service_error("DELETE_FAILED", "Failed to delete resume version")
    
    action = "permanently deleted" if permanent else "deleted"
    logger.info(f"用户 {current_user.id} {action} 简历版本 {resume_id}")
    
    return {"success": True, "message": f"Resume version {action} successfully"}


@router.get("/resume/{resume_id}/compare/{compare_id}/", tags=["Resume"])
async def compare_resume_versions(
    resume_id: int,
    compare_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.AI_ASSESS)),
) -> Any:
    """
    比较两个简历版本的差异
    
    **Required Permission:** `ai:assess`
    
    **Parameters:**
    - `resume_id`: 第一个简历版本ID
    - `compare_id`: 第二个简历版本ID
    
    **Returns:**
    - 版本差异信息
    """
    version_service = ResumeVersionService(db)
    
    diff = await version_service.compare_versions(
        version1_id=resume_id,
        version2_id=compare_id
    )
    
    if not diff:
        raise not_found("VERSIONS_NOT_FOUND", "One or both resume versions not found")
    
    logger.info(f"用户 {current_user.id} 比较简历版本 {resume_id} 和 {compare_id}")
    
    return diff