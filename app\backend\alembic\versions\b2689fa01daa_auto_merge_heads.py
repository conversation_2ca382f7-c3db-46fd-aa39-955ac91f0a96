"""auto_merge_heads

Revision ID: b2689fa01daa
Revises: 87768550447f
Create Date: 2025-08-07 12:35:36.247335

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'b2689fa01daa'
down_revision: Union[str, None] = '87768550447f'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass