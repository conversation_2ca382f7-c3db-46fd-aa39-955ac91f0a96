"""auto_merge_heads

Revision ID: 8af548f6adf5
Revises: c448a8da7fe6
Create Date: 2025-08-11 03:55:20.557875

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '8af548f6adf5'
down_revision: Union[str, None] = 'c448a8da7fe6'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass