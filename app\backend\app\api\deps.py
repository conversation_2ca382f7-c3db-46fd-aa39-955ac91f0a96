"""
API dependencies
"""
from typing import Optional
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON>A<PERSON><PERSON><PERSON><PERSON>wordBearer
from jose import JW<PERSON>rror, jwt
from pydantic import ValidationError
from sqlalchemy.ext.asyncio import AsyncSession
from app.core import config, security
from app.core.database import get_db
from app.core.exceptions import unauthorized, forbidden
from app.crud import user as user_crud
from app.models.user import User
from app.schemas.token import TokenPayload

# OAuth2 scheme
oauth2_scheme = OAuth2PasswordBearer(tokenUrl=f"{config.settings.API_V1_STR}/auth/login")


async def get_current_user(
    db: AsyncSession = Depends(get_db),
    token: str = Depends(oauth2_scheme)
) -> User:
    """
    Get current authenticated user
    
    Args:
        db: Database session
        token: JWT access token (or dev bypass token in development)
    
    Returns:
        Current user object
    
    Raises:
        HTTPException: If token is invalid or user not found
    """
    # Development bypass token support
    if config.settings.ENVIRONMENT in ("development", "dev") and token == config.settings.DEV_BYPASS_TOKEN:
        # Return the first superuser as dev user
        dev_user = await user_crud.get_by_email(db, email="<EMAIL>")
        if dev_user and dev_user.is_active:
            return dev_user
        else:
            # Fallback: return first active superuser
            all_superusers = await user_crud.get_superusers(db)
            if all_superusers:
                return all_superusers[0]
            else:
                raise unauthorized("DEV_TOKEN_NO_SUPERUSER_AVAILABLE")
    
    # Normal JWT token validation
    try:
        payload = security.decode_token(token)
        token_data = TokenPayload(**payload)
        
        if token_data.type != "access":
            raise unauthorized("AUTH_TOKEN_INVALID_TYPE")
        
    except (JWTError, ValidationError):
        raise unauthorized("AUTH_TOKEN_VALIDATION_FAILED")
    
    # Get user from database
    # Convert string user_id to int (bigint in database)
    try:
        user_id = int(token_data.sub) if token_data.sub else None
    except (ValueError, TypeError):
        raise unauthorized("AUTH_TOKEN_INVALID_USER_ID")
    
    if not user_id:
        raise unauthorized("AUTH_TOKEN_USER_ID_MISSING")
    
    user = await user_crud.get(db, id=user_id)
    if not user:
        raise unauthorized("AUTH_USER_NOT_FOUND")
    
    if not user.is_active:
        raise forbidden("AUTH_USER_INACTIVE")
    
    return user


async def get_current_active_user(
    current_user: User = Depends(get_current_user),
) -> User:
    """
    Get current active user
    
    Args:
        current_user: Current user from get_current_user
    
    Returns:
        Current active user
    
    Raises:
        HTTPException: If user is not active
    """
    if not current_user.is_active:
        raise forbidden("AUTH_USER_INACTIVE")
    return current_user


async def get_current_active_superuser(
    current_user: User = Depends(get_current_user),
) -> User:
    """
    Get current active superuser
    
    Args:
        current_user: Current user from get_current_user
    
    Returns:
        Current superuser
    
    Raises:
        HTTPException: If user is not superuser
    """
    if not current_user.is_superuser:
        raise forbidden("AUTH_INSUFFICIENT_PERMISSIONS")
    return current_user


class PermissionChecker:
    """
    Permission dependency class
    
    Usage:
        @router.get("/", dependencies=[Depends(PermissionChecker("users:read"))])
    """
    
    def __init__(self, permission: str):
        self.permission = permission
    
    async def __call__(
        self,
        current_user: User = Depends(get_current_active_user)
    ) -> bool:
        """Check if user has required permission"""
        if not current_user.has_permission(self.permission):
            raise forbidden("AUTH_PERMISSION_REQUIRED", details={"permission": self.permission})
        return True


# Convenience functions for common permissions
def require_permission(permission: str) -> PermissionChecker:
    """Require specific permission"""
    return PermissionChecker(permission)


def check_permission(permission: str):
    """
    Create a dependency that checks if user has specific permission
    
    Args:
        permission: Permission string to check
        
    Returns:
        Function that validates permission and returns current user
    """
    async def _check_permission(
        current_user: User = Depends(get_current_active_user)
    ) -> User:
        if not current_user.has_permission(permission):
            raise forbidden("AUTH_PERMISSION_REQUIRED", details={"permission": permission})
        return current_user
    
    return _check_permission