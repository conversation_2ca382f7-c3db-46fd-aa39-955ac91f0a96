# Questionnaire API 500 Error - Repository Analysis Report

**Generated**: 2025-08-15 08:25:00 UTC
**Issue**: HTTP 500 Internal Server Error on GET /api/v1/questionnaires/
**Error**: ProgrammingError - column questionnaires.category does not exist

## Executive Summary

The questionnaire API is failing due to a **database schema mismatch**. The SQLAlchemy model expects an enhanced questionnaire schema with advanced features (categories, sections, advanced scoring), but the actual database table was created with a simple legacy schema missing critical columns.

## Project Architecture Analysis

### Technology Stack
- **Backend**: Python 3.12, FastAPI 0.110+, SQLAlchemy 2.0 (async)
- **Frontend**: Next.js 15.4.1, React 19, TypeScript 5.x
- **Database**: PostgreSQL 17 with pgvector 0.8.0
- **Infrastructure**: <PERSON>er <PERSON>se, <PERSON>, <PERSON><PERSON>, <PERSON>lla<PERSON>, Nginx

### Directory Structure
```
talent_forge_pro/
├── app/backend/          # FastAPI backend service
├── app/frontend/         # Next.js frontend application  
├── app/configs/          # Configuration files (nginx, etc.)
├── app/docker-compose.yml # Service orchestration
├── docs/                 # Project documentation
└── examples/            # Code examples and patterns
```

## Root Cause Analysis

### Database Schema Mismatch

**Current Database Schema** (from migration `05e30b49ff35`):
```sql
CREATE TABLE questionnaires (
    id BIGINT PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    questions JSONB NOT NULL,           -- Legacy: stores questions as JSON
    scoring_enabled BOOLEAN DEFAULT false,
    passing_score DOUBLE PRECISION,
    max_score DOUBLE PRECISION,
    time_limit_minutes INTEGER,
    randomize_questions BOOLEAN DEFAULT false,
    show_progress BOOLEAN DEFAULT true,
    allow_back_navigation BOOLEAN DEFAULT true,
    completion_message TEXT,
    show_score_on_completion BOOLEAN DEFAULT false,
    created_by BIGINT NOT NULL,
    is_active BOOLEAN DEFAULT true,     -- Legacy status field
    response_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);
```

**Expected Schema** (from SQLAlchemy model):
```sql
-- Missing columns that cause the ProgrammingError:
category             ENUM(QuestionnaireCategory) DEFAULT 'survey',
version              INTEGER DEFAULT 1,
updated_by           BIGINT REFERENCES users(id),
status               ENUM(QuestionnaireStatus) DEFAULT 'draft',
published_at         TIMESTAMP WITH TIME ZONE,
archived_at          TIMESTAMP WITH TIME ZONE,
theme                VARCHAR(50) DEFAULT 'default',
branding             JSON DEFAULT '{}',
-- ... plus 20+ additional advanced feature columns
```

### API Implementation Status

**✅ Properly Configured**:
- FastAPI router registered at `/api/v1/questionnaires/`
- CRUD operations implemented for enhanced model
- Pydantic schemas for advanced features
- Authentication and permission checks
- API endpoints for full lifecycle management

**❌ Database Schema Issues**:
- Missing `category` column (immediate cause of 500 error)
- Missing `version`, `updated_by`, `status` columns
- Missing advanced feature columns (20+ fields)
- Legacy `questions` JSONB field conflicts with new relational design
- Status field mismatch: `is_active` vs `status` enum

## Detailed Error Analysis

### SQL Query Failure
```sql
-- This query fails because 'category' column doesn't exist:
SELECT questionnaires.id, questionnaires.title, questionnaires.description, 
       questionnaires.category,  -- ❌ MISSING COLUMN
       questionnaires.version,   -- ❌ MISSING COLUMN  
       questionnaires.status,    -- ❌ MISSING COLUMN
       -- ... more missing columns
FROM questionnaires 
LIMIT 10 OFFSET 0;
```

### Code vs Database Mismatch

**SQLAlchemy Model Expectations**:
```python
class Questionnaire(Base):
    category = Column(SQLEnum(QuestionnaireCategory), default=QuestionnaireCategory.SURVEY)
    version = Column(Integer, default=1)
    status = Column(SQLEnum(QuestionnaireStatus), default=QuestionnaireStatus.DRAFT)
    updated_by = Column(BigInteger, ForeignKey("users.id"))
    # ... 20+ more advanced fields
```

**Actual Database Table**:
- Has `is_active` boolean instead of `status` enum
- Missing `category`, `version`, `updated_by` entirely
- Uses legacy `questions` JSONB approach vs relational design

## Impact Assessment

### Current Failures
- **GET /api/v1/questionnaires/** → 500 Internal Server Error
- **All questionnaire CRUD operations** → Will fail on schema mismatch
- **Frontend questionnaire features** → Cannot load questionnaire data

### Affected Components
1. **Backend API** - All questionnaire endpoints non-functional
2. **Frontend** - Questionnaire management UI broken
3. **Web Application Forms** - Public questionnaire features unusable
4. **Admin Panel** - Questionnaire administration not working

## Solution Architecture

### Migration Strategy Options

#### Option 1: Database Migration (Recommended)
Create Alembic migration to add missing columns:

```sql
-- Add missing columns to match model
ALTER TABLE questionnaires 
  ADD COLUMN category VARCHAR(20) DEFAULT 'survey',
  ADD COLUMN version INTEGER DEFAULT 1,  
  ADD COLUMN updated_by BIGINT REFERENCES users(id),
  ADD COLUMN status VARCHAR(20) DEFAULT 'draft',
  ADD COLUMN published_at TIMESTAMP WITH TIME ZONE,
  ADD COLUMN archived_at TIMESTAMP WITH TIME ZONE,
  -- ... add remaining columns
```

#### Option 2: Model Downgrade (Alternative)
Modify SQLAlchemy model to match existing schema:
- Remove missing columns from model
- Use `is_active` instead of `status` 
- Maintain backward compatibility

### Recommended Implementation Plan

1. **Immediate Fix** (15 minutes):
   - Add missing columns via Alembic migration
   - Migrate data from legacy format
   - Test basic CRUD operations

2. **Data Migration** (30 minutes):
   - Convert `is_active` → `status` mapping
   - Handle existing questionnaire data
   - Preserve backward compatibility

3. **Full System Test** (15 minutes):
   - Test all API endpoints
   - Verify frontend integration
   - Validate public questionnaire access

## Next Steps

### Priority 1: Fix Database Schema
```bash
# Create and run migration
alembic revision --autogenerate -m "add_missing_questionnaire_columns"
alembic upgrade head
```

### Priority 2: Test API Endpoints
```bash
# Test with dev token
curl -H "Authorization: Bearer dev_bypass_token_2025_talentforge" \
     http://localhost:8088/api/v1/questionnaires/
```

### Priority 3: Frontend Integration
- Ensure frontend can consume new API structure
- Update questionnaire management components
- Test public questionnaire forms

## Files Requiring Attention

### Backend Files
- `app/backend/alembic/versions/` - Create new migration
- `app/backend/app/models/questionnaire.py` - Verify model consistency
- `app/backend/app/crud/questionnaire.py` - Test CRUD operations
- `app/backend/app/api/v1/questionnaires.py` - Validate API endpoints

### Frontend Files
- `app/frontend/services/questionnaire.service.ts` - Update API client
- `app/frontend/components/survey-builder/` - Questionnaire UI components
- `app/frontend/types/index.ts` - Update TypeScript interfaces

## Estimated Fix Time
- **Database Schema Fix**: 15-30 minutes
- **API Testing & Validation**: 15 minutes  
- **Frontend Integration**: 15-30 minutes
- **Full System Testing**: 15 minutes
- **Total**: 1-1.5 hours

The core issue is straightforward to fix with a proper database migration, and the existing code architecture is well-designed for the intended advanced questionnaire features.