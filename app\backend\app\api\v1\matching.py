"""
Matching API endpoints for Sprint 4 Intelligent Recruitment System
Handles resume parsing, candidate search, and batch matching operations
"""
import base64
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime

from fastapi import APIRouter, Depends, UploadFile, File, HTTPException, BackgroundTasks, Query
from fastapi.responses import JSONResponse
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel, Field, validator

from app.api.deps import get_db, get_current_active_user
from app.models import User
from app.services.resume_parsing_service import resume_parsing_service
from app.services.matching_service import matching_service
from app.services.celery_monitoring import celery_monitoring
from app.tasks import (
    parse_resume_async,
    batch_resume_parsing, 
    batch_candidate_matching,
    batch_generate_embeddings
)
from app.core.exceptions import bad_request, service_error, not_found
from app.api.deps import PermissionChecker

logger = logging.getLogger(__name__)
router = APIRouter()

# Request/Response Models

class ParseResumeRequest(BaseModel):
    """Request model for resume parsing"""
    filename: str = Field(..., description="Original filename")
    candidate_id: Optional[int] = Field(None, description="Existing candidate ID to update")
    generate_embeddings: bool = Field(True, description="Generate vector embeddings")
    embedding_provider: Optional[str] = Field(None, description="Preferred embedding provider")
    async_processing: bool = Field(False, description="Process asynchronously")


class ParseResumeResponse(BaseModel):
    """Response model for resume parsing"""
    success: bool
    candidate_id: Optional[int] = None
    task_id: Optional[str] = None  # For async processing
    parsing_result: Optional[Dict[str, Any]] = None
    message: str


class CandidateSearchRequest(BaseModel):
    """Request model for candidate search"""
    position_id: int = Field(..., description="Position ID to match against")
    limit: int = Field(50, ge=1, le=200, description="Maximum candidates to return")
    similarity_threshold: float = Field(0.7, ge=0.0, le=1.0, description="Minimum similarity score")
    include_assessments: bool = Field(True, description="Include assessment data")
    include_jfs_scores: bool = Field(True, description="Include JFS scores")
    filters: Optional[Dict[str, Any]] = Field(None, description="Additional search filters")


class CandidateSearchResponse(BaseModel):
    """Response model for candidate search"""
    success: bool
    position_id: int
    candidates_found: int
    query_time_ms: float
    candidates: List[Dict[str, Any]]
    search_metadata: Dict[str, Any]


class BatchMatchingRequest(BaseModel):
    """Request model for batch candidate matching"""
    position_id: int = Field(..., description="Position ID to match against")
    limit: int = Field(100, ge=1, le=500, description="Maximum candidates to return")
    similarity_threshold: float = Field(0.7, ge=0.0, le=1.0, description="Minimum similarity score")
    include_assessments: bool = Field(True, description="Include assessment data")
    include_jfs_scores: bool = Field(True, description="Include JFS scores")
    priority: str = Field("normal", pattern="^(low|normal|high)$", description="Task priority")


class BatchMatchingResponse(BaseModel):
    """Response model for batch matching"""
    success: bool
    task_id: str
    position_id: int
    estimated_completion: str
    message: str


class BatchEmbeddingRequest(BaseModel):
    """Request model for batch embedding generation"""
    candidate_ids: List[int] = Field(..., min_items=1, max_items=100, description="Candidate IDs to process")
    force_regenerate: bool = Field(False, description="Force regeneration of existing embeddings")
    embedding_provider: Optional[str] = Field(None, description="Preferred embedding provider")
    max_concurrent: int = Field(5, ge=1, le=10, description="Maximum concurrent operations")


class BatchEmbeddingResponse(BaseModel):
    """Response model for batch embedding generation"""
    success: bool
    task_id: str
    candidate_count: int
    estimated_completion: str
    message: str


class TaskStatusResponse(BaseModel):
    """Response model for task status"""
    task_id: str
    status: str
    result: Optional[Dict[str, Any]] = None
    progress: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    created_at: str
    completed_at: Optional[str] = None


# API Endpoints

@router.post("/parse-resume", response_model=ParseResumeResponse, tags=["Resume Parsing"])
async def parse_resume(
    request: ParseResumeRequest,
    file: UploadFile = File(...),
    background_tasks: BackgroundTasks = None,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Parse uploaded resume with LLM enhancement and vector embeddings
    
    Supports both synchronous and asynchronous processing:
    - Sync: Returns immediate results, suitable for single resumes
    - Async: Returns task ID for monitoring, suitable for large files or batch operations
    """
    try:
        # Validate file format
        allowed_formats = {'.pdf', '.docx', '.doc', '.txt'}
        file_ext = '.' + request.filename.lower().split('.')[-1] if '.' in request.filename else ''
        
        if file_ext not in allowed_formats:
            raise bad_request("PARSER_UNSUPPORTED_FILE_FORMAT")
        
        # Validate file size (50MB limit)
        file_content = await file.read()
        if len(file_content) > 50 * 1024 * 1024:  # 50MB
            raise bad_request("PARSER_FILE_TOO_LARGE")
        
        # Check permissions if updating existing candidate
        if request.candidate_id:
            from app.crud import candidate as candidate_crud
            candidate = await candidate_crud.get(db, id=request.candidate_id)
            if not candidate:
                raise not_found(f"CANDIDATE_NOT_FOUND: {request.candidate_id}")
            
            if not permission_checker.can_update_candidate(current_user.id, candidate):
                raise service_error("CANDIDATE_UPDATE_PERMISSION_DENIED")
        
        if request.async_processing:
            # Async processing with Celery
            try:
                file_content_base64 = base64.b64encode(file_content).decode('utf-8')
                
                task = parse_resume_async.delay(
                    file_content_base64=file_content_base64,
                    filename=request.filename,
                    user_id=current_user.id,
                    candidate_id=request.candidate_id,
                    generate_embeddings=request.generate_embeddings,
                    embedding_provider=request.embedding_provider
                )
                
                # Record task start
                await celery_monitoring.record_task_event(
                    task.id, 'task-submitted', 'parse_resume_async', 'parsing'
                )
                
                return ParseResumeResponse(
                    success=True,
                    task_id=task.id,
                    message="PARSER_ASYNC_TASK_STARTED"
                )
                
            except Exception as e:
                logger.error(f"Failed to start async resume parsing: {str(e)}")
                raise service_error(f"PARSER_ASYNC_START_FAILED: {str(e)}")
        else:
            # Synchronous processing
            result = await resume_parsing_service.parse_and_store_resume(
                db=db,
                file_content=file_content,
                filename=request.filename,
                user_id=current_user.id,
                candidate_id=request.candidate_id,
                generate_embeddings=request.generate_embeddings,
                embedding_provider=request.embedding_provider
            )
            
            return ParseResumeResponse(
                success=True,
                candidate_id=result["candidate_id"],
                parsing_result=result,
                message="PARSER_SYNC_COMPLETED_SUCCESSFULLY"
            )
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Resume parsing failed: {str(e)}")
        raise service_error(f"PARSER_GENERAL_ERROR: {str(e)}")


@router.post("/search-candidates", response_model=CandidateSearchResponse, tags=["Candidate Search"])
async def search_candidates(
    request: CandidateSearchRequest,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Search candidates using vector similarity matching against position requirements
    
    Fast response with <100ms target using pgvector HNSW indexing
    """
    try:
        # Verify position exists and user has access
        from app.crud import position as position_crud
        position = await position_crud.get(db, id=request.position_id)
        if not position:
            raise not_found(f"POSITION_NOT_FOUND: {request.position_id}")
        
        if not permission_checker.can_view_position(current_user.id, position):
            raise service_error("POSITION_VIEW_PERMISSION_DENIED")
        
        # Perform fast candidate search
        start_time = datetime.utcnow()
        
        search_result = await matching_service.fast_candidate_search(
            db=db,
            position_id=request.position_id,
            limit=request.limit,
            similarity_threshold=request.similarity_threshold,
            include_metadata=True,
            filters=request.filters
        )
        
        query_time = (datetime.utcnow() - start_time).total_seconds() * 1000  # ms
        
        # Enhance with assessment data if requested
        if request.include_assessments and search_result.get("candidates"):
            from app.services.assessment_service import assessment_service
            
            for candidate in search_result["candidates"]:
                try:
                    assessment = await assessment_service.get_latest_assessment(
                        db=db, candidate_id=candidate["candidate_id"]
                    )
                    if assessment:
                        candidate["assessment"] = {
                            "dci_score": assessment.dci_score,
                            "assessed_at": assessment.assessed_at.isoformat()
                        }
                except Exception as e:
                    logger.warning(f"Failed to get assessment for candidate {candidate['candidate_id']}: {str(e)}")
        
        # Calculate JFS scores if requested
        if request.include_jfs_scores and search_result.get("candidates"):
            from app.services.assessment_service import assessment_service
            
            for candidate in search_result["candidates"]:
                try:
                    jfs_result = await assessment_service.calculate_jfs_score(
                        db=db,
                        candidate_id=candidate["candidate_id"],
                        position_id=request.position_id
                    )
                    candidate["jfs_score"] = jfs_result.jfs_score
                    candidate["match_confidence"] = jfs_result.match_confidence
                except Exception as e:
                    logger.warning(f"Failed to calculate JFS for candidate {candidate['candidate_id']}: {str(e)}")
        
        return CandidateSearchResponse(
            success=True,
            position_id=request.position_id,
            candidates_found=len(search_result.get("candidates", [])),
            query_time_ms=round(query_time, 2),
            candidates=search_result.get("candidates", []),
            search_metadata={
                "similarity_threshold": request.similarity_threshold,
                "total_candidates_checked": search_result.get("total_candidates", 0),
                "vector_search_time_ms": search_result.get("query_time_ms", 0),
                "filters_applied": request.filters or {}
            }
        )
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Candidate search failed: {str(e)}")
        raise service_error(f"SEARCH_GENERAL_ERROR: {str(e)}")


@router.post("/batch-matching", response_model=BatchMatchingResponse, tags=["Batch Operations"])
async def start_batch_matching(
    request: BatchMatchingRequest,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Start batch candidate matching process for large-scale operations
    
    Returns task ID for monitoring progress via /task-status endpoint
    """
    try:
        # Verify position exists and user has access
        from app.crud import position as position_crud
        position = await position_crud.get(db, id=request.position_id)
        if not position:
            raise not_found(f"POSITION_NOT_FOUND: {request.position_id}")
        
        if not permission_checker.can_view_position(current_user.id, position):
            raise service_error("POSITION_VIEW_PERMISSION_DENIED")
        
        # Start batch matching task
        task = batch_candidate_matching.delay(
            position_id=request.position_id,
            user_id=current_user.id,
            limit=request.limit,
            similarity_threshold=request.similarity_threshold,
            include_assessments=request.include_assessments,
            include_jfs=request.include_jfs_scores
        )
        
        # Record task start
        await celery_monitoring.record_task_event(
            task.id, 'task-submitted', 'batch_candidate_matching', 'matching'
        )
        
        # Estimate completion time based on request size
        estimated_minutes = max(2, min(15, request.limit // 10))  # 2-15 minutes
        estimated_completion = datetime.utcnow().replace(microsecond=0)
        estimated_completion = estimated_completion.replace(minute=estimated_completion.minute + estimated_minutes)
        
        return BatchMatchingResponse(
            success=True,
            task_id=task.id,
            position_id=request.position_id,
            estimated_completion=estimated_completion.isoformat(),
            message="BATCH_MATCHING_TASK_STARTED"
        )
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Batch matching start failed: {str(e)}")
        raise service_error(f"BATCH_MATCHING_START_FAILED: {str(e)}")


@router.post("/batch-embeddings", response_model=BatchEmbeddingResponse, tags=["Batch Operations"])
async def start_batch_embedding_generation(
    request: BatchEmbeddingRequest,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Start batch embedding generation for multiple candidates
    
    Useful for retroactively generating embeddings for existing candidates
    """
    try:
        # Verify candidates exist and user has access
        from app.crud import candidate as candidate_crud
        
        accessible_candidates = []
        for candidate_id in request.candidate_ids:
            candidate = await candidate_crud.get(db, id=candidate_id)
            if candidate:
                if permission_checker.can_view_candidate(current_user.id, candidate):
                    accessible_candidates.append(candidate_id)
                else:
                    logger.warning(f"User {current_user.id} lacks permission for candidate {candidate_id}")
        
        if not accessible_candidates:
            raise bad_request("BATCH_EMBEDDING_NO_ACCESSIBLE_CANDIDATES")
        
        # Start batch embedding task
        task = batch_generate_embeddings.delay(
            candidate_ids=accessible_candidates,
            force_regenerate=request.force_regenerate,
            embedding_provider=request.embedding_provider,
            max_concurrent=request.max_concurrent
        )
        
        # Record task start
        await celery_monitoring.record_task_event(
            task.id, 'task-submitted', 'batch_generate_embeddings', 'matching'
        )
        
        # Estimate completion time based on candidate count and concurrency
        estimated_minutes = max(5, min(30, len(accessible_candidates) // request.max_concurrent))
        estimated_completion = datetime.utcnow()
        estimated_completion = estimated_completion.replace(minute=estimated_completion.minute + estimated_minutes)
        
        return BatchEmbeddingResponse(
            success=True,
            task_id=task.id,
            candidate_count=len(accessible_candidates),
            estimated_completion=estimated_completion.isoformat(),
            message="BATCH_EMBEDDING_TASK_STARTED"
        )
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Batch embedding start failed: {str(e)}")
        raise service_error(f"BATCH_EMBEDDING_START_FAILED: {str(e)}")


@router.get("/task-status/{task_id}", response_model=TaskStatusResponse, tags=["Task Monitoring"])
async def get_task_status(
    task_id: str,
    current_user: User = Depends(get_current_active_user)
):
    """
    Get status and results of async task
    
    Returns current status, progress information, and results when completed
    """
    try:
        # Get task result from Celery
        from celery.result import AsyncResult
        
        task_result = AsyncResult(task_id, app=parse_resume_async.app)
        
        if not task_result:
            raise not_found(f"TASK_NOT_FOUND: {task_id}")
        
        # Get task info
        task_info = task_result.info or {}
        
        response = TaskStatusResponse(
            task_id=task_id,
            status=task_result.status.lower(),
            created_at=datetime.utcnow().isoformat(),  # Placeholder - would need to track this
            result=task_info if task_result.successful() else None,
            error=str(task_info) if task_result.failed() else None
        )
        
        # Add completion time if task is done
        if task_result.status in ['SUCCESS', 'FAILURE']:
            response.completed_at = datetime.utcnow().isoformat()  # Placeholder
        
        # Add progress information for running tasks
        if task_result.status == 'PROGRESS' and isinstance(task_info, dict):
            response.progress = task_info.get('progress', {})
        
        return response
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Task status check failed for {task_id}: {str(e)}")
        raise service_error(f"TASK_STATUS_CHECK_FAILED: {str(e)}")


@router.post("/parse-resume-preview", tags=["Resume Parsing"])
async def parse_resume_preview(
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_active_user),
    include_raw_text: bool = Query(False, description="Include raw extracted text")
):
    """
    Preview resume parsing results without storing to database
    
    Useful for testing parser accuracy and previewing extracted data
    """
    try:
        # Validate file format
        allowed_formats = {'.pdf', '.docx', '.doc', '.txt'}
        file_ext = '.' + file.filename.lower().split('.')[-1] if '.' in file.filename else ''
        
        if file_ext not in allowed_formats:
            raise bad_request("PARSER_UNSUPPORTED_FILE_FORMAT")
        
        # Validate file size (10MB limit for preview)
        file_content = await file.read()
        if len(file_content) > 10 * 1024 * 1024:  # 10MB
            raise bad_request("PARSER_PREVIEW_FILE_TOO_LARGE")
        
        # Parse resume without storing
        result = await resume_parsing_service.parse_resume_only(
            file_content=file_content,
            filename=file.filename,
            include_raw_text=include_raw_text
        )
        
        return JSONResponse({
            "success": True,
            "message": "PARSER_PREVIEW_COMPLETED",
            "result": result
        })
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Resume preview parsing failed: {str(e)}")
        raise service_error(f"PARSER_PREVIEW_FAILED: {str(e)}")


@router.get("/matching-statistics", tags=["Analytics"])
async def get_matching_statistics(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get matching system statistics and performance metrics
    """
    try:
        # Get parsing service statistics
        parsing_stats = await resume_parsing_service.get_parsing_statistics(db)
        
        # Get Celery monitoring data
        health_data = await celery_monitoring.get_overall_health()
        
        # Get performance analytics for matching queue
        matching_analytics = await celery_monitoring.get_performance_analytics("matching")
        
        return JSONResponse({
            "success": True,
            "statistics": {
                "parsing_service": parsing_stats,
                "queue_health": health_data,
                "matching_performance": matching_analytics,
                "generated_at": datetime.utcnow().isoformat()
            }
        })
    
    except Exception as e:
        logger.error(f"Failed to get matching statistics: {str(e)}")
        raise service_error(f"MATCHING_STATISTICS_FAILED: {str(e)}")


@router.get("/queue-health", tags=["Monitoring"])
async def get_queue_health(
    current_user: User = Depends(get_current_active_user)
):
    """
    Get real-time queue health and worker status
    
    Useful for monitoring system performance and identifying bottlenecks
    """
    try:
        health_data = await celery_monitoring.get_overall_health()
        
        return JSONResponse({
            "success": True,
            "health": health_data,
            "timestamp": datetime.utcnow().isoformat()
        })
    
    except Exception as e:
        logger.error(f"Queue health check failed: {str(e)}")
        raise service_error(f"QUEUE_HEALTH_CHECK_FAILED: {str(e)}")


# Additional utility endpoints for development/debugging

@router.post("/test-embedding-providers", tags=["Development"])
async def test_embedding_providers(
    text: str = Query(..., description="Test text for embedding generation"),
    current_user: User = Depends(get_current_active_user)
):
    """Test all available embedding providers with sample text"""
    try:
        from app.services.embedding_service import embedding_service
        
        providers = embedding_service.get_supported_providers()
        results = {}
        
        for provider in providers:
            try:
                start_time = datetime.utcnow()
                embedding, metadata = await embedding_service.generate_single_embedding(
                    text=text, provider=provider
                )
                duration = (datetime.utcnow() - start_time).total_seconds() * 1000
                
                results[provider] = {
                    "success": True,
                    "dimension": len(embedding),
                    "generation_time_ms": round(duration, 2),
                    "metadata": metadata
                }
            except Exception as e:
                results[provider] = {
                    "success": False,
                    "error": str(e)
                }
        
        return JSONResponse({
            "success": True,
            "test_text": text,
            "provider_results": results,
            "timestamp": datetime.utcnow().isoformat()
        })
    
    except Exception as e:
        logger.error(f"Embedding provider test failed: {str(e)}")
        raise service_error(f"EMBEDDING_PROVIDER_TEST_FAILED: {str(e)}")