"""Fix user_preferences.user_id to BigInteger

Revision ID: 76f9bec42773
Revises: b37b7a4e0c04
Create Date: 2025-08-06 09:24:52.491426

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '76f9bec42773'
down_revision: Union[str, None] = 'b37b7a4e0c04'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('user_preferences', 'user_id',
               existing_type=sa.INTEGER(),
               type_=sa.BigInteger(),
               existing_nullable=False)
    op.alter_column('users', 'id',
               existing_type=sa.BIGINT(),
               server_default=None,
               existing_nullable=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('users', 'id',
               existing_type=sa.BIGINT(),
               server_default=sa.text("nextval('users_id_seq'::regclass)"),
               existing_nullable=False)
    op.alter_column('user_preferences', 'user_id',
               existing_type=sa.BigInteger(),
               type_=sa.INTEGER(),
               existing_nullable=False)
    # ### end Alembic commands ###