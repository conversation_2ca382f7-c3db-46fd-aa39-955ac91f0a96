"""auto_merge_heads

Revision ID: 6ee471bc5ba5
Revises: a7e666d752a4
Create Date: 2025-08-13 01:49:35.337055

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '6ee471bc5ba5'
down_revision: Union[str, None] = 'a7e666d752a4'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass