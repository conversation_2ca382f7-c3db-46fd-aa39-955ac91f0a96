"""
LLM Service for DeepSeek Integration with Rate Limiting
"""
import asyncio
import json
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
import httpx
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type

from app.core.config import settings

logger = logging.getLogger(__name__)


class RateLimitExceeded(Exception):
    """Raised when rate limit is exceeded"""
    pass


class RateLimiter:
    """Simple rate limiter for API calls"""
    
    def __init__(self, max_requests: int = 10, per_minutes: int = 1):
        self.max_requests = max_requests
        self.per_minutes = per_minutes
        self.requests: List[datetime] = []
        self.lock = asyncio.Lock()
    
    async def acquire(self):
        """Acquire permission to make a request"""
        async with self.lock:
            now = datetime.now()
            cutoff = now - timedelta(minutes=self.per_minutes)
            
            # Remove old requests
            self.requests = [req_time for req_time in self.requests if req_time > cutoff]
            
            # Check if we can make a request
            if len(self.requests) >= self.max_requests:
                wait_time = (self.requests[0] + timedelta(minutes=self.per_minutes) - now).total_seconds()
                if wait_time > 0:
                    logger.warning(f"Rate limit reached. Waiting {wait_time:.1f} seconds")
                    await asyncio.sleep(wait_time)
                    return await self.acquire()  # Retry after waiting
            
            # Record this request
            self.requests.append(now)


class LLMService:
    """Service for interacting with DeepSeek API"""
    
    def __init__(self):
        self.api_key = settings.DEEPSEEK_API_KEY
        self.api_base = settings.DEEPSEEK_API_BASE
        self.model = settings.DEEPSEEK_MODEL
        self.temperature = settings.DEEPSEEK_TEMPERATURE
        self.max_tokens = settings.DEEPSEEK_MAX_TOKENS
        self.rate_limiter = RateLimiter(
            max_requests=settings.DEEPSEEK_RATE_LIMIT,
            per_minutes=1
        )
        self.client = httpx.AsyncClient(timeout=60.0)
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception_type((httpx.RequestError, httpx.TimeoutException))
    )
    async def complete(
        self,
        prompt: str,
        system_prompt: Optional[str] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
        response_format: Optional[str] = "json"
    ) -> Dict[str, Any]:
        """
        Send a completion request to DeepSeek API
        
        Args:
            prompt: The user prompt
            system_prompt: Optional system prompt
            temperature: Override default temperature
            max_tokens: Override default max tokens
            response_format: Expected response format (json or text)
        
        Returns:
            Parsed response from the API
        """
        if not self.api_key:
            logger.error("DeepSeek API key not configured")
            raise ValueError("DEEPSEEK_API_KEY not configured")
        
        # Acquire rate limit permission
        await self.rate_limiter.acquire()
        
        # Build messages
        messages = []
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        messages.append({"role": "user", "content": prompt})
        
        # Build request
        request_data = {
            "model": self.model,
            "messages": messages,
            "temperature": temperature or self.temperature,
            "max_tokens": max_tokens or self.max_tokens,
        }
        
        # Add response format hint if JSON expected
        if response_format == "json":
            request_data["response_format"] = {"type": "json_object"}
        
        try:
            logger.info(f"Sending request to DeepSeek API (model: {self.model})")
            response = await self.client.post(
                f"{self.api_base}/chat/completions",
                headers={
                    "Authorization": f"Bearer {self.api_key}",
                    "Content-Type": "application/json"
                },
                json=request_data
            )
            response.raise_for_status()
            
            result = response.json()
            content = result['choices'][0]['message']['content']
            
            # Parse JSON if expected
            if response_format == "json":
                try:
                    return json.loads(content)
                except json.JSONDecodeError:
                    logger.warning("Failed to parse JSON response, extracting JSON from text")
                    return self._extract_json_from_text(content)
            
            return {"content": content}
            
        except httpx.HTTPStatusError as e:
            logger.error(f"DeepSeek API error: {e.response.status_code} - {e.response.text}")
            if e.response.status_code == 429:
                raise RateLimitExceeded("DeepSeek API rate limit exceeded")
            raise
        except Exception as e:
            logger.error(f"Unexpected error calling DeepSeek API: {str(e)}")
            raise
    
    def _extract_json_from_text(self, text: str) -> Dict[str, Any]:
        """Extract JSON from text that may contain other content"""
        import re
        
        # Try to find JSON in the text
        json_patterns = [
            r'\{[\s\S]*\}',  # Match outermost braces
            r'\[[\s\S]*\]',  # Match outermost brackets
        ]
        
        for pattern in json_patterns:
            matches = re.findall(pattern, text)
            for match in matches:
                try:
                    return json.loads(match)
                except json.JSONDecodeError:
                    continue
        
        # If no valid JSON found, return the text as content
        logger.warning("No valid JSON found in response")
        return {"content": text}
    
    async def generate_questionnaire(
        self,
        position_type: str,
        dimensions: List[str],
        question_count: int = 20,
        industry: str = "烟草"
    ) -> Dict[str, Any]:
        """
        Generate a questionnaire using DeepSeek
        
        Args:
            position_type: Type of position
            dimensions: Evaluation dimensions
            question_count: Number of questions to generate
            industry: Industry context
        
        Returns:
            Generated questionnaire data
        """
        system_prompt = """你是一个专业的人才评估专家，精通问卷设计和人才测评。
        请根据要求生成结构化的JSON格式问卷数据。
        每个问题都要贴近实际工作场景，选项要有区分度。"""
        
        user_prompt = f"""请为{industry}行业的{position_type}岗位生成一份人才评估问卷。

要求：
1. 评估维度：{', '.join(dimensions)}
2. 题目数量：{question_count}道
3. 每道题包含4个选项，分值分别为5、3、1、0分
4. 题目要结合{industry}行业实际工作场景
5. 返回严格的JSON格式

返回格式：
{{
    "title": "问卷标题",
    "description": "问卷说明",
    "questions": [
        {{
            "id": 1,
            "title": "问题文本",
            "type": "single_choice",
            "dimension": "所属维度",
            "required": true,
            "options": [
                {{"label": "A", "text": "选项内容", "score": 5}},
                {{"label": "B", "text": "选项内容", "score": 3}},
                {{"label": "C", "text": "选项内容", "score": 1}},
                {{"label": "D", "text": "选项内容", "score": 0}}
            ]
        }}
    ],
    "evaluation_criteria": {{
        "excellent": {{"min": 80, "max": 100, "description": "优秀"}},
        "good": {{"min": 60, "max": 79, "description": "良好"}},
        "pass": {{"min": 40, "max": 59, "description": "及格"}},
        "fail": {{"min": 0, "max": 39, "description": "不及格"}}
    }},
    "dimensions_weight": {{
        "维度名称": 权重百分比
    }}
}}"""
        
        try:
            result = await self.complete(
                prompt=user_prompt,
                system_prompt=system_prompt,
                response_format="json"
            )
            
            # Validate the structure
            if "questions" not in result:
                raise ValueError("Invalid questionnaire structure: missing questions")
            
            logger.info(f"Successfully generated questionnaire with {len(result.get('questions', []))} questions")
            return result
            
        except Exception as e:
            logger.error(f"Failed to generate questionnaire: {str(e)}")
            raise
    
    async def evaluate_responses(
        self,
        questionnaire_title: str,
        position_type: str,
        dimension_scores: Dict[str, float],
        answers_summary: str
    ) -> Dict[str, Any]:
        """
        Evaluate candidate responses using DeepSeek
        
        Args:
            questionnaire_title: Title of the questionnaire
            position_type: Position being evaluated for
            dimension_scores: Scores for each dimension
            answers_summary: Summary of candidate's answers
        
        Returns:
            Evaluation report
        """
        system_prompt = """你是一个专业的人才评估专家。
        请根据候选人的测评结果，提供专业、客观的评价和建议。
        评价要具体、有建设性，避免空泛的描述。"""
        
        user_prompt = f"""请对以下候选人的{position_type}岗位测评结果进行专业评价：

问卷：{questionnaire_title}
各维度得分：
{json.dumps(dimension_scores, ensure_ascii=False, indent=2)}

候选人答题情况摘要：
{answers_summary}

请提供以下内容（JSON格式）：
{{
    "overall_evaluation": "总体评价（200-300字）",
    "strengths": ["优势1", "优势2", "优势3"],
    "weaknesses": ["待改进点1", "待改进点2"],
    "recommendations": "改进建议（150-200字）",
    "is_qualified": true/false,
    "match_score": 0-100,
    "risk_level": "low/medium/high",
    "key_insights": "关键洞察（100字）"
}}"""
        
        try:
            result = await self.complete(
                prompt=user_prompt,
                system_prompt=system_prompt,
                response_format="json"
            )
            
            logger.info("Successfully generated evaluation report")
            return result
            
        except Exception as e:
            logger.error(f"Failed to generate evaluation: {str(e)}")
            raise
    
    async def close(self):
        """Close the HTTP client"""
        await self.client.aclose()


# Global instance
llm_service = LLMService()