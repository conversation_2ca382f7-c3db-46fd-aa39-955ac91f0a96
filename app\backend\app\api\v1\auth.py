"""
Authentication routes
"""
import logging
from datetime import <PERSON><PERSON><PERSON>
from typing import Any, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Header, Request
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.ext.asyncio import AsyncSession
from jose import JWTError
from app.api import deps
from app.core import security
from app.core.config import settings
from app.core.database import get_db
from app.core.exceptions import unauthorized, bad_request
from app.crud import user as user_crud
from app.crud import user_preference as preference_crud
from app.models.user import User
from app.schemas.token import Token, RefreshTokenRequest, RefreshTokenResponse
from app.schemas.user import UserLogin, UserResponse

router = APIRouter()
logger = logging.getLogger(__name__)


def extract_bearer_token(authorization_header: Optional[str]) -> Optional[str]:
    """
    Extract Bearer token from Authorization header
    
    Args:
        authorization_header: Authorization header value
        
    Returns:
        Token string if valid Bearer token found, None otherwise
    """
    if not authorization_header:
        return None
    
    parts = authorization_header.split()
    if len(parts) != 2 or parts[0].lower() != "bearer":
        return None
    
    return parts[1]


@router.post("/login", response_model=Token)
async def login(
    db: AsyncSession = Depends(get_db),
    form_data: OAuth2PasswordRequestForm = Depends()
) -> Any:
    """
    OAuth2 compatible token login, get an access token for future requests
    """
    # Authenticate user
    user = await user_crud.authenticate(
        db, username=form_data.username, password=form_data.password
    )
    if not user:
        raise unauthorized("AUTH_LOGIN_INVALID_CREDENTIALS")
    elif not user.is_active:
        raise bad_request("AUTH_LOGIN_INACTIVE_USER")
    
    # Get user preferences (creates default if not exists)
    preferences = await preference_crud.get_or_create_for_user(db, user_id=user.id)
    
    # Prepare preferences for JWT (only essential fields)
    jwt_preferences = {
        "locale": preferences.locale,
        "timezone": preferences.timezone
    }
    
    # Create tokens with preferences
    access_token = security.create_access_token(user.id, preferences=jwt_preferences)
    refresh_token = security.create_refresh_token(user.id)
    
    return Token(
        access_token=access_token,
        refresh_token=refresh_token,
        token_type="bearer"
    )


@router.post("/login/access-token", response_model=Token)
async def login_access_token(
    user_credentials: UserLogin,
    db: AsyncSession = Depends(get_db)
) -> Any:
    """
    Alternative login endpoint with JSON body
    """
    # Authenticate user
    user = await user_crud.authenticate(
        db, username=user_credentials.username, password=user_credentials.password
    )
    if not user:
        raise unauthorized("AUTH_LOGIN_INVALID_CREDENTIALS")
    elif not user.is_active:
        raise bad_request("AUTH_LOGIN_INACTIVE_USER")
    
    # Adjust token expiration if remember_me is true
    access_token_expires = None
    if user_credentials.remember_me:
        access_token_expires = timedelta(days=7)
    
    # Get user preferences (creates default if not exists)
    preferences = await preference_crud.get_or_create_for_user(db, user_id=user.id)
    
    # Prepare preferences for JWT (only essential fields)
    jwt_preferences = {
        "locale": preferences.locale,
        "timezone": preferences.timezone
    }
    
    # Create tokens with preferences
    access_token = security.create_access_token(
        user.id, expires_delta=access_token_expires, preferences=jwt_preferences
    )
    refresh_token = security.create_refresh_token(user.id)
    
    return Token(
        access_token=access_token,
        refresh_token=refresh_token,
        token_type="bearer"
    )


@router.post("/refresh", response_model=RefreshTokenResponse)
async def refresh_token(
    request: Request,
    db: AsyncSession = Depends(get_db),
    authorization: Optional[str] = Header(None),
    token_request: Optional[RefreshTokenRequest] = None
) -> Any:
    """
    JWT Standardization (JWT-STD-2025-001): Refresh access token using refresh token
    
    Supports dual-mode operation for gradual migration:
    1. RFC 6750 compliant: Authorization header (preferred method)
    2. Legacy compatibility: Request body (deprecated, will be removed)
    
    Priority Order:
    - If Authorization header present -> use header method
    - Else if request body present -> use body method (with deprecation warning)
    - Else -> reject with appropriate error
    
    Args:
        request: FastAPI request object for logging
        db: Database session
        authorization: Optional Authorization header (Bearer token)
        token_request: Optional request body with refresh token (legacy)
    
    Returns:
        RefreshTokenResponse with method information and optional deprecation warning
        
    Raises:
        HTTPException: If no token provided or token is invalid
    """
    client_ip = getattr(request.client, 'host', 'unknown') if request.client else 'unknown'
    user_agent = request.headers.get('user-agent', 'unknown')
    
    # Extract refresh token using dual-mode approach
    refresh_token = None
    method_used = None
    deprecation_warning = None
    
    # Priority 1: Authorization header (RFC 6750 compliant)
    if authorization:
        refresh_token = extract_bearer_token(authorization)
        if refresh_token:
            method_used = "header"
            logger.info(
                f"JWT refresh via Authorization header - IP: {client_ip}, "
                f"User-Agent: {user_agent[:100]}"
            )
        else:
            # Invalid Authorization header format
            logger.warning(
                f"Invalid Authorization header format - IP: {client_ip}, "
                f"Header: {authorization[:50]}..."
            )
            raise unauthorized("AUTH_INVALID_AUTHORIZATION_HEADER")
    
    # Priority 2: Request body (legacy compatibility)
    elif token_request and token_request.refresh_token:
        refresh_token = token_request.refresh_token
        method_used = "body"
        deprecation_warning = (
            "DEPRECATED: Sending refresh tokens in request body is deprecated. "
            "Use Authorization header instead: 'Authorization: Bearer {token}'. "
            "This method will be removed in future versions."
        )
        logger.warning(
            f"JWT refresh via deprecated body method - IP: {client_ip}, "
            f"User-Agent: {user_agent[:100]} - MIGRATION NEEDED"
        )
    
    # No valid token source found
    if not refresh_token:
        logger.error(
            f"JWT refresh failed - no token provided - IP: {client_ip}, "
            f"Authorization: {bool(authorization)}, Body: {bool(token_request)}"
        )
        raise unauthorized("AUTH_REFRESH_TOKEN_MISSING")
    
    # Validate and process refresh token
    try:
        payload = security.decode_token(refresh_token)
        
        if payload.get("type") != "refresh":
            logger.warning(
                f"JWT refresh failed - invalid token type: {payload.get('type')} - "
                f"IP: {client_ip}, Method: {method_used}"
            )
            raise unauthorized("AUTH_TOKEN_INVALID_TYPE")
        
        user_id = payload.get("sub")
        if not user_id:
            logger.warning(
                f"JWT refresh failed - missing user ID in token - "
                f"IP: {client_ip}, Method: {method_used}"
            )
            raise unauthorized("AUTH_TOKEN_INVALID")
        
        logger.info(
            f"JWT refresh token validated - User: {user_id}, Method: {method_used}, "
            f"IP: {client_ip}"
        )
        
    except JWTError as e:
        logger.error(
            f"JWT refresh failed - token validation error: {str(e)} - "
            f"IP: {client_ip}, Method: {method_used}"
        )
        raise unauthorized("AUTH_REFRESH_TOKEN_INVALID")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            f"JWT refresh failed - unexpected error: {str(e)} - "
            f"IP: {client_ip}, Method: {method_used}"
        )
        raise bad_request("AUTH_REFRESH_TOKEN_ERROR")
    
    # Get and validate user
    try:
        user = await user_crud.get(db, id=int(user_id))
        if not user:
            logger.warning(
                f"JWT refresh failed - user not found: {user_id} - "
                f"IP: {client_ip}, Method: {method_used}"
            )
            raise unauthorized("AUTH_USER_NOT_FOUND")
        elif not user.is_active:
            logger.warning(
                f"JWT refresh failed - inactive user: {user_id} - "
                f"IP: {client_ip}, Method: {method_used}"
            )
            raise bad_request("AUTH_LOGIN_INACTIVE_USER")
    except ValueError:
        logger.error(
            f"JWT refresh failed - invalid user ID format: {user_id} - "
            f"IP: {client_ip}, Method: {method_used}"
        )
        raise unauthorized("AUTH_TOKEN_INVALID_USER_ID")
    
    # Get user preferences (creates default if not exists)
    preferences = await preference_crud.get_or_create_for_user(db, user_id=user.id)
    
    # Prepare preferences for JWT (only essential fields)
    jwt_preferences = {
        "locale": preferences.locale,
        "timezone": preferences.timezone
    }
    
    # Create new tokens with preferences
    access_token = security.create_access_token(user.id, preferences=jwt_preferences)
    new_refresh_token = security.create_refresh_token(user.id)
    
    logger.info(
        f"JWT refresh successful - User: {user_id}, Method: {method_used}, "
        f"IP: {client_ip}, New tokens generated"
    )
    
    return RefreshTokenResponse(
        access_token=access_token,
        refresh_token=new_refresh_token,
        token_type="bearer",
        method_used=method_used,
        deprecation_warning=deprecation_warning
    )


@router.get("/me", response_model=UserResponse)
async def get_current_user(
    current_user: User = Depends(deps.get_current_active_user)
) -> Any:
    """
    Get current user information
    """
    return current_user


@router.post("/logout")
async def logout(
    current_user: User = Depends(deps.get_current_active_user)
) -> Any:
    """
    Logout current user
    
    Note: With JWT, we can't invalidate tokens server-side.
    The client should remove the token from storage.
    For added security, implement a token blacklist with Redis.
    """
    # TODO: Implement token blacklist with Redis
    return {"success": True, "message": "SUCCESS_LOGOUT"}