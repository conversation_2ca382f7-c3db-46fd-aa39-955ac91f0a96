"""
Public questionnaire access endpoints - no authentication required for public questionnaires
"""

from typing import Any, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Request
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, func
from datetime import datetime, timezone

from app.core.database import get_db
from app.models.questionnaire import Questionnaire, QuestionnaireStatus, AccessType
from app.models.questionnaire_response import QuestionnaireResponse
from app.schemas.ai_questionnaire import PublishRequest
from app.core.security import verify_password
import hashlib

router = APIRouter()


@router.get("/questionnaires/{slug}")
async def get_public_questionnaire(
    slug: str,
    request: Request,
    db: AsyncSession = Depends(get_db)
) -> Any:
    """
    Get public questionnaire by slug (no auth required for public questionnaires)
    """
    # Query questionnaire by slug
    stmt = select(Questionnaire).where(
        and_(
            Questionnaire.slug == slug,
            Questionnaire.status == QuestionnaireStatus.PUBLISHED,
            Questionnaire.deleted_at.is_(None)
        )
    )
    result = await db.execute(stmt)
    questionnaire = result.scalar_one_or_none()
    
    if not questionnaire:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Questionnaire not found or not published"
        )
    
    # Check if questionnaire has expired
    if questionnaire.valid_until and questionnaire.valid_until < datetime.now(timezone.utc):
        raise HTTPException(
            status_code=status.HTTP_410_GONE,
            detail="This questionnaire has expired"
        )
    
    # Check submission limit
    if questionnaire.max_submissions:
        # Count current submissions
        count_stmt = select(func.count()).select_from(QuestionnaireResponse).where(
            QuestionnaireResponse.questionnaire_id == questionnaire.id
        )
        count_result = await db.execute(count_stmt)
        current_submissions = count_result.scalar()
        
        if current_submissions >= questionnaire.max_submissions:
            raise HTTPException(
                status_code=status.HTTP_410_GONE,
                detail="This questionnaire has reached its submission limit"
            )
    else:
        current_submissions = 0
    
    # For private questionnaires, return limited info
    if questionnaire.access_type == AccessType.PRIVATE:
        return {
            "id": str(questionnaire.id),
            "title": questionnaire.title,
            "access_type": questionnaire.access_type.value,
            "status": questionnaire.status.value,
            "message": "This questionnaire requires special access"
        }
    
    # Prepare response based on access type
    response_data = {
        "id": str(questionnaire.id),
        "title": questionnaire.title,
        "description": questionnaire.description,
        "access_type": questionnaire.access_type.value,
        "status": questionnaire.status.value,
        "valid_until": questionnaire.valid_until.isoformat() if questionnaire.valid_until else None,
        "max_submissions": questionnaire.max_submissions,
        "current_submissions": current_submissions,
        "estimated_time": questionnaire.estimated_time,
        "ai_generated": questionnaire.ai_generated,
        "settings": questionnaire.settings or {},
    }
    
    # Only include questions for PUBLIC access or after password verification
    if questionnaire.access_type == AccessType.PUBLIC:
        response_data["questions"] = questionnaire.questions or []
    
    return response_data


@router.post("/questionnaires/{slug}/verify-password")
async def verify_questionnaire_password(
    slug: str,
    password_data: dict,
    db: AsyncSession = Depends(get_db)
) -> Any:
    """
    Verify password for password-protected questionnaire
    """
    password = password_data.get("password", "")
    
    if not password:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Password is required"
        )
    
    # Get questionnaire
    stmt = select(Questionnaire).where(
        and_(
            Questionnaire.slug == slug,
            Questionnaire.status == QuestionnaireStatus.PUBLISHED,
            Questionnaire.access_type == AccessType.PASSWORD,
            Questionnaire.deleted_at.is_(None)
        )
    )
    result = await db.execute(stmt)
    questionnaire = result.scalar_one_or_none()
    
    if not questionnaire:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Questionnaire not found"
        )
    
    # Check if questionnaire has expired
    if questionnaire.valid_until and questionnaire.valid_until < datetime.now(timezone.utc):
        raise HTTPException(
            status_code=status.HTTP_410_GONE,
            detail="This questionnaire has expired"
        )
    
    # Verify password (assuming password is stored in settings)
    stored_password = questionnaire.settings.get("password") if questionnaire.settings else None
    
    if not stored_password:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Questionnaire password not configured"
        )
    
    # Simple password comparison (in production, use hashed passwords)
    is_valid = password == stored_password
    
    if is_valid:
        # Return questionnaire data with questions
        count_stmt = select(func.count()).select_from(QuestionnaireResponse).where(
            QuestionnaireResponse.questionnaire_id == questionnaire.id
        )
        count_result = await db.execute(count_stmt)
        current_submissions = count_result.scalar()
        
        return {
            "valid": True,
            "questionnaire": {
                "id": str(questionnaire.id),
                "title": questionnaire.title,
                "description": questionnaire.description,
                "questions": questionnaire.questions or [],
                "access_type": questionnaire.access_type.value,
                "status": questionnaire.status.value,
                "valid_until": questionnaire.valid_until.isoformat() if questionnaire.valid_until else None,
                "max_submissions": questionnaire.max_submissions,
                "current_submissions": current_submissions,
                "estimated_time": questionnaire.estimated_time,
                "ai_generated": questionnaire.ai_generated,
                "settings": questionnaire.settings or {},
            }
        }
    
    return {"valid": False}


@router.post("/questionnaire-responses/submit")
async def submit_questionnaire_response(
    submission_data: dict,
    request: Request,
    db: AsyncSession = Depends(get_db)
) -> Any:
    """
    Submit questionnaire response (public endpoint)
    """
    questionnaire_id = submission_data.get("questionnaire_id")
    session_id = submission_data.get("session_id")
    candidate_info = submission_data.get("candidate_info", {})
    responses = submission_data.get("responses", {})
    
    if not questionnaire_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Questionnaire ID is required"
        )
    
    # Get questionnaire
    stmt = select(Questionnaire).where(
        and_(
            Questionnaire.id == questionnaire_id,
            Questionnaire.status == QuestionnaireStatus.PUBLISHED,
            Questionnaire.deleted_at.is_(None)
        )
    )
    result = await db.execute(stmt)
    questionnaire = result.scalar_one_or_none()
    
    if not questionnaire:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Questionnaire not found or not published"
        )
    
    # Check if questionnaire has expired
    if questionnaire.valid_until and questionnaire.valid_until < datetime.now(timezone.utc):
        raise HTTPException(
            status_code=status.HTTP_410_GONE,
            detail="This questionnaire has expired"
        )
    
    # Check submission limit
    if questionnaire.max_submissions:
        count_stmt = select(func.count()).select_from(QuestionnaireResponse).where(
            QuestionnaireResponse.questionnaire_id == questionnaire.id
        )
        count_result = await db.execute(count_stmt)
        current_submissions = count_result.scalar()
        
        if current_submissions >= questionnaire.max_submissions:
            raise HTTPException(
                status_code=status.HTTP_410_GONE,
                detail="This questionnaire has reached its submission limit"
            )
    
    # Check for duplicate submission by email
    candidate_email = candidate_info.get("email")
    if candidate_email:
        duplicate_stmt = select(QuestionnaireResponse).where(
            and_(
                QuestionnaireResponse.questionnaire_id == questionnaire_id,
                QuestionnaireResponse.candidate_email == candidate_email
            )
        )
        duplicate_result = await db.execute(duplicate_stmt)
        duplicate = duplicate_result.scalar_one_or_none()
        
        if duplicate:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail="You have already submitted a response to this questionnaire"
            )
    
    # Create response record
    response = QuestionnaireResponse(
        questionnaire_id=questionnaire_id,
        session_id=session_id,
        candidate_name=candidate_info.get("name"),
        candidate_email=candidate_email,
        candidate_phone=candidate_info.get("phone"),
        candidate_position=candidate_info.get("position"),
        responses=responses,
        submitted_at=datetime.now(timezone.utc),
        completion_time=submission_data.get("completion_time"),
        ip_address=request.client.host if request.client else None,
        user_agent=request.headers.get("user-agent"),
    )
    
    db.add(response)
    await db.commit()
    await db.refresh(response)
    
    # Check if evaluation is available (for AI-generated questionnaires)
    evaluation_available = questionnaire.ai_generated and questionnaire.evaluation_enabled
    
    return {
        "success": True,
        "submission_id": str(response.id),
        "evaluation_available": evaluation_available,
        "message": "Response submitted successfully"
    }


@router.post("/questionnaire-responses/save-progress")
async def save_response_progress(
    progress_data: dict,
    db: AsyncSession = Depends(get_db)
) -> Any:
    """
    Save progress for later continuation
    """
    questionnaire_id = progress_data.get("questionnaire_id")
    session_id = progress_data.get("session_id")
    responses = progress_data.get("responses", {})
    current_page = progress_data.get("current_page", 0)
    
    if not questionnaire_id or not session_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Questionnaire ID and session ID are required"
        )
    
    # Check if there's an existing progress record
    stmt = select(QuestionnaireResponse).where(
        and_(
            QuestionnaireResponse.questionnaire_id == questionnaire_id,
            QuestionnaireResponse.session_id == session_id,
            QuestionnaireResponse.submitted_at.is_(None)  # Not yet submitted
        )
    )
    result = await db.execute(stmt)
    progress = result.scalar_one_or_none()
    
    if progress:
        # Update existing progress
        progress.responses = responses
        progress.current_page = current_page
        progress.last_saved_at = datetime.now(timezone.utc)
    else:
        # Create new progress record
        progress = QuestionnaireResponse(
            questionnaire_id=questionnaire_id,
            session_id=session_id,
            responses=responses,
            current_page=current_page,
            last_saved_at=datetime.now(timezone.utc),
        )
        db.add(progress)
    
    await db.commit()
    
    return {
        "success": True,
        "message": "Progress saved successfully"
    }


@router.get("/questionnaire-responses/progress/{session_id}")
async def load_response_progress(
    session_id: str,
    questionnaire_id: str,
    db: AsyncSession = Depends(get_db)
) -> Any:
    """
    Load saved progress for a session
    """
    stmt = select(QuestionnaireResponse).where(
        and_(
            QuestionnaireResponse.questionnaire_id == questionnaire_id,
            QuestionnaireResponse.session_id == session_id,
            QuestionnaireResponse.submitted_at.is_(None)  # Not yet submitted
        )
    )
    result = await db.execute(stmt)
    progress = result.scalar_one_or_none()
    
    if not progress:
        return None
    
    return {
        "questionnaire_id": str(progress.questionnaire_id),
        "session_id": progress.session_id,
        "responses": progress.responses or {},
        "current_page": progress.current_page or 0,
        "last_saved_at": progress.last_saved_at.isoformat() if progress.last_saved_at else None,
    }


@router.get("/questionnaire-responses/check-duplicate")
async def check_duplicate_submission(
    questionnaire_id: str,
    email: str,
    db: AsyncSession = Depends(get_db)
) -> Any:
    """
    Check if email has already submitted response
    """
    stmt = select(QuestionnaireResponse).where(
        and_(
            QuestionnaireResponse.questionnaire_id == questionnaire_id,
            QuestionnaireResponse.candidate_email == email,
            QuestionnaireResponse.submitted_at.is_not(None)  # Already submitted
        )
    )
    result = await db.execute(stmt)
    submission = result.scalar_one_or_none()
    
    if submission:
        return {
            "exists": True,
            "submitted_at": submission.submitted_at.isoformat() if submission.submitted_at else None
        }
    
    return {"exists": False}