"""create resume files table

Revision ID: d1a2b3c4e5f6
Revises: c45e8b9f1a2d
Create Date: 2025-08-11 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'd1a2b3c4e5f6'
down_revision = 'c45e8b9f1a2d'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """创建简历文件相关表"""
    
    # 创建简历文件表
    op.create_table(
        'resume_files',
        sa.Column('id', sa.BigInteger(), nullable=False),
        sa.Column('candidate_id', sa.BigInteger(), nullable=False),
        sa.Column('file_key', sa.String(length=255), nullable=False),
        sa.Column('filename', sa.String(length=255), nullable=False),
        sa.Column('file_type', sa.String(length=50), nullable=False),
        sa.Column('file_size', sa.Integer(), nullable=False),
        sa.Column('version', sa.Integer(), nullable=False, server_default='1'),
        sa.Column('is_active', sa.Boolean(), server_default='true'),
        sa.Column('upload_method', sa.String(length=50), nullable=True),
        
        # 解析结果字段
        sa.Column('parsed_data', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('raw_text', sa.Text(), nullable=True),
        sa.Column('parsing_confidence', sa.Float(), nullable=True),
        sa.Column('parsing_status', sa.String(length=50), nullable=True),
        sa.Column('parsed_at', sa.DateTime(timezone=True), nullable=True),
        
        # 元数据字段
        sa.Column('created_by', sa.BigInteger(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
        
        # 主键和外键
        sa.PrimaryKeyConstraint('id'),
        sa.ForeignKeyConstraint(['candidate_id'], ['candidates.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['created_by'], ['users.id'], ondelete='SET NULL'),
    )
    
    # 创建索引
    op.create_index(
        'idx_resume_files_candidate_active',
        'resume_files',
        ['candidate_id', 'is_active']
    )
    
    op.create_index(
        'idx_resume_files_candidate_version',
        'resume_files',
        ['candidate_id', 'version']
    )
    
    op.create_index(
        'idx_resume_files_parsing_status',
        'resume_files',
        ['parsing_status']
    )
    
    op.create_index(
        'idx_resume_files_deleted_at',
        'resume_files',
        ['deleted_at']
    )
    
    # 添加唯一约束（候选人ID + 版本号）
    op.create_unique_constraint(
        'uq_resume_files_candidate_version',
        'resume_files',
        ['candidate_id', 'version']
    )
    
    # 创建版本日志表
    op.create_table(
        'resume_version_logs',
        sa.Column('id', sa.BigInteger(), nullable=False),
        sa.Column('resume_file_id', sa.BigInteger(), nullable=False),
        sa.Column('action', sa.String(length=50), nullable=False),
        sa.Column('details', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('performed_by', sa.BigInteger(), nullable=True),
        sa.Column('performed_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        
        # 主键和外键
        sa.PrimaryKeyConstraint('id'),
        sa.ForeignKeyConstraint(['resume_file_id'], ['resume_files.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['performed_by'], ['users.id'], ondelete='SET NULL'),
    )
    
    # 创建日志表索引
    op.create_index(
        'idx_resume_version_logs_resume_file',
        'resume_version_logs',
        ['resume_file_id']
    )
    
    op.create_index(
        'idx_resume_version_logs_action',
        'resume_version_logs',
        ['action']
    )
    
    op.create_index(
        'idx_resume_version_logs_performed_at',
        'resume_version_logs',
        ['performed_at']
    )
    
    # 创建简历解析队列表（用于异步处理）
    op.create_table(
        'resume_parse_queue',
        sa.Column('id', sa.BigInteger(), nullable=False),
        sa.Column('resume_file_id', sa.BigInteger(), nullable=False),
        sa.Column('priority', sa.Integer(), server_default='5'),
        sa.Column('status', sa.String(length=50), server_default='pending'),
        sa.Column('attempts', sa.Integer(), server_default='0'),
        sa.Column('last_error', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('started_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('completed_at', sa.DateTime(timezone=True), nullable=True),
        
        # 主键和外键
        sa.PrimaryKeyConstraint('id'),
        sa.ForeignKeyConstraint(['resume_file_id'], ['resume_files.id'], ondelete='CASCADE'),
    )
    
    # 创建队列表索引
    op.create_index(
        'idx_resume_parse_queue_status_priority',
        'resume_parse_queue',
        ['status', 'priority']
    )
    
    op.create_index(
        'idx_resume_parse_queue_resume_file',
        'resume_parse_queue',
        ['resume_file_id']
    )


def downgrade() -> None:
    """删除简历文件相关表"""
    
    # 删除索引
    op.drop_index('idx_resume_parse_queue_resume_file', table_name='resume_parse_queue')
    op.drop_index('idx_resume_parse_queue_status_priority', table_name='resume_parse_queue')
    
    op.drop_index('idx_resume_version_logs_performed_at', table_name='resume_version_logs')
    op.drop_index('idx_resume_version_logs_action', table_name='resume_version_logs')
    op.drop_index('idx_resume_version_logs_resume_file', table_name='resume_version_logs')
    
    op.drop_index('idx_resume_files_deleted_at', table_name='resume_files')
    op.drop_index('idx_resume_files_parsing_status', table_name='resume_files')
    op.drop_index('idx_resume_files_candidate_version', table_name='resume_files')
    op.drop_index('idx_resume_files_candidate_active', table_name='resume_files')
    
    # 删除表
    op.drop_table('resume_parse_queue')
    op.drop_table('resume_version_logs')
    op.drop_table('resume_files')