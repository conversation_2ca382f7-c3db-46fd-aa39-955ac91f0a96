"""
Batch Processing API Endpoints for Sprint 4
Handles async batch operations with progress tracking

Data Flow:
- Create batch task → Store in database → Queue in Celery
- Track progress through Redis cache with regular updates
- Results stored in database and cached for quick access
"""
from typing import Any, List, Optional
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.deps import get_db, check_permission
from app.core.exceptions import not_found, bad_request, service_error
from app.core.enums import Permission
from app.core.id_types import SnowflakeID
from app.models.user import User
from app.schemas.batch import (
    BatchTaskRequest,
    BatchTaskResponse,
    TaskStatusResponse,
    BatchTaskStatus
)
from app.services.batch_service import BatchService
from app.services.cache_service import cache_service
import logging

router = APIRouter()
logger = logging.getLogger(__name__)


@router.post("/create/", response_model=BatchTaskResponse)
async def create_batch_task(
    request: BatchTaskRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.AI_MATCH)),
) -> Any:
    """
    Create async batch processing task
    
    Process Flow:
    1. Validate batch size and parameters
    2. Create task record in database
    3. Queue task in Celery for background processing
    4. Return task ID and initial status
    
    Data Sources:
    - candidate_ids: References candidates table
    - position_ids: References positions table
    - parameters: Task-specific configuration
    
    Supported Task Types:
    - "candidate_match": Match candidates to positions
    - "resume_parse": Parse multiple resume files
    - "assessment_generate": Generate assessments for candidates
    - "position_evaluation": Evaluate position requirements
    """
    # Validate batch size limits
    total_items = 0
    if request.candidate_ids:
        total_items += len(request.candidate_ids)
    if request.position_ids:
        total_items += len(request.position_ids)
    
    if total_items == 0:
        raise bad_request("BATCH_EMPTY", "At least one candidate or position ID is required")
    
    if total_items > 1000:
        raise bad_request("BATCH_TOO_LARGE", "Maximum 1000 items per batch")
    
    # Validate task type
    valid_task_types = [
        "candidate_match",
        "resume_parse", 
        "assessment_generate",
        "position_evaluation"
    ]
    
    if request.task_type not in valid_task_types:
        raise bad_request("BATCH_INVALID_TYPE", f"Task type must be one of: {valid_task_types}")
    
    # Initialize batch service
    batch_service = BatchService(db)
    
    try:
        # Create batch task
        task = await batch_service.create_batch_task(
            task_type=request.task_type,
            candidate_ids=request.candidate_ids or [],
            position_ids=request.position_ids or [],
            parameters=request.parameters,
            priority=request.priority,
            user_id=current_user.id
        )
        
        # Queue for async processing (would use Celery in production)
        await batch_service.queue_task(task.task_id)
        
        logger.info(f"Created batch task {task.task_id} for user {current_user.id}")
        
        return task
        
    except Exception as e:
        logger.error(f"Failed to create batch task: {str(e)}")
        raise service_error("BATCH_CREATE_FAILED", "Failed to create batch task")


@router.get("/status/{task_id}/", response_model=TaskStatusResponse)
async def get_batch_status(
    task_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.AI_MATCH)),
) -> Any:
    """
    Get batch task status with progress details
    
    Data Sources:
    1. Redis cache: Real-time progress updates from Celery workers
    2. Database: Task metadata and final results
    3. Fallback chain: Redis → Database → Not Found
    
    Response includes:
    - Current status (pending, processing, completed, failed)
    - Progress percentage (0-100)
    - Processed/total item counts
    - Error details if failed
    - Results summary if completed
    """
    # Check cache first for real-time updates
    cache_key = f"batch:status:{task_id}"
    cached_status = await cache_service.get(cache_key)
    
    if cached_status:
        # Verify user has permission to view this task
        batch_service = BatchService(db)
        if not await batch_service.user_can_access_task(task_id, current_user.id):
            raise not_found("BATCH_TASK_NOT_FOUND")
        
        logger.debug(f"Returning cached status for task {task_id}")
        return TaskStatusResponse(**cached_status)
    
    # Fallback to database
    batch_service = BatchService(db)
    task_status = await batch_service.get_task_status(task_id, current_user.id)
    
    if not task_status:
        raise not_found("BATCH_TASK_NOT_FOUND")
    
    return task_status


@router.get("/tasks/", response_model=List[TaskStatusResponse])
async def list_user_batch_tasks(
    skip: int = 0,
    limit: int = 20,
    status: Optional[BatchTaskStatus] = None,
    task_type: Optional[str] = None,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.AI_MATCH)),
) -> Any:
    """
    List user's batch tasks with filtering
    
    Data Source: batch_tasks table filtered by user_id
    
    Filters:
    - status: Filter by task status
    - task_type: Filter by task type
    - Pagination: skip/limit for large result sets
    """
    batch_service = BatchService(db)
    
    tasks = await batch_service.list_user_tasks(
        user_id=current_user.id,
        skip=skip,
        limit=limit,
        status=status,
        task_type=task_type
    )
    
    return tasks


@router.post("/cancel/{task_id}/", response_model=TaskStatusResponse)
async def cancel_batch_task(
    task_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.AI_MATCH)),
) -> Any:
    """
    Cancel running batch task
    
    Process:
    1. Verify user owns the task
    2. Check if task can be cancelled (status = pending or processing)
    3. Send cancellation signal to Celery worker
    4. Update task status to cancelled
    """
    batch_service = BatchService(db)
    
    # Get current task status
    task = await batch_service.get_task_status(task_id, current_user.id)
    
    if not task:
        raise not_found("BATCH_TASK_NOT_FOUND")
    
    if task.status not in [BatchTaskStatus.PENDING, BatchTaskStatus.PROCESSING]:
        raise bad_request("BATCH_TASK_NOT_CANCELLABLE", f"Cannot cancel task with status: {task.status}")
    
    # Cancel the task
    cancelled_task = await batch_service.cancel_task(task_id)
    
    logger.info(f"Cancelled batch task {task_id} for user {current_user.id}")
    
    return cancelled_task


@router.get("/results/{task_id}/", response_model=dict)
async def get_batch_results(
    task_id: str,
    format: str = "json",  # json, csv, excel
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.AI_MATCH)),
) -> Any:
    """
    Get detailed batch task results
    
    Data Sources:
    - batch_tasks.result_summary: Aggregated results
    - Related tables: Detailed data based on task type
    
    Format Options:
    - json: Structured data for API consumption
    - csv: Download link for CSV export
    - excel: Download link for Excel export
    """
    batch_service = BatchService(db)
    
    # Verify task exists and user has access
    task = await batch_service.get_task_status(task_id, current_user.id)
    
    if not task:
        raise not_found("BATCH_TASK_NOT_FOUND")
    
    if task.status != BatchTaskStatus.COMPLETED:
        raise bad_request("BATCH_TASK_NOT_COMPLETED", "Task must be completed to retrieve results")
    
    # Get detailed results
    results = await batch_service.get_task_results(task_id, format)
    
    return results


@router.delete("/cleanup/", response_model=dict)
async def cleanup_old_tasks(
    days_old: int = 30,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.AI_MATCH)),
) -> Any:
    """
    Cleanup old completed/failed batch tasks
    
    Process:
    1. Find tasks older than specified days
    2. Archive or delete based on task importance
    3. Clean up associated cache entries
    4. Return cleanup summary
    
    Only removes user's own tasks unless admin permission
    """
    if days_old < 7:
        raise bad_request("BATCH_CLEANUP_TOO_RECENT", "Cannot cleanup tasks less than 7 days old")
    
    batch_service = BatchService(db)
    
    # Check if user is admin (can cleanup all tasks)
    is_admin = current_user.is_superuser
    
    cleanup_result = await batch_service.cleanup_old_tasks(
        days_old=days_old,
        user_id=None if is_admin else current_user.id
    )
    
    logger.info(f"Cleaned up {cleanup_result['deleted_count']} old batch tasks")
    
    return cleanup_result


@router.get("/stats/", response_model=dict)
async def get_batch_statistics(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.AI_MATCH)),
) -> Any:
    """
    Get batch processing statistics for user
    
    Aggregates:
    - Total tasks by status
    - Average processing time by task type
    - Success/failure rates
    - Recent activity summary
    """
    batch_service = BatchService(db)
    
    stats = await batch_service.get_user_batch_statistics(current_user.id)
    
    return stats