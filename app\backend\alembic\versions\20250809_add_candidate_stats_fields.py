"""Add candidate statistics fields and indexes

Revision ID: add_candidate_stats_20250809
Revises: b2689fa01daa
Create Date: 2025-08-09 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'add_candidate_stats_20250809'
down_revision = 'b2689fa01daa'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Add statistics fields and indexes to candidates table"""
    
    # Add new fields to candidates table for enhanced statistics
    # Note: 'source' column already exists, skip it
    op.add_column('candidates', sa.Column('source_channel', sa.String(100), nullable=True))
    op.add_column('candidates', sa.Column('expected_salary_min', sa.Integer(), nullable=True))
    op.add_column('candidates', sa.Column('expected_salary_max', sa.Integer(), nullable=True))
    op.add_column('candidates', sa.Column('last_active_at', sa.DateTime(timezone=True), nullable=True))
    op.add_column('candidates', sa.Column('interview_count', sa.Integer(), default=0, nullable=False, server_default='0'))
    op.add_column('candidates', sa.Column('match_score_avg', sa.Float(), nullable=True))
    op.add_column('candidates', sa.Column('education_level', sa.String(50), nullable=True))
    
    # Create performance indexes for statistics queries
    op.create_index('idx_candidate_status_active', 'candidates', ['status', 'last_active_at'])
    # Note: Skip idx_candidate_source as 'source' column already exists
    op.create_index('idx_candidate_education_level', 'candidates', ['education_level'])
    op.create_index('idx_candidate_experience_years', 'candidates', ['years_of_experience'])
    op.create_index('idx_candidate_salary_range', 'candidates', ['expected_salary_min', 'expected_salary_max'])
    # Use created_at directly instead of DATE() function to avoid IMMUTABLE issue
    op.create_index('idx_candidate_created_at', 'candidates', ['created_at'])
    op.create_index('idx_candidate_interview_count', 'candidates', ['interview_count'])
    
    # Create candidate statistics cache table
    op.create_table('candidate_stats_cache',
        sa.Column('id', sa.String(26), primary_key=True),
        sa.Column('stat_type', sa.String(50), nullable=False),
        sa.Column('stat_date', sa.Date(), nullable=False),
        sa.Column('stat_data', postgresql.JSONB(), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), onupdate=sa.func.now(), nullable=True),
    )
    
    # Index for cache table
    op.create_index('idx_candidate_stats_cache_type_date', 'candidate_stats_cache', ['stat_type', 'stat_date'])
    op.create_index('idx_candidate_stats_cache_created', 'candidate_stats_cache', ['created_at'])
    
    # Update existing candidates with default last_active_at to created_at
    op.execute("""
        UPDATE candidates 
        SET last_active_at = created_at 
        WHERE last_active_at IS NULL AND is_deleted = false
    """)


def downgrade() -> None:
    """Remove statistics fields and indexes"""
    
    # Drop indexes first
    op.drop_index('idx_candidate_stats_cache_created', 'candidate_stats_cache')
    op.drop_index('idx_candidate_stats_cache_type_date', 'candidate_stats_cache')
    op.drop_index('idx_candidate_interview_count', 'candidates')
    op.drop_index('idx_candidate_created_at', 'candidates')
    op.drop_index('idx_candidate_salary_range', 'candidates')
    op.drop_index('idx_candidate_experience_years', 'candidates')
    op.drop_index('idx_candidate_education_level', 'candidates')
    # Note: Skip dropping idx_candidate_source as we didn't create it
    op.drop_index('idx_candidate_status_active', 'candidates')
    
    # Drop cache table
    op.drop_table('candidate_stats_cache')
    
    # Remove columns from candidates table
    op.drop_column('candidates', 'education_level')
    op.drop_column('candidates', 'match_score_avg')
    op.drop_column('candidates', 'interview_count')
    op.drop_column('candidates', 'last_active_at')
    op.drop_column('candidates', 'expected_salary_max')
    op.drop_column('candidates', 'expected_salary_min')
    op.drop_column('candidates', 'source_channel')
    # Note: Skip dropping 'source' as we didn't add it