"""Add questionnaire_response table

Revision ID: add_qr_table_001
Revises: add_ai_fields_001
Create Date: 2025-01-21 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'add_qr_table_001'
down_revision = 'add_ai_fields_001'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Check if table exists, if yes, drop it first
    op.execute("""
        DROP TABLE IF EXISTS questionnaire_responses CASCADE;
    """)
    
    # Create questionnaire_responses table
    op.execute("""
        CREATE TABLE questionnaire_responses (
            id VARCHAR PRIMARY KEY,
            questionnaire_id BIGINT REFERENCES questionnaires(id),
            session_id VARCHAR,
            
            -- Candidate information
            candidate_id BIGINT REFERENCES candidates(id),
            candidate_name VARCHAR(100),
            candidate_email VARCHAR(255),
            candidate_phone VARCHAR(20),
            candidate_position VARCHAR(100),
            
            -- Response data
            responses JSON,
            response_times JSON,
            
            -- Submission metadata
            submitted_at TIMESTAMP WITH TIME ZONE,
            completion_time INTEGER,
            current_page INTEGER DEFAULT 0,
            is_complete BOOLEAN DEFAULT FALSE,
            
            -- Progress tracking
            last_saved_at TIMESTAMP WITH TIME ZONE,
            save_count INTEGER DEFAULT 0,
            
            -- Scoring and evaluation
            total_score INTEGER,
            dimension_scores JSON,
            ai_evaluation_id BIGINT REFERENCES evaluation_reports(id),
            is_qualified BOOLEAN,
            
            -- Technical metadata
            ip_address VARCHAR(45),
            user_agent TEXT,
            browser_info JSON,
            
            -- Timestamps
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
        );
    """)
    
    # Create indexes
    op.create_index('idx_questionnaire_response_session', 'questionnaire_responses', ['session_id'])
    op.create_index('idx_questionnaire_response_email', 'questionnaire_responses', ['candidate_email'])
    op.create_index('idx_questionnaire_response_questionnaire', 'questionnaire_responses', ['questionnaire_id'])
    op.create_index('idx_questionnaire_response_candidate', 'questionnaire_responses', ['candidate_id'])
    op.create_index('idx_questionnaire_response_submitted', 'questionnaire_responses', ['submitted_at'])


def downgrade() -> None:
    # Drop indexes
    op.drop_index('idx_questionnaire_response_submitted', 'questionnaire_responses')
    op.drop_index('idx_questionnaire_response_candidate', 'questionnaire_responses')
    op.drop_index('idx_questionnaire_response_questionnaire', 'questionnaire_responses')
    op.drop_index('idx_questionnaire_response_email', 'questionnaire_responses')
    op.drop_index('idx_questionnaire_response_session', 'questionnaire_responses')
    
    # Drop table
    op.drop_table('questionnaire_responses')