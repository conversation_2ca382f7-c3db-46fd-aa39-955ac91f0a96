# 招聘管理页面重新设计需求确认文档

## 原始需求
重新梳理 http://localhost:8088/recruitment 页面的需求，弱化招聘功能，强调人才DCI和JSF能力测评，将系统重新定位为人才测评平台。

## 基于代码库分析的现状

### 当前招聘管理页面 (`/app/frontend/app/(dashboard)/recruitment/page.tsx`)
- **页面标题**: "招聘管理中心"
- **页面描述**: "智能招聘全流程管理，从简历筛选到入职跟踪"
- **主要功能**:
  - 招聘统计概览（在招职位、候选人总数、平均招聘周期、Offer接受率）
  - 招聘漏斗分析（从简历筛选到入职的转化率）
  - 职位管理集成
  - 简历上传和解析
  - 快速操作：智能匹配、能力评估、简历解析

### 当前测评页面 (`/app/frontend/app/(dashboard)/assessments/page.tsx`)
- **页面标题**: "评估管理"
- **页面描述**: "智能评估候选人能力，生成DCI和JFS分数报告"
- **主要功能**:
  - DCI和JFS测评统计
  - 评估列表管理
  - 候选人对比功能
  - 详细评估报告

### 导航系统 (`/app/frontend/components/layouts/Sidebar.tsx`)
- **当前菜单结构**:
  - 招聘管理 (`/recruitment`) - 带有子菜单
  - 候选人管理 (`/candidates`)
  - 职位管理 (`/positions`)
  - 评估管理 (`/assessments`)

## 确认的重新设计需求

### 1. 页面重新定位
✅ **页面名称变更**: "招聘管理中心" → "仪表盘"
✅ **核心功能**: 数据总览（而非招聘流程管理）
✅ **URL路径**: 保持 `/recruitment` 不变（避免破坏现有路由）

### 2. 弱化招聘功能
✅ **完全移除**:
- 招聘漏斗分析标签页
- 职位管理相关功能
- 面试安排功能
- Offer接受率统计
- 平均招聘周期
- 简历上传功能
- "发布职位"按钮

✅ **保留但重新包装**:
- 智能匹配 → "技能匹配分析"
- 候选人数据统计（重新包装为人才库统计）

### 3. DCI和JSF测评功能强化
✅ **需要实现**:
- 详细的DCI/JSF测评报告页面
- 历史测评记录和趋势分析
- 测评建议和改进方案
- 测评统计数据的可视化展示

### 4. 导航菜单重构
✅ **变更**:
- "招聘管理" → "仪表盘"
- 移除招聘相关的子菜单项
- 强化"评估管理"的可见性

## 新页面设计规格

### 仪表盘页面结构
```
仪表盘 (Dashboard)
├── 页面标题: "数据仪表盘"
├── 页面描述: "人才能力评估与分析总览"
├── 核心统计卡片:
│   ├── 候选人总数
│   ├── 平均DCI分数
│   ├── 平均JFS分数
│   └── 完成评估数量
├── 主要功能区域:
│   ├── 最新评估动态
│   ├── DCI/JFS趋势图表
│   ├── 技能匹配分析快速入口
│   └── 热门技能分布
└── 快速操作:
    ├── 新建评估
    ├── 技能匹配分析
    └── 生成报告
```

### 技术实现要求

#### 数据源整合
- **现有数据**: 复用候选人、评估、统计API
- **新增需求**: DCI/JFS历史趋势数据接口
- **移除依赖**: 招聘流程相关的API调用

#### UI组件重用
- **保留**: 统计卡片组件 (`StatCard`)
- **重构**: 主要内容区域，移除招聘相关Tab
- **新增**: DCI/JFS趋势图表组件

#### 响应式设计
- **移动端优化**: 确保新的仪表盘在各种屏幕尺寸下正常显示
- **交互优化**: 适配触摸操作和键盘导航

## 质量标准验证

### 功能完整性 (85分 - 需要细化趋势分析需求)
- ✅ 明确定义了页面重新定位为数据仪表盘
- ✅ 详细列出需要移除的招聘功能
- ✅ 明确了DCI/JSF测评功能强化需求
- ⚠️ **需要细化**: 历史趋势分析的具体展示方式和数据维度

### 技术可行性 (90分)
- ✅ 基于现有代码架构，技术方案可行
- ✅ 可以复用现有组件和API
- ✅ 明确了数据源和UI重构范围

### 实施明确性 (80分 - 需要确认图表库)
- ✅ 明确了文件修改范围
- ✅ 详细的功能移除清单
- ⚠️ **需要确认**: 图表组件库选择（Chart.js、Recharts等）

### 用户价值 (95分)
- ✅ 重新定位为人才测评平台符合业务目标
- ✅ 突出核心价值：DCI和JSF能力评估
- ✅ 简化用户界面，提升用户体验

## 用户确认的技术选型

### 1. 趋势分析详细规格 ✅
**用户选择**: D. 所有以上
**实施范围**:
- 时间趋势：过去30天、90天、年度DCI/JFS分数变化
- 技能分布趋势：各技能领域的评分分布
- 对比分析：个人vs行业平均、团队对比

### 2. 图表组件库选择 ✅
**用户选择**: A. Recharts（与React生态集成更好）
**技术决策**:
- 使用 `recharts` 库进行图表渲染
- 组件化设计，易于维护和扩展
- 支持响应式设计和主题定制

### 3. 数据获取策略 ✅
**用户选择**: C. 混合方案：基础数据用现有API，复杂分析新增API
**实施策略**:
- **现有API**: 候选人列表、评估记录、基础统计
- **新增API**: 历史趋势数据、技能分布分析、对比基准数据
- **前端聚合**: 简单的统计计算和数据转换

## 最终需求质量评分: 95/100 ✅

**通过质量门控** ✅ (超过90分门槛)

**完成项**:
- ✅ 功能重新定位清晰明确
- ✅ 基于现有代码架构的可行性分析
- ✅ 详细的实施范围定义
- ✅ 趋势分析的具体实现细节已确认
- ✅ 图表组件的技术选型已确定
- ✅ 数据获取策略已明确

**技术实施就绪** - 所有关键决策已确认，可以进入实施阶段。