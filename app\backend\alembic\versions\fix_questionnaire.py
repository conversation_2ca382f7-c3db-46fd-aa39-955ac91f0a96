"""fix questionnaire schema

Revision ID: fix_questionnaire
Revises: 591aec21aa13
Create Date: 2025-08-15 08:35:00

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision: str = 'fix_questionnaire'
down_revision: Union[str, None] = '591aec21aa13'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Create enum types with IF NOT EXISTS
    op.execute("DO $$ BEGIN CREATE TYPE questionnairecategory AS ENUM ('SURVEY', 'ASSESSMENT', 'EVALUATION', 'QUIZ', 'FEEDBACK'); EXCEPTION WHEN duplicate_object THEN null; END $$")
    op.execute("DO $$ BEGIN CREATE TYPE questionnairestatus AS ENUM ('DRAFT', 'ACTIVE', 'PAUSED', 'COMPLETED', 'ARCHIVED'); EXCEPTION WHEN duplicate_object THEN null; END $$")
    op.execute("DO $$ BEGIN CREATE TYPE scoringmethod AS ENUM ('SUM', 'AVERAGE', 'WEIGHTED', 'CUSTOM'); EXCEPTION WHEN duplicate_object THEN null; END $$")
    
    # Add the most critical missing column first - category
    op.execute("""
        DO $$ 
        BEGIN 
            IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'questionnaires' AND column_name = 'category') THEN
                ALTER TABLE questionnaires ADD COLUMN category questionnairecategory DEFAULT 'SURVEY';
            END IF;
        END $$
    """)
    
    # Add other essential columns
    op.execute("""
        DO $$ 
        BEGIN 
            IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'questionnaires' AND column_name = 'version') THEN
                ALTER TABLE questionnaires ADD COLUMN version INTEGER DEFAULT 1;
            END IF;
        END $$
    """)
    
    op.execute("""
        DO $$ 
        BEGIN 
            IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'questionnaires' AND column_name = 'updated_by') THEN
                ALTER TABLE questionnaires ADD COLUMN updated_by BIGINT;
            END IF;
        END $$
    """)
    
    # Add status column
    op.execute("""
        DO $$ 
        BEGIN 
            IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'questionnaires' AND column_name = 'status') THEN
                ALTER TABLE questionnaires ADD COLUMN status questionnairestatus DEFAULT 'DRAFT';
            END IF;
        END $$
    """)
    
    # Add other missing columns
    columns_to_add = [
        ('published_at', 'TIMESTAMP WITH TIME ZONE'),
        ('archived_at', 'TIMESTAMP WITH TIME ZONE'),
        ('theme', 'VARCHAR(50) DEFAULT \'default\''),
        ('branding', 'JSON DEFAULT \'{}\''),
        ('show_progress_bar', 'BOOLEAN DEFAULT true'),
        ('one_question_per_page', 'BOOLEAN DEFAULT false'),
        ('auto_save', 'BOOLEAN DEFAULT true'),
        ('scheduled_start', 'TIMESTAMP WITH TIME ZONE'),
        ('scheduled_end', 'TIMESTAMP WITH TIME ZONE'),
        ('custom_scoring_formula', 'TEXT'),
        ('max_responses', 'INTEGER'),
        ('allow_multiple_submissions', 'BOOLEAN DEFAULT false'),
        ('require_authentication', 'BOOLEAN DEFAULT false'),
        ('collect_email', 'BOOLEAN DEFAULT true'),
        ('collect_name', 'BOOLEAN DEFAULT true'),
        ('notify_on_submission', 'BOOLEAN DEFAULT false'),
        ('notification_emails', 'JSON DEFAULT \'[]\''),
        ('respondent_notification', 'BOOLEAN DEFAULT false'),
        ('redirect_url', 'VARCHAR(500)'),
        ('total_responses', 'INTEGER DEFAULT 0'),
        ('completion_rate', 'FLOAT DEFAULT 0.0'),
        ('average_score', 'FLOAT'),
        ('average_time_minutes', 'FLOAT'),
        ('last_response_at', 'TIMESTAMP WITH TIME ZONE'),
    ]
    
    for column_name, column_def in columns_to_add:
        op.execute(f"""
            DO $$ 
            BEGIN 
                IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'questionnaires' AND column_name = '{column_name}') THEN
                    ALTER TABLE questionnaires ADD COLUMN {column_name} {column_def};
                END IF;
            END $$
        """)
    
    # Add scoring_method column with enum type
    op.execute("""
        DO $$ 
        BEGIN 
            IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'questionnaires' AND column_name = 'scoring_method') THEN
                ALTER TABLE questionnaires ADD COLUMN scoring_method scoringmethod DEFAULT 'SUM';
            END IF;
        END $$
    """)
    
    # Convert is_active to status mapping (only if status column was just added)
    op.execute("""
        UPDATE questionnaires 
        SET status = CASE 
            WHEN is_active = true THEN 'ACTIVE'::questionnairestatus
            ELSE 'DRAFT'::questionnairestatus
        END
        WHERE status IS NULL
    """)
    
    # Create indexes if they don't exist
    op.execute("CREATE INDEX IF NOT EXISTS idx_questionnaire_status ON questionnaires (status)")
    op.execute("CREATE INDEX IF NOT EXISTS idx_questionnaire_category ON questionnaires (category)")


def downgrade() -> None:
    # Drop columns and enums
    pass