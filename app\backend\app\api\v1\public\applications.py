"""
Public application submission API endpoints (no authentication required)
"""
from typing import Any, Optional
from datetime import datetime, timezone
from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Request, Header
from fastapi.responses import JSONResponse
from sqlalchemy.ext.asyncio import AsyncSession
from slowapi import Limiter
from slowapi.util import get_remote_address
from app.core.database import get_db
from app.core.exceptions import not_found, bad_request, service_error, too_many_requests
from app.core.config import settings
from app.crud import application_form as form_crud
from app.crud import submission as submission_crud
from app.crud import questionnaire as questionnaire_crud
from app.schemas.application_form import PublicApplicationFormResponse
from app.schemas.submission import (
    PublicApplicationSubmit,
    ApplicationSubmissionResponse,
    ResumeUploadResponse,
    ApplicationDraftSave,
    ApplicationDraftResponse
)
from app.schemas.questionnaire import ResponseSubmit, ResponseComplete
from app.services.file_service import file_service
from app.services.resume_parsing_service import resume_parsing_service
from app.services.captcha_service import captcha_service
import logging
import json
from pathlib import Path

logger = logging.getLogger(__name__)

# Create rate limiter
limiter = Limiter(key_func=get_remote_address)
router = APIRouter()

# Rate limit decorators
public_form_limit = f"{settings.PUBLIC_FORM_RATE_LIMIT or 10}/minute"
upload_limit = "5/minute"


@router.get("/{slug}", response_model=PublicApplicationFormResponse)
@limiter.limit(public_form_limit)
async def get_application_form(
    request: Request,
    slug: str,
    db: AsyncSession = Depends(get_db)
) -> Any:
    """
    Get public application form configuration by slug
    
    This endpoint is public and does not require authentication.
    Returns the form structure and fields for rendering.
    """
    form = await form_crud.get_by_slug(db, slug=slug, active_only=True)
    
    if not form:
        raise not_found("APPLICATION_FORM_NOT_FOUND")
    
    # Log form view
    logger.info(f"Public form viewed: {slug} from IP: {get_remote_address(request)}")
    
    return PublicApplicationFormResponse(
        id=str(form.id),
        title=form.title,
        slug=form.slug,
        description=form.description,
        fields=form.fields,
        requires_resume=form.requires_resume,
        questionnaire_id=str(form.questionnaire_id) if form.questionnaire_id else None,
        success_message=form.success_message,
        redirect_url=form.redirect_url
    )


@router.post("/{slug}/upload", response_model=ResumeUploadResponse)
@limiter.limit(upload_limit)
async def upload_resume(
    request: Request,
    slug: str,
    file: UploadFile = File(...),
    db: AsyncSession = Depends(get_db)
) -> Any:
    """
    Upload resume for auto-fill functionality
    
    Accepts PDF, DOC, DOCX files up to 10MB.
    Returns parsed data that can be used to auto-fill form fields.
    """
    # Verify form exists and is active
    form = await form_crud.get_by_slug(db, slug=slug, active_only=True)
    if not form:
        raise not_found("APPLICATION_FORM_NOT_FOUND")
    
    if not form.requires_resume:
        raise bad_request("RESUME_NOT_REQUIRED")
    
    # Validate file type
    if not file.filename:
        raise bad_request("FILE_NAME_REQUIRED")
    
    file_ext = Path(file.filename).suffix.lower()
    allowed_extensions = ['.pdf', '.doc', '.docx']
    
    if file_ext not in allowed_extensions:
        raise bad_request("INVALID_FILE_TYPE")
    
    # Validate file size (10MB limit)
    file_content = await file.read()
    file_size = len(file_content)
    max_size = settings.MAX_FILE_UPLOAD_SIZE or 10485760  # 10MB
    
    if file_size > max_size:
        raise bad_request("FILE_TOO_LARGE")
    
    # Reset file pointer
    await file.seek(0)
    
    try:
        # Upload to storage (temporary location)
        temp_folder = f"temp/resumes/{datetime.now(timezone.utc).strftime('%Y%m%d')}"
        file_url = await file_service.upload_file(
            file=file,
            folder=temp_folder,
            allowed_extensions=allowed_extensions
        )
        
        # Parse resume
        parsed_data = await resume_parsing_service.parse_resume(file_content, file_ext)
        
        # Generate auto-fill suggestions based on form fields
        auto_fill_suggestions = {}
        for field in form.fields:
            field_name = field.get("name")
            field_type = field.get("type")
            
            # Map parsed data to form fields
            if field_name == "name" or field_name == "full_name":
                auto_fill_suggestions[field_name] = parsed_data.get("name", "")
            elif field_name == "email":
                auto_fill_suggestions[field_name] = parsed_data.get("email", "")
            elif field_name == "phone" or field_name == "mobile":
                auto_fill_suggestions[field_name] = parsed_data.get("phone", "")
            elif field_name == "current_position" or field_name == "position":
                auto_fill_suggestions[field_name] = parsed_data.get("current_position", "")
            elif field_name == "current_company" or field_name == "company":
                auto_fill_suggestions[field_name] = parsed_data.get("current_company", "")
            elif field_name == "experience" or field_name == "years_of_experience":
                auto_fill_suggestions[field_name] = parsed_data.get("years_of_experience", 0)
            elif field_name == "education" or field_name == "education_level":
                auto_fill_suggestions[field_name] = parsed_data.get("education_level", "")
            elif field_name == "skills":
                auto_fill_suggestions[field_name] = parsed_data.get("skills", [])
        
        # Generate temporary file ID
        import uuid
        file_id = str(uuid.uuid4())
        
        # Store file info in cache for later use (implement with Redis)
        # For now, we'll include the URL in the response
        
        return ResumeUploadResponse(
            file_id=file_id,
            parsed_data=parsed_data,
            auto_fill_suggestions=auto_fill_suggestions
        )
        
    except Exception as e:
        logger.error(f"Resume upload error: {str(e)}")
        raise service_error("RESUME_UPLOAD_FAILED")


@router.post("/{slug}/submit", response_model=ApplicationSubmissionResponse)
@limiter.limit(public_form_limit)
async def submit_application(
    request: Request,
    slug: str,
    submission: PublicApplicationSubmit,
    db: AsyncSession = Depends(get_db),
    user_agent: Optional[str] = Header(None)
) -> Any:
    """
    Submit application form
    
    Public endpoint for candidates to submit their applications.
    Includes optional CAPTCHA verification and questionnaire responses.
    """
    # Get form
    form = await form_crud.get_by_slug(db, slug=slug, active_only=True)
    if not form:
        raise not_found("APPLICATION_FORM_NOT_FOUND")
    
    # Verify CAPTCHA if enabled
    if settings.ENABLE_CAPTCHA:
        if not submission.captcha_token:
            raise bad_request("CAPTCHA_REQUIRED")
        
        is_valid = await captcha_service.verify_token(submission.captcha_token)
        if not is_valid:
            raise bad_request("CAPTCHA_INVALID")
    
    # Validate form data against form configuration
    validation_errors = _validate_form_data(submission.form_data, form.fields)
    if validation_errors:
        raise bad_request(json.dumps({"errors": validation_errors}))
    
    # Get client IP
    client_ip = get_remote_address(request)
    
    try:
        # Create submission
        db_submission = await submission_crud.create_submission(
            db,
            form_id=form.id,
            submission_data=submission,
            ip_address=client_ip,
            user_agent=user_agent
        )
        
        # Process questionnaire if attached
        questionnaire_url = None
        if form.questionnaire_id and submission.questionnaire_responses:
            # Create questionnaire response
            questionnaire_response = await questionnaire_crud.questionnaire_response.create_response(
                db,
                questionnaire_id=form.questionnaire_id,
                candidate_id=db_submission.candidate_id,
                submission_id=db_submission.id,
                submission=ResponseSubmit(
                    answers=submission.questionnaire_responses,
                    started_at=datetime.now(timezone.utc)
                ),
                ip_address=client_ip,
                user_agent=user_agent
            )
            
            logger.info(f"Questionnaire response created: {questionnaire_response.id}")
        
        # Send notification emails if configured
        if form.notify_on_submission and form.notification_emails:
            # TODO: Implement email notification service
            pass
        
        # Log successful submission
        logger.info(
            f"Application submitted: form={slug}, "
            f"submission_id={db_submission.id}, "
            f"candidate_id={db_submission.candidate_id}, "
            f"IP={client_ip}"
        )
        
        return ApplicationSubmissionResponse(
            submission_id=str(db_submission.id),
            candidate_id=str(db_submission.candidate_id),
            status="submitted",
            message=form.success_message or "Your application has been submitted successfully.",
            redirect_url=form.redirect_url,
            questionnaire_required=bool(form.questionnaire_id and not submission.questionnaire_responses),
            questionnaire_url=questionnaire_url
        )
        
    except Exception as e:
        logger.error(f"Application submission error: {str(e)}")
        raise service_error("SUBMISSION_FAILED")


@router.post("/{slug}/save-draft", response_model=ApplicationDraftResponse)
@limiter.limit(public_form_limit)
async def save_draft(
    request: Request,
    slug: str,
    draft: ApplicationDraftSave,
    email: str,
    db: AsyncSession = Depends(get_db)
) -> Any:
    """
    Save application as draft
    
    Allows candidates to save their progress and continue later.
    Draft expires after 30 days.
    """
    # Get form
    form = await form_crud.get_by_slug(db, slug=slug, active_only=True)
    if not form:
        raise not_found("APPLICATION_FORM_NOT_FOUND")
    
    # Get client IP
    client_ip = get_remote_address(request)
    
    try:
        # Save draft
        draft_submission = await submission_crud.save_draft(
            db,
            form_id=form.id,
            draft_data=draft,
            email=email,
            ip_address=client_ip
        )
        
        # Calculate expiration (30 days)
        from datetime import timedelta
        expires_at = draft_submission.draft_saved_at + timedelta(days=30)
        
        return ApplicationDraftResponse(
            draft_id=str(draft_submission.id),
            saved_at=draft_submission.draft_saved_at,
            expires_at=expires_at,
            message="Draft saved successfully. You can continue your application within 30 days."
        )
        
    except Exception as e:
        logger.error(f"Draft save error: {str(e)}")
        raise service_error("DRAFT_SAVE_FAILED")


@router.get("/{slug}/questionnaire", response_model=dict)
@limiter.limit(public_form_limit)
async def get_questionnaire(
    request: Request,
    slug: str,
    db: AsyncSession = Depends(get_db)
) -> Any:
    """
    Get questionnaire associated with the application form
    
    Returns the questionnaire configuration for rendering.
    Similar to DWSurvey's question flow system.
    """
    # Get form
    form = await form_crud.get_by_slug(db, slug=slug, active_only=True)
    if not form:
        raise not_found("APPLICATION_FORM_NOT_FOUND")
    
    if not form.questionnaire_id:
        raise not_found("QUESTIONNAIRE_NOT_CONFIGURED")
    
    # Get questionnaire
    questionnaire = await questionnaire_crud.get_active(db, id=form.questionnaire_id)
    if not questionnaire:
        raise not_found("QUESTIONNAIRE_NOT_FOUND")
    
    # Process questions for public display (remove scoring info if not shown)
    public_questions = []
    for question in questionnaire.questions:
        q = question.copy()
        
        # Remove scoring information if not shown to users
        if not questionnaire.show_score_on_completion:
            if "scoring" in q:
                del q["scoring"]
            if "options" in q:
                for option in q.get("options", []):
                    if "score" in option:
                        del option["score"]
        
        public_questions.append(q)
    
    return {
        "id": str(questionnaire.id),
        "title": questionnaire.title,
        "description": questionnaire.description,
        "questions": public_questions,
        "settings": {
            "time_limit_minutes": questionnaire.time_limit_minutes,
            "randomize_questions": questionnaire.randomize_questions,
            "show_progress": questionnaire.show_progress,
            "allow_back_navigation": questionnaire.allow_back_navigation
        }
    }


def _validate_form_data(form_data: dict, field_configs: list) -> dict:
    """
    Validate form data against field configurations
    
    Returns dictionary of field_name -> error messages
    """
    errors = {}
    
    for field_config in field_configs:
        field_name = field_config.get("name")
        field_type = field_config.get("type")
        validation = field_config.get("validation", {})
        required = validation.get("required", False)
        
        value = form_data.get(field_name)
        
        # Check required fields
        if required and (value is None or value == ""):
            errors[field_name] = f"Field '{field_name}' is required"
            continue
        
        # Skip validation if field is optional and empty
        if not required and (value is None or value == ""):
            continue
        
        # Type-specific validation
        if field_type == "email":
            import re
            email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            if not re.match(email_pattern, str(value)):
                errors[field_name] = "Invalid email format"
        
        elif field_type == "number":
            try:
                num_value = float(value)
                if "min_value" in validation and num_value < validation["min_value"]:
                    errors[field_name] = f"Value must be at least {validation['min_value']}"
                if "max_value" in validation and num_value > validation["max_value"]:
                    errors[field_name] = f"Value must be at most {validation['max_value']}"
            except (TypeError, ValueError):
                errors[field_name] = "Must be a valid number"
        
        elif field_type in ["text", "textarea"]:
            str_value = str(value)
            if "min_length" in validation and len(str_value) < validation["min_length"]:
                errors[field_name] = f"Minimum length is {validation['min_length']}"
            if "max_length" in validation and len(str_value) > validation["max_length"]:
                errors[field_name] = f"Maximum length is {validation['max_length']}"
            if "pattern" in validation:
                import re
                if not re.match(validation["pattern"], str_value):
                    errors[field_name] = "Invalid format"
        
        elif field_type == "select":
            options = validation.get("options", [])
            if value not in options:
                errors[field_name] = f"Invalid option. Must be one of: {options}"
        
        elif field_type == "multi_select":
            options = validation.get("options", [])
            if not isinstance(value, list):
                errors[field_name] = "Must be a list of values"
            else:
                invalid_options = [v for v in value if v not in options]
                if invalid_options:
                    errors[field_name] = f"Invalid options: {invalid_options}"
    
    return errors