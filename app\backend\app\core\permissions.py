"""
Permission checking system with Redis caching
"""
from typing import List, Optional
from fastapi import Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
import json
import redis.asyncio as redis

from app.core.database import get_db
from app.core.redis import get_redis
from app.core.enums import Permission, ROLE_PERMISSIONS, UserRole
from app.models.user import User
from app.api.deps import get_current_active_user


PERMISSION_CACHE_TTL = 300  # 5 minutes


async def get_user_permissions(user: User, redis_client: Optional[redis.Redis] = None) -> List[str]:
    """
    Get user permissions with caching
    
    Args:
        user: User object
        redis_client: Optional Redis client for caching
        
    Returns:
        List of permission strings
    """
    # If no Redis client provided, return permissions directly
    if not redis_client:
        return ROLE_PERMISSIONS.get(UserRole(user.role), [])
    
    cache_key = f"user_permissions:{user.id}"
    
    try:
        # Try to get from cache
        cached = await redis_client.get(cache_key)
        if cached:
            return json.loads(cached)
    except Exception:
        # If Redis fails, continue without cache
        pass
    
    # Get permissions from configuration
    permissions = ROLE_PERMISSIONS.get(UserRole(user.role), [])
    
    try:
        # Cache for 5 minutes
        await redis_client.set(
            cache_key, 
            json.dumps(permissions), 
            ex=PERMISSION_CACHE_TTL
        )
    except Exception:
        # If caching fails, continue without error
        pass
    
    return permissions


async def clear_user_permissions_cache(user_id: int, redis_client: redis.Redis):
    """Clear cached permissions for a user"""
    cache_key = f"user_permissions:{user_id}"
    try:
        await redis_client.delete(cache_key)
    except Exception:
        # Ignore cache clear failures
        pass


def has_permission(user: User, permission: Permission) -> bool:
    """
    Check if user has a specific permission (synchronous, no cache)
    
    Args:
        user: User object
        permission: Permission to check
        
    Returns:
        True if user has permission, False otherwise
    """
    user_permissions = ROLE_PERMISSIONS.get(UserRole(user.role), [])
    return permission.value in user_permissions


def check_permission(permission: Permission):
    """
    Permission check dependency for FastAPI routes
    
    Usage:
        @router.get("/protected")
        async def protected_route(
            user: User = Depends(check_permission(Permission.USERS_READ))
        ):
            ...
    """
    async def permission_checker(
        current_user: User = Depends(get_current_active_user),
        redis_client: redis.Redis = Depends(get_redis)
    ) -> User:
        # Get permissions with caching
        user_permissions = await get_user_permissions(current_user, redis_client)
        
        if permission.value not in user_permissions:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Permission denied. Required: {permission.value}"
            )
        return current_user
    
    return permission_checker


def check_any_permission(permissions: List[Permission]):
    """
    Check if user has any of the specified permissions
    
    Usage:
        @router.delete("/resource")
        async def delete_resource(
            user: User = Depends(check_any_permission([
                Permission.CANDIDATES_DELETE,
                Permission.SYSTEM_CONFIG
            ]))
        ):
            ...
    """
    async def permission_checker(
        current_user: User = Depends(get_current_active_user),
        redis_client: redis.Redis = Depends(get_redis)
    ) -> User:
        # Get permissions with caching
        user_permissions = await get_user_permissions(current_user, redis_client)
        
        # Check if user has any of the required permissions
        for permission in permissions:
            if permission.value in user_permissions:
                return current_user
        
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=f"Permission denied. Required any of: {[p.value for p in permissions]}"
        )
    
    return permission_checker


def check_all_permissions(permissions: List[Permission]):
    """
    Check if user has all of the specified permissions
    
    Usage:
        @router.post("/critical-operation")
        async def critical_operation(
            user: User = Depends(check_all_permissions([
                Permission.USERS_WRITE,
                Permission.SYSTEM_CONFIG
            ]))
        ):
            ...
    """
    async def permission_checker(
        current_user: User = Depends(get_current_active_user),
        redis_client: redis.Redis = Depends(get_redis)
    ) -> User:
        # Get permissions with caching
        user_permissions = await get_user_permissions(current_user, redis_client)
        
        # Check if user has all required permissions
        missing_permissions = []
        for permission in permissions:
            if permission.value not in user_permissions:
                missing_permissions.append(permission.value)
        
        if missing_permissions:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Permission denied. Missing: {missing_permissions}"
            )
        
        return current_user
    
    return permission_checker


def is_admin(user: User) -> bool:
    """Check if user is an admin (SUPER_ADMIN or ADMIN role)"""
    return user.role in [UserRole.SUPER_ADMIN.value, UserRole.ADMIN.value]


def is_super_admin(user: User) -> bool:
    """Check if user is a super admin"""
    return user.role == UserRole.SUPER_ADMIN.value