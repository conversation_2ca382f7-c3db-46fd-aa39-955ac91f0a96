"""
AI Questionnaire Generation Service
"""
import logging
import json
import re
from typing import Dict, Any, List, Optional
from datetime import datetime, timezone

from app.services.llm_service import llm_service
from app.models.questionnaire import (
    Questionnaire, Question,
    QuestionType, ScoringMethod, QuestionnaireStatus, QuestionnaireCategory
)
from app.core.id_types import generate_snowflake_id

logger = logging.getLogger(__name__)


class TobaccoIndustryTemplates:
    """Pre-defined templates for tobacco industry positions"""
    
    TEMPLATES = {
        "生产技术岗": {
            "dimensions": [
                "工艺流程理解",
                "质量控制意识",
                "设备操作能力",
                "安全生产意识",
                "问题解决能力"
            ],
            "description": "评估烟草生产技术人员的专业能力和综合素质",
            "question_types": ["single_choice", "multiple_choice", "rating_scale"],
            "focus_areas": ["制丝工艺", "卷接包装", "质量检验", "设备维护", "安全规范"]
        },
        "营销管理岗": {
            "dimensions": [
                "专卖法规知识",
                "市场分析能力",
                "客户服务意识",
                "营销策略制定",
                "数据分析能力"
            ],
            "description": "评估烟草营销管理人员的市场洞察力和管理能力",
            "question_types": ["single_choice", "matrix", "ranking"],
            "focus_areas": ["市场调研", "客户管理", "渠道建设", "品牌推广", "合规经营"]
        },
        "质检岗": {
            "dimensions": [
                "检验标准掌握",
                "检测技术能力",
                "数据分析能力",
                "问题追溯能力",
                "规范执行力"
            ],
            "description": "评估质检人员的专业检测能力和质量意识",
            "question_types": ["single_choice", "multiple_choice", "text_input"],
            "focus_areas": ["感官评吸", "理化检测", "微生物检验", "数据记录", "异常处理"]
        },
        "物流配送岗": {
            "dimensions": [
                "物流规划能力",
                "库存管理能力",
                "信息系统操作",
                "应急处理能力",
                "服务意识"
            ],
            "description": "评估物流配送人员的运营能力和服务水平",
            "question_types": ["single_choice", "ranking", "rating_scale"],
            "focus_areas": ["仓储管理", "配送调度", "系统操作", "客户对接", "异常处理"]
        },
        "综合管理岗": {
            "dimensions": [
                "企业文化认同",
                "合规意识",
                "团队协作",
                "创新思维",
                "执行力"
            ],
            "description": "评估综合管理人员的管理能力和综合素质",
            "question_types": ["single_choice", "matrix", "text_input"],
            "focus_areas": ["制度建设", "流程优化", "团队管理", "项目推进", "文化建设"]
        }
    }
    
    @classmethod
    def get_template(cls, position_type: str) -> Dict[str, Any]:
        """Get template for a specific position type"""
        return cls.TEMPLATES.get(position_type, cls.TEMPLATES["综合管理岗"])


class GenerationService:
    """Service for generating AI-powered questionnaires"""
    
    def __init__(self):
        self.templates = TobaccoIndustryTemplates()
    
    async def generate_questionnaire(
        self,
        position_type: str,
        dimensions: Optional[List[str]] = None,
        question_count: int = 20,
        industry: str = "烟草",
        custom_requirements: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Generate a questionnaire using AI
        
        Args:
            position_type: Type of position
            dimensions: Evaluation dimensions (optional, will use template if not provided)
            question_count: Number of questions to generate
            industry: Industry context
            custom_requirements: Additional custom requirements
        
        Returns:
            Generated questionnaire data
        """
        try:
            # Get template if dimensions not provided
            if not dimensions:
                template = self.templates.get_template(position_type)
                dimensions = template["dimensions"]
            
            # Generate using LLM service
            logger.info(f"Generating questionnaire for {position_type} with {question_count} questions")
            
            questionnaire_data = await llm_service.generate_questionnaire(
                position_type=position_type,
                dimensions=dimensions,
                question_count=question_count,
                industry=industry
            )
            
            # Add custom requirements if provided
            if custom_requirements:
                questionnaire_data["custom_requirements"] = custom_requirements
            
            # Validate and enhance the generated data
            questionnaire_data = self._validate_and_enhance(questionnaire_data, position_type)
            
            return questionnaire_data
            
        except Exception as e:
            logger.error(f"Failed to generate questionnaire: {str(e)}")
            # Fallback to template-based generation
            return self._generate_from_template(position_type, dimensions, question_count)
    
    def _validate_and_enhance(self, data: Dict[str, Any], position_type: str) -> Dict[str, Any]:
        """Validate and enhance generated questionnaire data"""
        
        # Ensure required fields
        if "title" not in data:
            data["title"] = f"{position_type}能力评估问卷"
        
        if "description" not in data:
            template = self.templates.get_template(position_type)
            data["description"] = template.get("description", "")
        
        # Validate questions
        if "questions" in data:
            for i, question in enumerate(data["questions"]):
                # Ensure question has required fields
                if "id" not in question:
                    question["id"] = i + 1
                
                if "type" not in question:
                    question["type"] = "single_choice"
                
                if "required" not in question:
                    question["required"] = True
                
                # Validate options for choice questions
                if question["type"] in ["single_choice", "multiple_choice"]:
                    if "options" not in question or not question["options"]:
                        question["options"] = self._generate_default_options()
                    
                    # Ensure options have scores
                    for j, option in enumerate(question["options"]):
                        if "score" not in option:
                            # Default scoring: 5, 3, 1, 0
                            scores = [5, 3, 1, 0]
                            option["score"] = scores[j] if j < len(scores) else 0
        
        # Add evaluation criteria if missing
        if "evaluation_criteria" not in data:
            data["evaluation_criteria"] = {
                "excellent": {"min": 80, "max": 100, "description": "优秀"},
                "good": {"min": 60, "max": 79, "description": "良好"},
                "pass": {"min": 40, "max": 59, "description": "及格"},
                "fail": {"min": 0, "max": 39, "description": "不及格"}
            }
        
        # Add dimension weights if missing
        if "dimensions_weight" not in data and "dimensions" in data:
            # Equal weight for all dimensions
            weight = 100 / len(data["dimensions"])
            data["dimensions_weight"] = {dim: weight for dim in data["dimensions"]}
        
        return data
    
    def _generate_default_options(self) -> List[Dict[str, Any]]:
        """Generate default options for a question"""
        return [
            {"label": "A", "text": "非常符合", "score": 5},
            {"label": "B", "text": "比较符合", "score": 3},
            {"label": "C", "text": "不太符合", "score": 1},
            {"label": "D", "text": "完全不符合", "score": 0}
        ]
    
    def _generate_from_template(
        self,
        position_type: str,
        dimensions: List[str],
        question_count: int
    ) -> Dict[str, Any]:
        """Generate questionnaire from template (fallback method)"""
        
        template = self.templates.get_template(position_type)
        
        # Generate questions based on template
        questions = []
        questions_per_dimension = max(1, question_count // len(dimensions))
        
        question_id = 1
        for dimension in dimensions:
            for i in range(questions_per_dimension):
                question = {
                    "id": question_id,
                    "title": f"针对{dimension}的评估题目{i+1}",
                    "type": "single_choice",
                    "dimension": dimension,
                    "required": True,
                    "options": [
                        {"label": "A", "text": "完全具备此能力", "score": 5},
                        {"label": "B", "text": "基本具备此能力", "score": 3},
                        {"label": "C", "text": "部分具备此能力", "score": 1},
                        {"label": "D", "text": "不具备此能力", "score": 0}
                    ]
                }
                questions.append(question)
                question_id += 1
                
                if len(questions) >= question_count:
                    break
            
            if len(questions) >= question_count:
                break
        
        return {
            "title": f"{position_type}能力评估问卷",
            "description": template.get("description", ""),
            "questions": questions,
            "evaluation_criteria": {
                "excellent": {"min": 80, "max": 100, "description": "优秀"},
                "good": {"min": 60, "max": 79, "description": "良好"},
                "pass": {"min": 40, "max": 59, "description": "及格"},
                "fail": {"min": 0, "max": 39, "description": "不及格"}
            },
            "dimensions_weight": {dim: 100/len(dimensions) for dim in dimensions}
        }
    
    async def convert_to_questionnaire_model(
        self,
        generated_data: Dict[str, Any],
        created_by: int,
        position_type: str,
        industry: str = "烟草"
    ) -> Questionnaire:
        """
        Convert generated data to Questionnaire model
        
        Args:
            generated_data: Generated questionnaire data
            created_by: User ID who created the questionnaire
            position_type: Position type
            industry: Industry context
        
        Returns:
            Questionnaire model instance
        """
        # Create questionnaire instance
        questionnaire = Questionnaire(
            id=generate_snowflake_id(),
            title=generated_data.get("title", f"{position_type}能力评估"),
            description=generated_data.get("description", ""),
            category=QuestionnaireCategory.ASSESSMENT,
            status=QuestionnaireStatus.DRAFT,
            created_by=created_by,
            
            # AI-specific fields
            ai_generated=True,
            generation_prompt=f"Generate questionnaire for {position_type} in {industry} industry",
            position_type=position_type,
            industry=industry,
            dimensions=generated_data.get("dimensions", []),
            evaluation_criteria=generated_data.get("evaluation_criteria", {}),
            
            # Scoring settings
            scoring_enabled=True,
            scoring_method=ScoringMethod.WEIGHTED,
            passing_score=60.0,
            show_score_on_completion=True,
            
            # Display settings
            show_progress_bar=True,
            one_question_per_page=False,
            auto_save=True,
            
            # Response settings
            require_authentication=False,
            collect_email=True,
            collect_name=True
        )
        
        return questionnaire


# Global instance
generation_service = GenerationService()