"""
Candidate model for talent management
"""
import enum
from sqlalchemy import Column, String, Integer, Text, Boolean, ForeignKey, Index, Float, DateTime, BigInteger
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy import Enum as SQLEnum
from app.models.base import BaseModel
from app.core.enums import DataPermission


class CandidateStatus(str, enum.Enum):
    """候选人状态"""
    NEW = "new"                    # 新候选人
    SCREENING = "screening"        # 筛选中
    INTERVIEW = "interview"        # 面试中
    OFFER = "offer"               # 已发Offer
    HIRED = "hired"               # 已入职
    REJECTED = "rejected"         # 已拒绝
    WITHDRAWN = "withdrawn"       # 已撤回


class ApprovalStatus(str, enum.Enum):
    """候选人申请审批状态"""
    PENDING = "pending"            # 待审批
    APPROVED = "approved"          # 已批准
    REJECTED = "rejected"          # 已拒绝
    WITHDRAWN = "withdrawn"        # 已撤回


class Candidate(BaseModel):
    """候选人模型"""
    
    __tablename__ = "candidates"
    
    # 基础信息
    name = Column(String(100), nullable=False, index=True)
    email = Column(String(255), index=True)
    phone = Column(String(20), index=True)
    gender = Column(String(10))
    birth_date = Column(DateTime(timezone=True))
    
    # 职业信息
    current_position = Column(String(200))
    current_company = Column(String(200))
    years_of_experience = Column(Integer, default=0)
    current_salary = Column(Integer)  # 年薪（万）
    expected_salary = Column(Integer)  # 期望年薪（万）
    expected_salary_min = Column(Integer)  # 期望最低年薪（万）
    expected_salary_max = Column(Integer)  # 期望最高年薪（万）
    
    # 教育背景（JSON格式存储）
    education = Column(JSONB, default=list)
    # 格式: [{"school": "清华大学", "major": "计算机科学", "degree": "本科", "start_date": "2015-09", "end_date": "2019-07"}]
    education_level = Column(String(50))  # 最高学历级别：高中、专科、本科、硕士、博士等
    
    # 工作经历（JSON格式存储）
    work_experience = Column(JSONB, default=list)
    # 格式: [{"company": "阿里巴巴", "position": "高级工程师", "start_date": "2019-07", "end_date": "2023-12", "description": "..."}]
    
    # 技能标签
    skills = Column(JSONB, default=list)  # ["Python", "Java", "Docker"]
    
    # 简历信息
    resume_url = Column(String(500))  # MinIO中的URL
    resume_text = Column(Text)        # 解析后的文本
    resume_parsed_at = Column(DateTime(timezone=True))
    
    # 评估信息
    dci_score = Column(Float)         # 数字能力指数
    jfs_score = Column(Float)         # 岗位匹配分数
    assessment_data = Column(JSONB)   # 详细评估数据
    match_score_avg = Column(Float)   # 平均匹配分数
    interview_count = Column(Integer, default=0)  # 面试次数
    
    # 活跃度信息
    last_active_at = Column(DateTime(timezone=True))  # 最后活跃时间
    
    # 状态和权限
    status = Column(SQLEnum(CandidateStatus, values_callable=lambda obj: [e.value for e in obj]), default=CandidateStatus.NEW)
    data_permission = Column(SQLEnum(DataPermission), default=DataPermission.PRIVATE)
    shared_with = Column(JSONB, default=list)  # 共享用户ID列表
    
    # 来源信息
    source = Column(String(50))       # 来源渠道
    source_channel = Column(String(100))  # 详细来源渠道
    referrer_id = Column(BigInteger, ForeignKey("users.id"))  # 推荐人
    
    # 申请审批信息 (Web Application)
    approval_status = Column(SQLEnum(ApprovalStatus, values_callable=lambda obj: [e.value for e in obj]), default=ApprovalStatus.PENDING)
    # Removed application_submission_id to avoid circular dependency
    # The relationship is managed from ApplicationSubmission side (submission has candidate_id)
    reviewed_by = Column(BigInteger, ForeignKey("users.id"))
    reviewed_at = Column(DateTime(timezone=True))
    review_notes = Column(Text)
    
    # 备注
    notes = Column(Text)
    tags = Column(JSONB, default=list)  # 自定义标签
    
    # 元数据
    created_by = Column(BigInteger, ForeignKey("users.id"), nullable=False)
    is_deleted = Column(Boolean, default=False)
    deleted_at = Column(DateTime(timezone=True))
    
    # 关系
    creator = relationship("User", foreign_keys=[created_by], back_populates="created_candidates")
    referrer = relationship("User", foreign_keys=[referrer_id])
    reviewer = relationship("User", foreign_keys=[reviewed_by])
    submission = relationship("ApplicationSubmission", back_populates="candidate", uselist=False)
    resume_vector = relationship("ResumeVector", back_populates="candidate", uselist=False, cascade="all, delete-orphan")
    assessment = relationship("CandidateAssessment", back_populates="candidate", uselist=False, cascade="all, delete-orphan")
    resume_files = relationship("ResumeFile", back_populates="candidate", cascade="all, delete-orphan", order_by="desc(ResumeFile.version)")
    questionnaire_responses = relationship("QuestionnaireResponse", back_populates="candidate", foreign_keys="QuestionnaireResponse.candidate_id")
    
    # 索引
    __table_args__ = (
        Index('idx_candidate_email_phone', 'email', 'phone'),
        Index('idx_candidate_created_by', 'created_by'),
        Index('idx_candidate_status', 'status'),
        Index('idx_candidate_permission', 'data_permission'),
        Index('idx_candidate_skills', 'skills', postgresql_using='gin'),
        Index('idx_candidate_deleted', 'is_deleted'),
        Index('idx_candidate_name_search', 'name', postgresql_using='gin'),
        # Statistics indexes
        Index('idx_candidate_status_active', 'status', 'last_active_at'),
        Index('idx_candidate_source', 'source'),
        Index('idx_candidate_education_level', 'education_level'),
        Index('idx_candidate_experience_years', 'years_of_experience'),
        Index('idx_candidate_salary_range', 'expected_salary_min', 'expected_salary_max'),
        Index('idx_candidate_interview_count', 'interview_count'),
        # Approval indexes
        Index('idx_candidate_approval_status', 'approval_status'),
        Index('idx_candidate_reviewed_by', 'reviewed_by'),
    )
    
    def __repr__(self) -> str:
        """String representation"""
        return f"<Candidate(id={self.id}, name={self.name}, status={self.status})>"