"""
Database configuration and session management
"""
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.orm import declarative_base
from sqlalchemy import MetaData, event, create_engine

try:
    from app.core.config_simple import settings
except ImportError:
    from app.core.config import settings

# Naming convention for indexes, constraints, etc.
NAMING_CONVENTION = {
    "ix": "ix_%(column_0_label)s",
    "uq": "uq_%(table_name)s_%(column_0_name)s",
    "ck": "ck_%(table_name)s_%(constraint_name)s",
    "fk": "fk_%(table_name)s_%(column_0_name)s_%(referred_table_name)s",
    "pk": "pk_%(table_name)s"
}

# Create metadata with naming convention
metadata = MetaData(naming_convention=NAMING_CONVENTION)

# Create async engine
database_url = settings.async_database_url

engine = create_async_engine(
    database_url,
    echo=settings.DEBUG,  # Log SQL statements in debug mode
    future=True,
    pool_size=10,
    max_overflow=20,
    pool_pre_ping=True,  # Verify connections before using
    pool_recycle=3600,   # Recycle connections after 1 hour
)

# Create sync engine for pgvector registration
sync_engine = create_engine(
    settings.sync_database_url,
    echo=settings.DEBUG,
    pool_size=10,
    max_overflow=20,
    pool_pre_ping=True,
    pool_recycle=3600,
)

# Register pgvector extension with the connection
try:
    from pgvector.psycopg2 import register_vector
    
    @event.listens_for(sync_engine, "connect")
    def register_vector_type(dbapi_conn, connection_record):
        """Register pgvector type with each new connection"""
        register_vector(dbapi_conn)
except ImportError:
    # pgvector not installed yet, will be available after poetry install
    pass

# Create async session factory
AsyncSessionLocal = async_sessionmaker(
    engine,
    class_=AsyncSession,
    expire_on_commit=False,
    autocommit=False,
    autoflush=False,
)

# Create declarative base with our metadata
Base = declarative_base(metadata=metadata)


async def get_db() -> AsyncSession:
    """
    Dependency to get database session
    
    Usage:
        @router.get("/items")
        async def get_items(db: AsyncSession = Depends(get_db)):
            ...
    """
    async with AsyncSessionLocal() as session:
        try:
            yield session
            await session.commit()
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()


async def init_db() -> None:
    """Initialize database (create tables)"""
    async with engine.begin() as conn:
        # Import all models here to ensure they are registered
        from app.models import user  # noqa
        
        # Create all tables
        await conn.run_sync(Base.metadata.create_all)


async def close_db() -> None:
    """Close database connections"""
    await engine.dispose()