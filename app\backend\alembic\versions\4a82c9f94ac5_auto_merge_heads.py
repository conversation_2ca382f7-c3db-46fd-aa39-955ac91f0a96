"""auto_merge_heads

Revision ID: 4a82c9f94ac5
Revises: 280c47f0dc74
Create Date: 2025-08-10 17:57:53.199686

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '4a82c9f94ac5'
down_revision: Union[str, None] = '280c47f0dc74'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass