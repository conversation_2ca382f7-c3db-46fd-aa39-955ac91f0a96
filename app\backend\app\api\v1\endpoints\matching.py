"""
Matching Service API Endpoints for Sprint 4
Handles intelligent candidate-job matching with vector similarity and ML models
"""
import logging
from typing import Any, List, Dict, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from uuid import UUID
import time
import asyncio

from app.core.database import get_db
from app.core.exceptions import bad_request, service_error, not_found
from app.core.permissions import check_permission
from app.core.enums import Permission
from app.models.user import User
from app.schemas.matching import (
    MatchingRequest,
    MatchingResponse,
    BatchMatchingRequest,
    BatchMatchingResponse,
    MatchingStatistics,
    TaskStatusResponse
)
from app.services.matching_service import matching_service
from app.crud import candidate as candidate_crud
from app.crud import position as position_crud

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post("/candidate-jobs", response_model=MatchingResponse, tags=["Matching"])
async def match_candidate_to_jobs(
    *,
    request: MatchingRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.AI_MATCH)),
) -> Any:
    """
    Find best job matches for a specific candidate
    
    **Required Permission:** `ai:match`
    
    **Request Body:**
    - `candidate_id`: UUID of the candidate
    - `job_ids`: List of job UUIDs to match against (optional, matches all if empty)
    - `top_k`: Number of top matches to return (default: 10, max: 100)
    - `include_details`: Include detailed match explanations
    - `min_score`: Minimum similarity score threshold (0.0-1.0)
    
    **Returns:**
    - Ranked list of job matches
    - Similarity scores and match reasons
    - DCI score for the candidate
    - Processing time and metadata
    """
    try:
        # Handle both single candidate_id and candidate_ids format
        # Schema automatically converts candidate_id to candidate_ids list
        candidate_ids = request.candidate_ids or []
        if not candidate_ids:
            raise bad_request("MATCHING_NO_CANDIDATES_PROVIDED", "Either candidate_id or candidate_ids must be provided")
        
        # For single candidate matching, we only handle the first candidate
        primary_candidate_id = candidate_ids[0]
        
        # Validate primary candidate exists and user has access
        candidate = await candidate_crud.get(db, id=primary_candidate_id)
        if not candidate:
            raise not_found("MATCHING_CANDIDATE_NOT_FOUND", f"Candidate {primary_candidate_id} not found")
        
        # Validate job IDs if provided
        if request.job_ids:
            for job_id in request.job_ids:
                job = await position_crud.get(db, id=job_id)
                if not job:
                    raise not_found("MATCHING_JOB_NOT_FOUND", f"Job {job_id} not found")
        
        logger.info(f"User {current_user.id} matching candidate {primary_candidate_id} to {len(request.job_ids) if request.job_ids else 'all'} jobs")
        
        start_time = time.time()
        
        # Perform matching using service
        result = await matching_service.match_candidate_to_jobs(
            candidate_id=primary_candidate_id,
            job_ids=request.job_ids or None,
            top_k=request.top_k,
            min_score=request.min_score,
            include_details=request.include_details,
            db=db
        )
        
        processing_time = (time.time() - start_time) * 1000  # Convert to milliseconds
        
        logger.info(f"Candidate matching completed: {len(result['matches'])} matches found in {processing_time:.1f}ms")
        
        return MatchingResponse(
            candidate_id=primary_candidate_id,
            matches=result['matches'],
            dci_score=result.get('dci_score', 0.0),
            processing_time_ms=processing_time,
            total_jobs_evaluated=result.get('total_jobs_evaluated', 0)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Candidate matching failed: {str(e)}")
        raise service_error("MATCHING_SERVICE_ERROR", "Failed to perform candidate matching")


@router.post("/job-candidates", response_model=BatchMatchingResponse, tags=["Matching"])
async def match_job_to_candidates(
    *,
    request: BatchMatchingRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.AI_MATCH)),
) -> Any:
    """
    Find best candidate matches for a specific job
    
    **Required Permission:** `ai:match`
    
    **Request Body:**
    - `job_id`: UUID of the job position
    - `candidate_ids`: List of candidate UUIDs to match against (optional, matches all if empty)
    - `min_score`: Minimum similarity score threshold (default: 0.7)
    - `max_results`: Maximum number of matches to return (default: 100, max: 1000)
    
    **Returns:**
    - Ranked list of candidate matches
    - Match scores and explanations
    - Processing statistics
    - Optional task ID for async processing
    """
    try:
        # Validate job exists
        job = await position_crud.get(db, id=request.job_id)
        if not job:
            raise not_found("MATCHING_JOB_NOT_FOUND", f"Job {request.job_id} not found")
        
        # Validate candidate IDs if provided
        if request.candidate_ids:
            for candidate_id in request.candidate_ids:
                candidate = await candidate_crud.get(db, id=candidate_id)
                if not candidate:
                    raise not_found("MATCHING_CANDIDATE_NOT_FOUND", f"Candidate {candidate_id} not found")
        
        logger.info(f"User {current_user.id} matching job {request.job_id} to {len(request.candidate_ids) if request.candidate_ids else 'all'} candidates")
        
        start_time = time.time()
        
        # Perform matching using service
        result = await matching_service.match_job_to_candidates(
            job_id=request.job_id,
            candidate_ids=request.candidate_ids or None,
            min_score=request.min_score,
            max_results=request.max_results,
            db=db
        )
        
        processing_time = (time.time() - start_time) * 1000
        
        logger.info(f"Job matching completed: {len(result['matches'])} matches found in {processing_time:.1f}ms")
        
        return BatchMatchingResponse(
            job_id=request.job_id,
            matched_candidates=result['matches'],
            total_processed=result.get('total_processed', 0),
            total_matched=len(result['matches']),
            processing_time_ms=processing_time
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Job matching failed: {str(e)}")
        raise service_error("MATCHING_SERVICE_ERROR", "Failed to perform job matching")


@router.post("/similarity-search", tags=["Matching"])
async def vector_similarity_search(
    *,
    query_text: str,
    search_type: str = "candidates",  # candidates or jobs
    top_k: int = Query(10, ge=1, le=100),
    min_score: float = Query(0.0, ge=0.0, le=1.0),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.AI_MATCH)),
) -> Any:
    """
    Perform vector similarity search on candidates or jobs
    
    **Required Permission:** `ai:match`
    
    **Request Body:**
    - `query_text`: Text query to search for
    - `search_type`: "candidates" or "jobs"
    - `top_k`: Number of top results (1-100)
    - `min_score`: Minimum similarity threshold (0.0-1.0)
    
    **Returns:**
    - Similar candidates or jobs based on vector embeddings
    - Similarity scores
    - Processing time and metadata
    """
    try:
        if not query_text or len(query_text.strip()) == 0:
            raise bad_request("SEARCH_EMPTY_QUERY", "Query text cannot be empty")
        
        if len(query_text) > 2000:
            raise bad_request("SEARCH_QUERY_TOO_LONG", "Query text must be under 2000 characters")
        
        if search_type not in ["candidates", "jobs"]:
            raise bad_request("SEARCH_INVALID_TYPE", "Search type must be 'candidates' or 'jobs'")
        
        logger.info(f"User {current_user.id} performing vector search for {search_type}: '{query_text[:100]}...'")
        
        start_time = time.time()
        
        # Perform vector similarity search
        results = await matching_service.vector_similarity_search(
            query_text=query_text,
            search_type=search_type,
            top_k=top_k,
            min_score=min_score,
            db=db
        )
        
        processing_time = (time.time() - start_time) * 1000
        
        logger.info(f"Vector search completed: {len(results)} results found in {processing_time:.1f}ms")
        
        return {
            "query": query_text,
            "search_type": search_type,
            "results": results,
            "total_results": len(results),
            "processing_time_ms": processing_time,
            "parameters": {
                "top_k": top_k,
                "min_score": min_score
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Vector similarity search failed: {str(e)}")
        raise service_error("SEARCH_SERVICE_ERROR", "Failed to perform vector similarity search")


@router.post("/reindex", tags=["Matching"])
async def reindex_vectors(
    *,
    content_type: str = "all",  # all, candidates, jobs
    force: bool = False,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.SYSTEM_CONFIG)),
) -> Any:
    """
    Trigger re-indexing of vector embeddings
    
    **Required Permission:** `system:config`
    
    **Request Body:**
    - `content_type`: Type to reindex ("all", "candidates", "jobs")
    - `force`: Force reindex even if vectors exist
    
    **Returns:**
    - Reindexing status and progress
    - Task ID for async monitoring
    - Estimated completion time
    """
    try:
        if content_type not in ["all", "candidates", "jobs"]:
            raise bad_request("REINDEX_INVALID_TYPE", "Content type must be 'all', 'candidates', or 'jobs'")
        
        logger.info(f"User {current_user.id} triggered vector reindexing for {content_type}")
        
        # Start reindexing process
        result = await matching_service.reindex_vectors(
            content_type=content_type,
            force=force,
            db=db
        )
        
        return {
            "status": "started",
            "content_type": content_type,
            "force_reindex": force,
            "task_id": result.get("task_id"),
            "estimated_items": result.get("estimated_items", 0),
            "message": f"Vector reindexing started for {content_type}",
            "started_by": current_user.id
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Vector reindexing failed: {str(e)}")
        raise service_error("REINDEX_SERVICE_ERROR", "Failed to start vector reindexing")


@router.get("/statistics", response_model=MatchingStatistics, tags=["Matching"])
async def get_matching_statistics(
    *,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.AI_MATCH)),
) -> Any:
    """
    Get matching system statistics and performance metrics
    
    **Required Permission:** `ai:match`
    
    **Returns:**
    - Total indexed candidates and jobs
    - Average matching performance
    - Cache hit rates
    - System health metrics
    """
    try:
        logger.info(f"User {current_user.id} requested matching statistics")
        
        # Get statistics from service
        stats = await matching_service.get_statistics(db=db)
        
        return MatchingStatistics(**stats)
        
    except Exception as e:
        logger.error(f"Failed to get matching statistics: {str(e)}")
        raise service_error("STATISTICS_SERVICE_ERROR", "Failed to retrieve matching statistics")


@router.get("/performance", tags=["Matching"])
async def get_performance_metrics(
    *,
    hours: int = Query(24, ge=1, le=168),  # 1 hour to 1 week
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.MONITORING_VIEW)),
) -> Any:
    """
    Get detailed performance metrics for matching system
    
    **Required Permission:** `monitoring:view`
    
    **Query Parameters:**
    - `hours`: Time window for metrics (1-168 hours)
    
    **Returns:**
    - Response time percentiles
    - Match quality metrics
    - Error rates and types
    - Resource utilization
    """
    try:
        logger.info(f"User {current_user.id} requested performance metrics for {hours}h window")
        
        metrics = await matching_service.get_performance_metrics(
            hours=hours,
            db=db
        )
        
        return {
            "time_window_hours": hours,
            "metrics": metrics,
            "collected_at": time.time()
        }
        
    except Exception as e:
        logger.error(f"Failed to get performance metrics: {str(e)}")
        raise service_error("METRICS_SERVICE_ERROR", "Failed to retrieve performance metrics")


@router.post("/benchmark", tags=["Matching"])
async def run_matching_benchmark(
    *,
    sample_size: int = Query(100, ge=10, le=1000),
    include_vector_search: bool = True,
    include_ml_scoring: bool = True,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.SYSTEM_CONFIG)),
) -> Any:
    """
    Run performance benchmark on matching system
    
    **Required Permission:** `system:config`
    
    **Query Parameters:**
    - `sample_size`: Number of matches to benchmark (10-1000)
    - `include_vector_search`: Test vector similarity performance
    - `include_ml_scoring`: Test ML model performance
    
    **Returns:**
    - Benchmark results for each component
    - Performance recommendations
    - Bottleneck identification
    """
    try:
        logger.info(f"User {current_user.id} started matching benchmark (size: {sample_size})")
        
        start_time = time.time()
        
        # Run benchmark tests
        benchmark_results = await matching_service.run_benchmark(
            sample_size=sample_size,
            include_vector_search=include_vector_search,
            include_ml_scoring=include_ml_scoring,
            db=db
        )
        
        total_time = time.time() - start_time
        
        return {
            "benchmark_completed": True,
            "total_time_seconds": total_time,
            "sample_size": sample_size,
            "results": benchmark_results,
            "recommendations": await matching_service.generate_performance_recommendations(
                benchmark_results
            ),
            "run_by": current_user.id,
            "timestamp": time.time()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Matching benchmark failed: {str(e)}")
        raise service_error("BENCHMARK_SERVICE_ERROR", "Failed to run matching benchmark")


@router.get("/health", tags=["Matching"])
async def matching_service_health(
    current_user: User = Depends(check_permission(Permission.AI_MATCH)),
) -> Any:
    """
    Get matching service health status
    
    **Required Permission:** `ai:match`
    
    **Returns:**
    - Service health status
    - Component availability
    - Performance indicators
    - Error diagnostics
    """
    try:
        health_status = await matching_service.health_check()
        
        return {
            "status": health_status.get("status", "unknown"),
            "components": health_status.get("components", {}),
            "performance": health_status.get("performance", {}),
            "last_check": time.time()
        }
        
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e),
            "components": {
                "vector_db": "unknown",
                "ml_models": "unknown",
                "cache": "unknown"
            },
            "last_check": time.time()
        }


@router.post("/explain", tags=["Matching"])
async def explain_match(
    *,
    candidate_id: UUID,
    job_id: UUID,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.AI_MATCH)),
) -> Any:
    """
    Get detailed explanation for a specific candidate-job match
    
    **Required Permission:** `ai:match`
    
    **Request Body:**
    - `candidate_id`: UUID of the candidate
    - `job_id`: UUID of the job
    
    **Returns:**
    - Similarity breakdown by components
    - Match reasons and factors
    - Improvement suggestions
    - Visual explanation data
    """
    try:
        # Validate entities exist
        candidate = await candidate_crud.get(db, id=candidate_id)
        if not candidate:
            raise not_found("EXPLAIN_CANDIDATE_NOT_FOUND")
        
        job = await position_crud.get(db, id=job_id)
        if not job:
            raise not_found("EXPLAIN_JOB_NOT_FOUND")
        
        logger.info(f"User {current_user.id} requesting match explanation for candidate {candidate_id} and job {job_id}")
        
        # Get detailed match explanation
        explanation = await matching_service.explain_match(
            candidate_id=candidate_id,
            job_id=job_id,
            db=db
        )
        
        return explanation
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Match explanation failed: {str(e)}")
        raise service_error("EXPLAIN_SERVICE_ERROR", "Failed to generate match explanation")