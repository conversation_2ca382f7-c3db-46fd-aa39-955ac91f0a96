'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Radar<PERSON>hart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar,
  ResponsiveContainer,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  LineChart,
  Line,
} from 'recharts';
import {
  Trophy,
  Medal,
  Award,
  Users,
  TrendingUp,
  <PERSON>Up,
  <PERSON>Down,
  Minus,
  Filter,
  Download,
  Share2,
  Ch<PERSON><PERSON>Up,
  ChevronDown,
} from 'lucide-react';

interface Candidate {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  position: string;
  total_score: number;
  match_score: number;
  risk_level: 'low' | 'medium' | 'high';
  is_qualified: boolean;
  dimension_scores: Record<string, number>;
  strengths: string[];
  weaknesses: string[];
  evaluation_date: string;
}

interface CandidateComparisonDashboardProps {
  questionnaireId: string;
  candidates: Candidate[];
  onSelectCandidate?: (candidateId: string) => void;
}

const COLORS = ['#8884d8', '#82ca9d', '#ffc658', '#ff7c7c', '#8dd1e1'];

export function CandidateComparisonDashboard({
  questionnaireId,
  candidates,
  onSelectCandidate,
}: CandidateComparisonDashboardProps) {
  const [selectedDimension, setSelectedDimension] = useState<string>('all');
  const [sortBy, setSortBy] = useState<'total_score' | 'match_score' | 'name'>('total_score');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  // Sort candidates
  const sortedCandidates = [...candidates].sort((a, b) => {
    let comparison = 0;
    if (sortBy === 'name') {
      comparison = a.name.localeCompare(b.name);
    } else {
      comparison = a[sortBy] - b[sortBy];
    }
    return sortOrder === 'asc' ? comparison : -comparison;
  });

  // Get all dimensions from first candidate
  const dimensions = candidates[0]?.dimension_scores
    ? Object.keys(candidates[0].dimension_scores)
    : [];

  // Prepare radar chart data
  const radarData = dimensions.map((dimension) => {
    const dataPoint: any = { dimension: getDimensionShortName(dimension) };
    candidates.slice(0, 5).forEach((candidate, index) => {
      dataPoint[`候选人${index + 1}`] = candidate.dimension_scores[dimension];
    });
    return dataPoint;
  });

  // Prepare comparison bar chart data
  const barData = sortedCandidates.map((candidate) => ({
    name: candidate.name.split(' ')[0],
    总分: candidate.total_score,
    匹配度: candidate.match_score,
  }));

  // Prepare dimension trend data
  const trendData = dimensions.map((dimension) => ({
    dimension: getDimensionShortName(dimension),
    平均分: 
      candidates.reduce((sum, c) => sum + c.dimension_scores[dimension], 0) /
      candidates.length,
    最高分: Math.max(...candidates.map((c) => c.dimension_scores[dimension])),
    最低分: Math.min(...candidates.map((c) => c.dimension_scores[dimension])),
  }));

  function getDimensionShortName(dimension: string): string {
    const nameMap: Record<string, string> = {
      '工艺流程理解': '工艺流程',
      '质量控制意识': '质量控制',
      '设备操作能力': '设备操作',
      '安全生产意识': '安全生产',
      '问题解决能力': '问题解决',
    };
    return nameMap[dimension] || dimension;
  }

  function getRankIcon(rank: number) {
    switch (rank) {
      case 1:
        return <Trophy className="h-5 w-5 text-yellow-500" />;
      case 2:
        return <Medal className="h-5 w-5 text-gray-400" />;
      case 3:
        return <Award className="h-5 w-5 text-orange-600" />;
      default:
        return <span className="text-sm font-medium text-muted-foreground">#{rank}</span>;
    }
  }

  function getRiskBadge(risk: string) {
    const configs = {
      low: { label: '低', variant: 'success' as const },
      medium: { label: '中', variant: 'warning' as const },
      high: { label: '高', variant: 'destructive' as const },
    };
    const config = configs[risk as keyof typeof configs] || configs.medium;
    return <Badge variant={config.variant}>{config.label}风险</Badge>;
  }

  const handleSort = (column: 'total_score' | 'match_score' | 'name') => {
    if (sortBy === column) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(column);
      setSortOrder('desc');
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">候选人对比分析</h2>
          <p className="text-muted-foreground mt-1">
            共 {candidates.length} 位候选人参与评估
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm">
            <Share2 className="h-4 w-4 mr-1" />
            分享
          </Button>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-1" />
            导出
          </Button>
        </div>
      </div>

      {/* Top 3 Candidates */}
      <div className="grid grid-cols-3 gap-4">
        {sortedCandidates.slice(0, 3).map((candidate, index) => (
          <Card
            key={candidate.id}
            className={index === 0 ? 'border-yellow-500 border-2' : ''}
          >
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="flex items-center gap-3">
                  <Avatar>
                    <AvatarImage src={candidate.avatar} />
                    <AvatarFallback>{candidate.name[0]}</AvatarFallback>
                  </Avatar>
                  <div>
                    <CardTitle className="text-base">{candidate.name}</CardTitle>
                    <CardDescription className="text-xs">
                      {candidate.position}
                    </CardDescription>
                  </div>
                </div>
                {getRankIcon(index + 1)}
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">总分</span>
                  <span className="text-lg font-bold">{candidate.total_score}%</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">匹配度</span>
                  <span className="text-sm font-medium">{candidate.match_score}%</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">风险</span>
                  {getRiskBadge(candidate.risk_level)}
                </div>
                {candidate.is_qualified ? (
                  <Badge variant="success" className="w-full justify-center">
                    推荐录用
                  </Badge>
                ) : (
                  <Badge variant="secondary" className="w-full justify-center">
                    待定
                  </Badge>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Analysis Tabs */}
      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">总览对比</TabsTrigger>
          <TabsTrigger value="dimensions">维度分析</TabsTrigger>
          <TabsTrigger value="ranking">排名详情</TabsTrigger>
          <TabsTrigger value="insights">洞察分析</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          {/* Score Comparison Bar Chart */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">得分对比</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={barData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis domain={[0, 100]} />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="总分" fill="#8884d8" />
                  <Bar dataKey="匹配度" fill="#82ca9d" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Quick Stats */}
          <div className="grid grid-cols-4 gap-4">
            <Card>
              <CardHeader className="pb-3">
                <CardDescription>平均得分</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {(
                    candidates.reduce((sum, c) => sum + c.total_score, 0) /
                    candidates.length
                  ).toFixed(1)}
                  %
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-3">
                <CardDescription>合格率</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {(
                    (candidates.filter((c) => c.is_qualified).length /
                      candidates.length) *
                    100
                  ).toFixed(0)}
                  %
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-3">
                <CardDescription>最高分</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {Math.max(...candidates.map((c) => c.total_score))}%
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-3">
                <CardDescription>分数差距</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {Math.max(...candidates.map((c) => c.total_score)) -
                    Math.min(...candidates.map((c) => c.total_score))}
                  %
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="dimensions" className="space-y-4">
          {/* Dimension Filter */}
          <div className="flex items-center gap-4">
            <Label>选择维度:</Label>
            <Select value={selectedDimension} onValueChange={setSelectedDimension}>
              <SelectTrigger className="w-48">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部维度</SelectItem>
                {dimensions.map((dim) => (
                  <SelectItem key={dim} value={dim}>
                    {dim}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Radar Chart */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">能力雷达图对比</CardTitle>
              <CardDescription>显示前5名候选人</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <RadarChart data={radarData}>
                  <PolarGrid strokeDasharray="3 3" />
                  <PolarAngleAxis dataKey="dimension" />
                  <PolarRadiusAxis angle={90} domain={[0, 100]} />
                  {sortedCandidates.slice(0, 5).map((_, index) => (
                    <Radar
                      key={index}
                      name={`候选人${index + 1}`}
                      dataKey={`候选人${index + 1}`}
                      stroke={COLORS[index]}
                      fill={COLORS[index]}
                      fillOpacity={0.3}
                    />
                  ))}
                  <Legend />
                </RadarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Dimension Trend */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">维度得分趋势</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={trendData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="dimension" />
                  <YAxis domain={[0, 100]} />
                  <Tooltip />
                  <Legend />
                  <Line type="monotone" dataKey="平均分" stroke="#8884d8" />
                  <Line type="monotone" dataKey="最高分" stroke="#82ca9d" />
                  <Line type="monotone" dataKey="最低分" stroke="#ffc658" />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="ranking" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-base">完整排名</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-16">排名</TableHead>
                    <TableHead>候选人</TableHead>
                    <TableHead 
                      className="cursor-pointer"
                      onClick={() => handleSort('total_score')}
                    >
                      <div className="flex items-center gap-1">
                        总分
                        {sortBy === 'total_score' && (
                          sortOrder === 'desc' ? <ChevronDown className="h-3 w-3" /> : <ChevronUp className="h-3 w-3" />
                        )}
                      </div>
                    </TableHead>
                    <TableHead 
                      className="cursor-pointer"
                      onClick={() => handleSort('match_score')}
                    >
                      <div className="flex items-center gap-1">
                        匹配度
                        {sortBy === 'match_score' && (
                          sortOrder === 'desc' ? <ChevronDown className="h-3 w-3" /> : <ChevronUp className="h-3 w-3" />
                        )}
                      </div>
                    </TableHead>
                    <TableHead>风险等级</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>趋势</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {sortedCandidates.map((candidate, index) => {
                    const trend = index === 0 ? 0 : candidate.total_score - sortedCandidates[index - 1].total_score;
                    return (
                      <TableRow 
                        key={candidate.id}
                        className="cursor-pointer hover:bg-muted/50"
                        onClick={() => onSelectCandidate?.(candidate.id)}
                      >
                        <TableCell>
                          <div className="flex justify-center">
                            {getRankIcon(index + 1)}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-3">
                            <Avatar className="h-8 w-8">
                              <AvatarImage src={candidate.avatar} />
                              <AvatarFallback>{candidate.name[0]}</AvatarFallback>
                            </Avatar>
                            <div>
                              <p className="font-medium">{candidate.name}</p>
                              <p className="text-xs text-muted-foreground">
                                {candidate.email}
                              </p>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="font-medium">{candidate.total_score}%</div>
                        </TableCell>
                        <TableCell>{candidate.match_score}%</TableCell>
                        <TableCell>{getRiskBadge(candidate.risk_level)}</TableCell>
                        <TableCell>
                          {candidate.is_qualified ? (
                            <Badge variant="success">合格</Badge>
                          ) : (
                            <Badge variant="secondary">待定</Badge>
                          )}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-1">
                            {trend > 0 ? (
                              <ArrowUp className="h-3 w-3 text-green-600" />
                            ) : trend < 0 ? (
                              <ArrowDown className="h-3 w-3 text-red-600" />
                            ) : (
                              <Minus className="h-3 w-3 text-muted-foreground" />
                            )}
                            <span className="text-xs">
                              {trend !== 0 && Math.abs(trend).toFixed(1)}
                            </span>
                          </div>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="insights" className="space-y-4">
          {/* Common Strengths */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">共同优势</CardTitle>
              <CardDescription>多数候选人表现优秀的领域</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {dimensions
                  .filter(
                    (dim) =>
                      candidates.filter((c) => c.dimension_scores[dim] >= 70).length >=
                      candidates.length * 0.6
                  )
                  .map((dim) => (
                    <div key={dim} className="flex items-center gap-2">
                      <TrendingUp className="h-4 w-4 text-green-600" />
                      <span className="text-sm">{dim}</span>
                      <Badge variant="outline" className="ml-auto">
                        平均 {(
                          candidates.reduce((sum, c) => sum + c.dimension_scores[dim], 0) /
                          candidates.length
                        ).toFixed(1)}%
                      </Badge>
                    </div>
                  ))}
              </div>
            </CardContent>
          </Card>

          {/* Common Weaknesses */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">改进机会</CardTitle>
              <CardDescription>多数候选人需要提升的领域</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {dimensions
                  .filter(
                    (dim) =>
                      candidates.filter((c) => c.dimension_scores[dim] < 60).length >=
                      candidates.length * 0.4
                  )
                  .map((dim) => (
                    <div key={dim} className="flex items-center gap-2">
                      <TrendingDown className="h-4 w-4 text-orange-600" />
                      <span className="text-sm">{dim}</span>
                      <Badge variant="outline" className="ml-auto">
                        平均 {(
                          candidates.reduce((sum, c) => sum + c.dimension_scores[dim], 0) /
                          candidates.length
                        ).toFixed(1)}%
                      </Badge>
                    </div>
                  ))}
              </div>
            </CardContent>
          </Card>

          {/* Recommendations */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">招聘建议</CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2 text-sm">
                <li className="flex items-start gap-2">
                  <Users className="h-4 w-4 text-blue-600 mt-0.5" />
                  <span>
                    建议优先考虑前 {Math.ceil(candidates.length * 0.3)} 名候选人，
                    他们在综合能力和岗位匹配度上表现优秀
                  </span>
                </li>
                <li className="flex items-start gap-2">
                  <TrendingUp className="h-4 w-4 text-green-600 mt-0.5" />
                  <span>
                    {sortedCandidates[0].name} 在各维度表现均衡，特别适合该岗位要求
                  </span>
                </li>
                <li className="flex items-start gap-2">
                  <Award className="h-4 w-4 text-yellow-600 mt-0.5" />
                  <span>
                    可考虑对排名 4-6 位的候选人进行二次面试，深入了解其潜力
                  </span>
                </li>
              </ul>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

// Add missing Label import
import { Label } from '@/components/ui/label';