"""
Audit Log Model for Security and Compliance Tracking
"""
from datetime import datetime, timezone
from sqlalchemy import (
    Column, String, Text, BigInteger, 
    ForeignKey, JSON, TIMESTAMP, Index
)
from sqlalchemy.orm import relationship

from app.core.database import Base
from app.core.id_types import generate_snowflake_id


class AuditLog(Base):
    """Audit log for tracking all critical operations"""
    __tablename__ = "audit_logs"
    
    # Primary key
    id = Column(BigInteger, primary_key=True, default=generate_snowflake_id)
    
    # User information
    user_id = Column(BigInteger, ForeignKey("users.id", ondelete="SET NULL"))
    user_email = Column(String(255))  # Store email in case user is deleted
    user_role = Column(String(50))  # Store role at time of action
    
    # Operation details
    operation = Column(String(100), nullable=False)  # e.g., "questionnaire.create", "evaluation.generate"
    resource_type = Column(String(50))  # e.g., "questionnaire", "evaluation", "user"
    resource_id = Column(BigInteger)  # ID of the affected resource
    
    # Request details
    http_method = Column(String(10))  # GET, POST, PUT, DELETE
    endpoint = Column(String(500))  # API endpoint accessed
    ip_address = Column(String(45))  # IPv4 or IPv6 address
    user_agent = Column(Text)  # Browser/client information
    
    # Operation result
    status = Column(String(20))  # success, failure, error
    status_code = Column(BigInteger)  # HTTP status code
    error_message = Column(Text)  # Error details if failed
    
    # Additional context
    details = Column(JSON)  # Additional operation-specific details
    changes = Column(JSON)  # Before/after values for updates
    
    # Timestamp
    timestamp = Column(TIMESTAMP(timezone=True), default=lambda: datetime.now(timezone.utc), nullable=False)
    
    # Relationships
    user = relationship("User", backref="audit_logs")
    
    # Indexes for efficient querying
    __table_args__ = (
        Index("idx_audit_user_id", "user_id"),
        Index("idx_audit_timestamp", "timestamp"),
        Index("idx_audit_operation", "operation"),
        Index("idx_audit_resource", "resource_type", "resource_id"),
        Index("idx_audit_user_timestamp", "user_id", "timestamp"),  # For user activity queries
        Index("idx_audit_status", "status"),
    )