"""add positions and job vectors tables

Revision ID: c45e8b9f1a2d
Revises: b37b7a4e0c04
Create Date: 2025-01-07 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'c45e8b9f1a2d'
down_revision = 'b37b7a4e0c04'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Create pgvector extension if not exists
    op.execute("CREATE EXTENSION IF NOT EXISTS vector")
    
    # Create position status enum if not exists
    op.execute("""
        DO $$ BEGIN
            CREATE TYPE positionstatus AS ENUM ('draft', 'reviewing', 'open', 'paused', 'closed');
        EXCEPTION
            WHEN duplicate_object THEN null;
        END $$;
    """)
    
    # Create position urgency enum if not exists
    op.execute("""
        DO $$ BEGIN
            CREATE TYPE positionurgency AS ENUM ('low', 'normal', 'high', 'critical');
        EXCEPTION
            WHEN duplicate_object THEN null;
        END $$;
    """)
    
    # Create positions table
    op.create_table(
        'positions',
        sa.Column('id', sa.BigInteger(), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        
        # Basic info
        sa.Column('title', sa.String(length=200), nullable=False),
        sa.Column('department', sa.String(length=100), nullable=True),
        sa.Column('location', sa.String(length=100), nullable=True),
        sa.Column('job_level', sa.String(length=50), nullable=True),
        sa.Column('job_type', sa.String(length=50), nullable=True, server_default='full-time'),
        
        # Recruitment info
        sa.Column('headcount', sa.Integer(), nullable=True, server_default='1'),
        sa.Column('urgency', postgresql.ENUM('low', 'normal', 'high', 'critical', name='positionurgency'), nullable=True, server_default='normal'),
        sa.Column('status', postgresql.ENUM('draft', 'reviewing', 'open', 'paused', 'closed', name='positionstatus'), nullable=True, server_default='draft'),
        
        # Salary info
        sa.Column('salary_min', sa.Integer(), nullable=True),
        sa.Column('salary_max', sa.Integer(), nullable=True),
        sa.Column('salary_currency', sa.String(length=10), nullable=True, server_default='CNY'),
        
        # Rich content - JSONB fields
        sa.Column('requirements', postgresql.JSONB(astext_type=sa.Text()), nullable=True, server_default='{}'),
        sa.Column('responsibilities', postgresql.JSONB(astext_type=sa.Text()), nullable=True, server_default='[]'),
        sa.Column('qualifications', postgresql.JSONB(astext_type=sa.Text()), nullable=True, server_default='[]'),
        sa.Column('benefits', postgresql.JSONB(astext_type=sa.Text()), nullable=True, server_default='[]'),
        
        # Matching configuration
        sa.Column('capability_weights', postgresql.JSONB(astext_type=sa.Text()), nullable=True, server_default='{}'),
        sa.Column('required_skills', postgresql.JSONB(astext_type=sa.Text()), nullable=True, server_default='[]'),
        sa.Column('preferred_skills', postgresql.JSONB(astext_type=sa.Text()), nullable=True, server_default='[]'),
        
        # Permissions
        sa.Column('data_permission', postgresql.ENUM('PRIVATE', 'SHARED', 'TEAM', 'PUBLIC', name='datapermission'), nullable=True, server_default='PRIVATE'),
        sa.Column('shared_with', postgresql.JSONB(astext_type=sa.Text()), nullable=True, server_default='[]'),
        
        # Metadata
        sa.Column('created_by', sa.BigInteger(), nullable=False),
        sa.Column('deadline', sa.DateTime(timezone=True), nullable=True),
        sa.Column('is_deleted', sa.Boolean(), nullable=True, server_default='false'),
        sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
        
        sa.PrimaryKeyConstraint('id'),
        sa.ForeignKeyConstraint(['created_by'], ['users.id'], ),
    )
    
    # Create indexes for positions table
    op.create_index('idx_position_status', 'positions', ['status'], unique=False)
    op.create_index('idx_position_department', 'positions', ['department'], unique=False)
    op.create_index('idx_position_title', 'positions', ['title'], unique=False, postgresql_using='gin', postgresql_ops={'title': 'gin_trgm_ops'})
    op.create_index('idx_position_created_by', 'positions', ['created_by'], unique=False)
    op.create_index('idx_position_deleted', 'positions', ['is_deleted'], unique=False)
    op.create_index('idx_position_permission', 'positions', ['data_permission'], unique=False)
    op.create_index('idx_position_required_skills', 'positions', ['required_skills'], unique=False, postgresql_using='gin')
    
    # Create job_vectors table
    op.create_table(
        'job_vectors',
        sa.Column('id', sa.BigInteger(), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        
        # Relationship
        sa.Column('position_id', sa.BigInteger(), nullable=False),
        
        # Vector embeddings - Note: Vector type requires pgvector extension
        sa.Column('title_vector', postgresql.ARRAY(sa.Float(), dimensions=1), nullable=True),  # Will be altered to vector type
        sa.Column('requirements_vector', postgresql.ARRAY(sa.Float(), dimensions=1), nullable=True),  # Will be altered to vector type
        sa.Column('full_text_vector', postgresql.ARRAY(sa.Float(), dimensions=1), nullable=False),  # Will be altered to vector type
        
        # Cached data
        sa.Column('parsed_requirements', postgresql.JSONB(astext_type=sa.Text()), nullable=True, server_default='{}'),
        sa.Column('extracted_keywords', postgresql.JSONB(astext_type=sa.Text()), nullable=True, server_default='[]'),
        
        # Metadata
        sa.Column('embedding_model', sa.String(length=50), nullable=True, server_default='bge-m3'),
        sa.Column('embedding_version', sa.String(length=20), nullable=True, server_default='v1'),
        sa.Column('embedding_provider', sa.String(length=20), nullable=True, server_default='ollama'),
        sa.Column('embedding_dimension', sa.Integer(), nullable=True, server_default='1024'),
        sa.Column('token_count', sa.Integer(), nullable=True),
        sa.Column('generation_time_ms', sa.Float(), nullable=True),
        sa.Column('generated_at', sa.DateTime(timezone=True), nullable=True),
        
        sa.PrimaryKeyConstraint('id'),
        sa.ForeignKeyConstraint(['position_id'], ['positions.id'], ),
        sa.UniqueConstraint('position_id')
    )
    
    # Create index for position_id
    op.create_index('idx_job_vector_position', 'job_vectors', ['position_id'], unique=False)
    
    # Alter columns to use vector type (requires pgvector)
    op.execute("ALTER TABLE job_vectors ALTER COLUMN title_vector TYPE vector(1024) USING title_vector::vector(1024)")
    op.execute("ALTER TABLE job_vectors ALTER COLUMN requirements_vector TYPE vector(1024) USING requirements_vector::vector(1024)")
    op.execute("ALTER TABLE job_vectors ALTER COLUMN full_text_vector TYPE vector(1024) USING full_text_vector::vector(1024)")
    
    # Create HNSW index for similarity search
    op.execute("""
        CREATE INDEX idx_job_full_vector_hnsw 
        ON job_vectors 
        USING hnsw (full_text_vector vector_cosine_ops)
        WITH (m = 16, ef_construction = 64)
    """)


def downgrade() -> None:
    # Drop indexes
    op.drop_index('idx_job_full_vector_hnsw', 'job_vectors')
    op.drop_index('idx_job_vector_position', 'job_vectors')
    
    # Drop tables
    op.drop_table('job_vectors')
    
    # Drop position indexes
    op.drop_index('idx_position_required_skills', 'positions')
    op.drop_index('idx_position_permission', 'positions')
    op.drop_index('idx_position_deleted', 'positions')
    op.drop_index('idx_position_created_by', 'positions')
    op.drop_index('idx_position_title', 'positions')
    op.drop_index('idx_position_department', 'positions')
    op.drop_index('idx_position_status', 'positions')
    
    op.drop_table('positions')
    
    # Drop enums - Note: These might be used elsewhere, so be careful
    op.execute("DROP TYPE IF EXISTS positionstatus")
    op.execute("DROP TYPE IF EXISTS positionurgency")