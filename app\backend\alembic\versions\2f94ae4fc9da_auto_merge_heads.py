"""auto_merge_heads

Revision ID: 2f94ae4fc9da
Revises: 82469a6af64d
Create Date: 2025-08-11 03:53:19.216406

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2f94ae4fc9da'
down_revision: Union[str, None] = '82469a6af64d'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass