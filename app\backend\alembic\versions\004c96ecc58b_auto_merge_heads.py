"""auto_merge_heads

Revision ID: 004c96ecc58b
Revises: 8af548f6adf5
Create Date: 2025-08-11 03:56:24.340896

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '004c96ecc58b'
down_revision: Union[str, None] = '8af548f6adf5'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass