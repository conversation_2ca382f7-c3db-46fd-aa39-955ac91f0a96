"""
System monitoring API endpoints
"""
from typing import Any, Dict, List, Optional
from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.api import deps
from app.core.database import get_db
from app.core.exceptions import NotFoundError
from app.core.permissions import check_permission
from app.core.enums import Permission
from app.models.user import User
from app.schemas.monitoring import (
    HealthCheckResponse,
    ServiceCheckRequest,
    ServiceHealthResponse,
    ServiceListResponse,
    SystemHealthResponse,
    SystemMetricsResponse,
)
from app.services.monitoring import monitoring_service
from app.services.role import role_service

router = APIRouter()


@router.get("/health", response_model=SystemHealthResponse)
async def get_system_health(
    *,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.MONITORING_VIEW)),
    force_refresh: bool = Query(False, description="Skip cache and force fresh health check")
) -> Any:
    """
    Get comprehensive system health overview with caching support.
    
    - By default returns cached data (up to 5 minutes old)
    - Use force_refresh=true to skip cache and get real-time data
    
    Requires permission: monitoring.view
    """
    return await monitoring_service.get_system_health_overview(db, force_refresh=force_refresh)


@router.get("/services", response_model=ServiceListResponse)
async def list_services_health(
    *,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.MONITORING_VIEW)),
    refresh: bool = Query(False, description="Force refresh health checks")
) -> Any:
    """
    Get health status for all services.
    
    Requires permission: monitoring.view
    """
    return await monitoring_service.list_services_health(db, refresh=refresh)


@router.get("/services/{service_name}", response_model=ServiceHealthResponse)
async def get_service_health(
    *,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.MONITORING_VIEW)),
    service_name: str
) -> Any:
    """
    Get health status for a specific service.
    
    Requires permission: monitoring.view
    """

    try:
        return await monitoring_service.get_service_health(db, service_name)
    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )


@router.get("/services/{service_name}/history", response_model=list[ServiceHealthResponse])
async def get_service_history(
    *,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.MONITORING_VIEW)),
    service_name: str,
    hours: int = Query(24, ge=1, le=168, description="Number of hours of history")
) -> Any:
    """
    Get health check history for a service.
    
    Requires permission: monitoring.view
    """
    return await monitoring_service.get_service_history(db, service_name, hours)


@router.get("/metrics", response_model=SystemMetricsResponse)
async def get_system_metrics(
    *,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.MONITORING_VIEW)),
    force_refresh: bool = Query(False, description="Skip cache and recalculate metrics")
) -> Any:
    """
    Get current system metrics with caching support.
    
    - By default returns cached metrics (up to 5 minutes old)
    - Use force_refresh=true to skip cache and recalculate metrics
    
    Requires permission: monitoring.view
    """
    return await monitoring_service.get_system_metrics(db, force_refresh=force_refresh)


@router.post("/check", response_model=HealthCheckResponse)
async def perform_health_check(
    *,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.MONITORING_VIEW)),
    check_request: ServiceCheckRequest
) -> Any:
    """
    Perform health check for specified services.
    
    Requires permission: monitoring.view
    """

    return await monitoring_service.perform_health_check(db, check_request)


@router.post("/cleanup", response_model=dict[str, int])
async def cleanup_old_records(
    *,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(deps.get_current_active_superuser),
    days: int = Query(7, ge=1, le=90, description="Number of days to keep")
) -> Any:
    """
    Clean up old monitoring records.
    
    Requires super admin access.
    """
    return await monitoring_service.cleanup_old_records(db, days)


@router.post("/initialize")
async def initialize_monitoring(
    *,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(deps.get_current_active_superuser)
) -> Any:
    """
    Initialize monitoring system with initial health checks.
    
    Requires super admin access.
    """
    await monitoring_service.initialize_monitoring(db)
    return {"message": "Monitoring system initialized successfully"}


@router.post("/cache/refresh")
async def refresh_monitoring_cache(
    *,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.MONITORING_VIEW))
) -> Any:
    """
    Manually trigger monitoring cache refresh.
    
    This endpoint triggers the background cache refresh task immediately.
    Use this when you need fresh monitoring data without waiting for
    the scheduled 5-minute refresh cycle.
    
    Requires permission: monitoring.view
    """
    from app.tasks import refresh_monitoring_cache
    
    # Trigger the background task asynchronously
    task_result = refresh_monitoring_cache.apply_async()
    
    return {
        "message": "Monitoring cache refresh triggered successfully",
        "task_id": task_result.id,
        "status": "processing",
        "estimated_completion": "30-60 seconds",
        "timestamp": datetime.now().isoformat(),
        "note": "Cache will be updated in background. Check /cache/info for status."
    }


@router.post("/cache/clear")
async def clear_monitoring_cache(
    *,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.MONITORING_VIEW))
) -> Any:
    """
    Clear all monitoring-related cache entries.
    
    This will force all subsequent requests to perform fresh health checks
    until the cache is repopulated (either manually or by scheduled tasks).
    
    Requires permission: monitoring.view
    """
    result = await monitoring_service.clear_monitoring_cache()
    return result


@router.get("/cache/info")
async def get_cache_info(
    *,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.MONITORING_VIEW))
) -> Any:
    """
    Get information about monitoring cache status.
    
    Returns cache hit/miss information, TTL for cached entries,
    and Redis connection health.
    
    Requires permission: monitoring.view
    """
    cache_info = await monitoring_service.get_cache_info()
    return cache_info


# New debugging and historical query endpoints

@router.get("/history/snapshots")
async def get_monitoring_snapshots(
    *,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.MONITORING_VIEW)),
    hours: int = Query(24, ge=1, le=168, description="Number of hours of history"),
    limit: int = Query(100, ge=1, le=500, description="Maximum number of snapshots"),
    status_filter: Optional[str] = Query(None, description="Filter by status: healthy, degraded, unhealthy"),
    include_raw: bool = Query(False, description="Include raw API response JSON for debugging")
) -> Any:
    """
    Get historical monitoring snapshots for analysis and debugging.
    
    This endpoint provides access to complete historical monitoring data
    including system health, service states, and performance metrics over time.
    
    When include_raw=true, returns complete API response JSON for debugging.
    
    Requires permission: monitoring.view
    """
    from app.crud.monitoring import monitoring_snapshot_crud
    from app.models.monitoring import ServiceStatus
    
    # Convert status filter string to enum
    status_enum = None
    if status_filter:
        status_map = {
            "healthy": ServiceStatus.HEALTHY,
            "degraded": ServiceStatus.DEGRADED, 
            "unhealthy": ServiceStatus.UNHEALTHY,
            "unknown": ServiceStatus.UNKNOWN
        }
        status_enum = status_map.get(status_filter.lower())
        if not status_enum:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid status filter: {status_filter}. Must be one of: healthy, degraded, unhealthy, unknown"
            )
    
    try:
        # Get snapshots with optional filtering
        snapshots = await monitoring_snapshot_crud.search_by_error_conditions(
            db,
            status_filter=status_enum,
            hours=hours,
            limit=limit
        )
        
        # Format response based on include_raw flag
        result = []
        for snapshot in snapshots:
            snapshot_data = {
                "id": snapshot.id,
                "created_at": snapshot.created_at.isoformat(),
                "overall_status": snapshot.overall_status.value,
                "health_score": snapshot.health_score,
                "system_metrics": {
                    "cpu_usage": snapshot.cpu_usage,
                    "memory_usage": snapshot.memory_usage,
                    "disk_usage": snapshot.disk_usage
                },
                "services_count": {
                    "total": snapshot.services_total,
                    "healthy": snapshot.services_healthy,
                    "degraded": snapshot.services_degraded,
                    "unhealthy": snapshot.services_unhealthy
                },
                "uptime_hours": snapshot.uptime_hours,
                "services_snapshot": snapshot.services_snapshot,
                "snapshot_source": snapshot.snapshot_source,
                "snapshot_trigger": snapshot.snapshot_trigger
            }
            
            # Include raw API response JSON for debugging if requested
            if include_raw:
                snapshot_data["raw_response_json"] = snapshot.raw_response_json
                snapshot_data["metrics_snapshot"] = snapshot.metrics_snapshot
                snapshot_data["cache_info"] = snapshot.cache_info
                snapshot_data["snapshot_metadata"] = snapshot.snapshot_metadata
            
            result.append(snapshot_data)
        
        return {
            "snapshots": result,
            "total_snapshots": len(result),
            "time_period_hours": hours,
            "status_filter": status_filter,
            "include_raw_data": include_raw
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve monitoring snapshots: {str(e)}"
        )


@router.get("/history/snapshots/{snapshot_id}/raw")
async def get_snapshot_raw_response(
    *,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.MONITORING_VIEW)),
    snapshot_id: int
) -> Any:
    """
    Get the complete raw API response JSON for a specific monitoring snapshot.
    
    This endpoint is specifically designed for debugging and problem analysis.
    It returns the exact JSON response that was returned by the health check API
    at the time the snapshot was created.
    
    Use this when investigating specific incidents or system issues.
    
    Requires permission: monitoring.view
    """
    from app.crud.monitoring import monitoring_snapshot_crud
    
    try:
        raw_response = await monitoring_snapshot_crud.get_raw_response_for_debugging(
            db, snapshot_id=snapshot_id
        )
        
        if not raw_response:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Snapshot with ID {snapshot_id} not found"
            )
        
        return {
            "snapshot_id": snapshot_id,
            "raw_response_json": raw_response,
            "retrieved_at": datetime.now().isoformat(),
            "note": "This is the complete API response as it was captured during the health check"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve raw response: {str(e)}"
        )


@router.get("/history/services/{service_name}/issues")
async def get_service_degradation_periods(
    *,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.MONITORING_VIEW)),
    service_name: str,
    hours: int = Query(24, ge=1, le=168, description="Number of hours to analyze")
) -> Any:
    """
    Find periods when a specific service was degraded or unhealthy.
    
    This endpoint helps identify service degradation patterns and provides
    detailed information about when issues occurred, how long they lasted,
    and the complete context for debugging.
    
    Requires permission: monitoring.view
    """
    from app.crud.monitoring import monitoring_snapshot_crud
    
    try:
        degradation_periods = await monitoring_snapshot_crud.find_service_degradation_periods(
            db, service_name=service_name, hours=hours
        )
        
        return {
            "service_name": service_name,
            "time_period_hours": hours,
            "degradation_periods_count": len(degradation_periods),
            "degradation_periods": degradation_periods,
            "analysis_timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to analyze service degradation: {str(e)}"
        )


@router.get("/history/uptime")
async def get_uptime_statistics(
    *,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.MONITORING_VIEW)),
    service_name: Optional[str] = Query(None, description="Specific service name for detailed analysis"),
    hours: int = Query(24, ge=1, le=168, description="Number of hours for uptime calculation")
) -> Any:
    """
    Get comprehensive uptime statistics from historical monitoring data.
    
    Provides system-wide and service-specific uptime analysis based on
    historical monitoring snapshots.
    
    Requires permission: monitoring.view
    """
    try:
        uptime_stats = await monitoring_service.get_uptime_statistics(
            db, service_name=service_name, hours=hours
        )
        
        return uptime_stats
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to calculate uptime statistics: {str(e)}"
        )


@router.get("/status-page")
async def get_status_page_data(
    *,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(check_permission(Permission.MONITORING_VIEW)),
    days: int = Query(90, ge=1, le=365, description="Number of days for historical trend data")
) -> Any:
    """
    Get StatusPage-style monitoring data with historical trends.
    
    Provides data formatted for a status page display including:
    - Current system status summary
    - Individual service status with historical trends
    - Uptime percentages for each service
    - Daily status indicators (past 90 days)
    
    Requires permission: monitoring.view
    """
    from app.crud.monitoring import monitoring_snapshot_crud
    from app.services.monitoring import monitoring_service
    from datetime import datetime, timedelta, timezone
    from collections import defaultdict
    
    try:
        # Get current system health
        current_health = await monitoring_service.get_system_health_overview(db, force_refresh=False)
        
        # Get historical snapshots for trend analysis
        hours_for_period = days * 24
        historical_snapshots = await monitoring_snapshot_crud.get_recent_snapshots(
            db, hours=hours_for_period, limit=days * 288  # Up to 288 snapshots per day (5min intervals)
        )
        
        # Process historical data by day and service
        service_daily_status = defaultdict(lambda: defaultdict(list))
        service_uptime_stats = {}
        
        # Group snapshots by date and service
        for snapshot in historical_snapshots:
            date_key = snapshot.created_at.date().isoformat()
            
            # Extract service status from services_snapshot
            if snapshot.services_snapshot:
                for service_data in snapshot.services_snapshot:
                    service_name = service_data.get('name')
                    service_status = service_data.get('status', 'unknown')
                    
                    if service_name:
                        service_daily_status[service_name][date_key].append(service_status)
        
        # Calculate uptime statistics for each service
        for service_name in service_daily_status.keys():
            try:
                uptime_stats = await monitoring_service.get_uptime_statistics(
                    db, service_name=service_name, hours=hours_for_period
                )
                service_uptime_stats[service_name] = uptime_stats
            except:
                service_uptime_stats[service_name] = {
                    'uptime_percentage': 0.0,
                    'total_snapshots': 0,
                    'healthy_snapshots': 0
                }
        
        # Generate daily status indicators for each service
        services_status_history = {}
        end_date = datetime.now(timezone.utc).date()
        
        for service_name in service_daily_status.keys():
            daily_indicators = []
            
            for i in range(days):
                date_key = (end_date - timedelta(days=i)).isoformat()
                day_statuses = service_daily_status[service_name].get(date_key, [])
                
                if not day_statuses:
                    status_indicator = 'no-data'
                else:
                    # Determine day status based on majority of checks
                    healthy_count = day_statuses.count('healthy')
                    total_count = len(day_statuses)
                    
                    if healthy_count == total_count:
                        status_indicator = 'healthy'
                    elif healthy_count > total_count * 0.8:
                        status_indicator = 'degraded'  # Minor issues
                    else:
                        status_indicator = 'unhealthy'  # Major issues
                
                daily_indicators.append({
                    'date': date_key,
                    'status': status_indicator,
                    'checks_count': len(day_statuses),
                    'healthy_ratio': day_statuses.count('healthy') / len(day_statuses) if day_statuses else 0
                })
            
            # Reverse to get chronological order (oldest first)
            daily_indicators.reverse()
            services_status_history[service_name] = daily_indicators
        
        # Determine overall system status
        current_services = current_health.services if current_health else []
        healthy_services = [s for s in current_services if s.status.value == 'healthy']
        degraded_services = [s for s in current_services if s.status.value == 'degraded'] 
        unhealthy_services = [s for s in current_services if s.status.value == 'unhealthy']
        
        if len(unhealthy_services) > 0:
            overall_status = 'major_outage'
            status_message = 'MONITORING_SERVICES_MAJOR_ISSUES'
            status_message_params = {"count": len(unhealthy_services)}
        elif len(degraded_services) > 0:
            overall_status = 'minor_outage'
            status_message = 'MONITORING_SERVICES_MINOR_ISSUES'
            status_message_params = {"count": len(degraded_services)}
        else:
            overall_status = 'operational'
            status_message = 'MONITORING_ALL_SYSTEMS_OPERATIONAL'
            status_message_params = {}
        
        # Format service data with current status and historical trends
        services_data = []
        for service in current_services:
            service_name = service.name
            uptime_data = service_uptime_stats.get(service_name, {
                'uptime_percentage': 100.0,
                'total_snapshots': 0,
                'healthy_snapshots': 0
            })
            
            services_data.append({
                'name': service_name,
                'display_name': service.displayName,
                'current_status': service.status.value,
                'uptime_percentage': round(uptime_data.get('uptime_percentage', 100.0), 2),
                'response_time': service.responseTime,
                'last_check': service.lastCheck,
                'daily_status_indicators': services_status_history.get(service_name, []),
                'total_incidents': len([d for d in services_status_history.get(service_name, []) 
                                     if d['status'] in ['degraded', 'unhealthy']]),
                'details': service.details.model_dump() if service.details else {}
            })
        
        return {
            'overall_status': overall_status,
            'status_message': status_message,
            'status_message_params': status_message_params,
            'last_updated': current_health.lastCheck if current_health else datetime.now(timezone.utc).isoformat(),
            'health_score': current_health.healthScore if current_health else 100.0,
            'services': services_data,
            'system_metrics': {
                'cpu_usage': current_health.metrics.cpu if current_health and current_health.metrics else 0,
                'memory_usage': current_health.metrics.memory if current_health and current_health.metrics else 0,
                'disk_usage': current_health.metrics.disk if current_health and current_health.metrics else 0,
                'uptime_hours': current_health.uptimeHours if current_health else 0
            },
            'time_period': {
                'days': days,
                'start_date': (end_date - timedelta(days=days-1)).isoformat(),
                'end_date': end_date.isoformat()
            },
            'statistics': {
                'total_services': len(current_services),
                'healthy_services': len(healthy_services),
                'degraded_services': len(degraded_services),
                'unhealthy_services': len(unhealthy_services),
                'total_snapshots_analyzed': len(historical_snapshots)
            }
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate status page data: {str(e)}"
        )
