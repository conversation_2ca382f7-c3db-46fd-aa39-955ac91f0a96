"""
Redis connection utilities
"""
from typing import Optional
import redis.asyncio as redis
try:
    from app.core.config_simple import settings
except ImportError:
    from app.core.config import settings


class RedisClient:
    """Redis client wrapper with authentication support"""
    
    _instance: Optional[redis.Redis] = None
    
    @classmethod
    async def get_client(cls) -> redis.Redis:
        """Get Redis client instance with authentication"""
        if cls._instance is None:
            cls._instance = redis.from_url(
                settings.redis_url_with_auth,
                encoding="utf-8",
                decode_responses=True
            )
        return cls._instance
    
    @classmethod
    async def close(cls):
        """Close Redis connection"""
        if cls._instance:
            await cls._instance.close()
            cls._instance = None


# Global redis client getter
async def get_redis() -> redis.Redis:
    """Get Redis client for dependency injection"""
    return await RedisClient.get_client()