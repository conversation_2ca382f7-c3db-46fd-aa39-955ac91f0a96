"""add security fields to user model

Revision ID: 20250729_091225
Revises: 
Create Date: 2025-07-29 09:12:25

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '20250729_091225'
down_revision = '20250729_090000'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Add security fields to users table
    op.add_column('users', sa.Column('failed_login_attempts', sa.Integer(), nullable=False, server_default='0'))
    op.add_column('users', sa.Column('locked_until', sa.DateTime(timezone=True), nullable=True))
    op.add_column('users', sa.Column('last_login_at', sa.DateTime(timezone=True), nullable=True))
    op.add_column('users', sa.Column('last_password_change', sa.DateTime(timezone=True), nullable=True))
    
    # Update role column to handle VIEWER -> GUEST migration
    op.execute("UPDATE users SET role = 'guest' WHERE role = 'viewer'")
    
    # Create indexes for better query performance
    op.create_index('idx_users_locked_until', 'users', ['locked_until'])
    op.create_index('idx_users_last_login_at', 'users', ['last_login_at'])


def downgrade() -> None:
    # Drop indexes
    op.drop_index('idx_users_last_login_at', table_name='users')
    op.drop_index('idx_users_locked_until', table_name='users')
    
    # Revert role changes
    op.execute("UPDATE users SET role = 'viewer' WHERE role = 'guest'")
    
    # Remove security fields
    op.drop_column('users', 'last_password_change')
    op.drop_column('users', 'last_login_at')
    op.drop_column('users', 'locked_until')
    op.drop_column('users', 'failed_login_attempts')