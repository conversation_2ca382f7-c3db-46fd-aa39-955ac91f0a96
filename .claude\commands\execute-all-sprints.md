# Execute All Sprints

Execute all TalentForge Pro sprints in sequence with full agent orchestration.

## Execution Strategy

```
/task description="Execute all TalentForge Pro sprints" prompt="
Execute all sprints for TalentForge Pro in sequence:

1. Sprint 1: @PRPs/sprint1_infrastructure_setup.md
2. Sprint 2: @PRPs/sprint2_auth_user_management.md  
3. Sprint 3: @PRPs/sprint3_candidate_management.md
4. Sprint 4: @PRPs/sprint4_ml_service_integration.md
5. Sprint 5: @PRPs/sprint5_performance_deployment.md

Use prp-execution-coordinator for each sprint with:
- Full validation between sprints
- Dependency checking
- Progress reporting
- Error recovery

Each sprint must be fully validated before proceeding to the next.
Generate a comprehensive report after all sprints complete.
" subagent_type="prp-execution-coordinator"
```

## Benefits
- One command to execute entire project
- Automatic sprint sequencing
- Inter-sprint validation
- Comprehensive progress tracking