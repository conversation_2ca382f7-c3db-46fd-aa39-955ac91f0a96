# Validate Sprint

Validate a completed sprint implementation using specialized validation agent.

## Sprint to Validate: $ARGUMENTS

## Validation Process

```
/task description="Validate Sprint: $ARGUMENTS" prompt="
Use validation-lifecycle-manager to comprehensively validate $ARGUMENTS implementation:

1. Load the original PRP requirements
2. Execute all validation levels:
   - Level 1: Syntax and type checking
   - Level 2: Code quality and linting
   - Level 3: Security scanning
   - Level 4: Unit test coverage (≥80%)
   - Level 5: Integration tests
   - Level 6: E2E testing
   - Level 7: Performance benchmarks
   - Level 8: Documentation completeness

3. Generate detailed validation report including:
   - Test results summary
   - Coverage metrics
   - Security scan results
   - Performance metrics
   - Compliance checklist

4. Fix any critical issues found
5. Re-run failed validations
6. Ensure all PRP success criteria are met

Report the final validation status with evidence.
" subagent_type="validation-lifecycle-manager"
```

## Usage Examples
- `/validate-sprint PRPs/sprint1_infrastructure_setup.md`
- `/validate-sprint sprint2` (will find the matching PRP)