"""create_monitoring_snapshots_table

Revision ID: 2fc538b21418
Revises: 21824c9dd8f9
Create Date: 2025-08-12 08:16:28.327269

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql


# revision identifiers, used by Alembic.
revision: str = '2fc538b21418'
down_revision: Union[str, None] = '21824c9dd8f9'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Create monitoring_snapshots table using existing servicestatus enum
    op.create_table('monitoring_snapshots',
        sa.Column('id', sa.BIGINT(), nullable=False),
        sa.Column('created_at', sa.TIMESTAMP(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.TIMESTAMP(timezone=True), server_default=sa.text('now()'), nullable=False),
        
        # Overall system status  
        sa.Column('overall_status', sa.String(20), nullable=False),
        sa.Column('health_score', sa.Float(), nullable=False),
        
        # System metrics at snapshot time
        sa.Column('cpu_usage', sa.Float(), nullable=False),
        sa.Column('memory_usage', sa.Float(), nullable=False),
        sa.Column('disk_usage', sa.Float(), nullable=False),
        
        # Service counts
        sa.Column('services_total', sa.Integer(), nullable=False),
        sa.Column('services_healthy', sa.Integer(), nullable=False),
        sa.Column('services_degraded', sa.Integer(), nullable=False),
        sa.Column('services_unhealthy', sa.Integer(), nullable=False),
        
        # Service uptime tracking
        sa.Column('uptime_hours', sa.Float(), nullable=False),
        
        # Complete API response for problem analysis and debugging
        sa.Column('raw_response_json', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
        
        # Detailed service status (for historical analysis)
        sa.Column('services_snapshot', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
        
        # System metrics snapshot
        sa.Column('metrics_snapshot', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
        
        # Cache information at snapshot time
        sa.Column('cache_info', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        
        # Snapshot source and trigger information
        sa.Column('snapshot_source', sa.String(length=50), nullable=False, server_default='monitoring_service'),
        sa.Column('snapshot_trigger', sa.String(length=100), nullable=True),
        
        # Additional snapshot metadata
        sa.Column('snapshot_metadata', postgresql.JSONB(astext_type=sa.Text()), nullable=True, server_default='{}'),
        
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes for performance optimization
    op.create_index('idx_monitoring_snapshots_created_at', 'monitoring_snapshots', ['created_at'], postgresql_ops={'created_at': 'DESC'})
    op.create_index('idx_monitoring_snapshots_overall_status', 'monitoring_snapshots', ['overall_status'])
    op.create_index('idx_monitoring_snapshots_health_score', 'monitoring_snapshots', ['health_score'])
    op.create_index('idx_monitoring_snapshots_source_trigger', 'monitoring_snapshots', ['snapshot_source', 'snapshot_trigger'])
    
    # Composite indexes for common query patterns
    op.create_index('idx_monitoring_snapshots_status_time', 'monitoring_snapshots', ['overall_status', 'created_at'], postgresql_ops={'created_at': 'DESC'})
    op.create_index('idx_monitoring_snapshots_health_time', 'monitoring_snapshots', ['health_score', 'created_at'], postgresql_ops={'health_score': 'DESC', 'created_at': 'DESC'})
    
    # GIN indexes for JSONB fields (for efficient JSON queries)
    op.create_index('idx_monitoring_snapshots_raw_response', 'monitoring_snapshots', ['raw_response_json'], postgresql_using='gin')
    op.create_index('idx_monitoring_snapshots_services_snapshot', 'monitoring_snapshots', ['services_snapshot'], postgresql_using='gin')


def downgrade() -> None:
    # Drop indexes first
    op.drop_index('idx_monitoring_snapshots_services_snapshot', table_name='monitoring_snapshots')
    op.drop_index('idx_monitoring_snapshots_raw_response', table_name='monitoring_snapshots')
    op.drop_index('idx_monitoring_snapshots_health_time', table_name='monitoring_snapshots')
    op.drop_index('idx_monitoring_snapshots_status_time', table_name='monitoring_snapshots')
    op.drop_index('idx_monitoring_snapshots_source_trigger', table_name='monitoring_snapshots')
    op.drop_index('idx_monitoring_snapshots_health_score', table_name='monitoring_snapshots')
    op.drop_index('idx_monitoring_snapshots_overall_status', table_name='monitoring_snapshots')
    op.drop_index('idx_monitoring_snapshots_created_at', table_name='monitoring_snapshots')
    
    # Drop table
    op.drop_table('monitoring_snapshots')
    
    # Note: We don't drop the servicestatus enum type as it might be used by other tables