"""add resume_vectors and candidate_assessments tables for intelligent recruitment

Revision ID: 20250810_add_resume_vectors_and_assessments
Revises: c45e8b9f1a2d
Create Date: 2025-08-10 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '20250810_sprint4'
down_revision = 'c45e8b9f1a2d'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Ensure pgvector extension exists
    op.execute("CREATE EXTENSION IF NOT EXISTS vector")
    
    # Create resume_vectors table for candidate embeddings
    op.create_table(
        'resume_vectors',
        sa.Column('id', sa.BigInteger(), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        
        # Relationship to candidates
        sa.Column('candidate_id', sa.<PERSON>(), nullable=False),
        
        # Multi-granularity embeddings - DeepSeek uses 768 dimensions
        sa.Column('full_vector', postgresql.ARRAY(sa.Float(), dimensions=1), nullable=False),  # Will be altered to vector(768)
        sa.Column('experience_vector', postgresql.ARRAY(sa.Float(), dimensions=1), nullable=True),  # Will be altered to vector(768)
        sa.Column('education_vector', postgresql.ARRAY(sa.Float(), dimensions=1), nullable=True),  # Will be altered to vector(768)
        sa.Column('skills_vector', postgresql.ARRAY(sa.Float(), dimensions=1), nullable=True),  # Will be altered to vector(768)
        
        # Cached parsed data for performance
        sa.Column('parsed_data', postgresql.JSONB(astext_type=sa.Text()), nullable=False, server_default='{}'),
        sa.Column('raw_text', sa.Text(), nullable=True),
        sa.Column('parser_confidence', sa.Float(), nullable=True, server_default='0.0'),
        
        # Embedding metadata
        sa.Column('embedding_model', sa.String(length=50), nullable=True, server_default='deepseek-chat'),
        sa.Column('embedding_provider', sa.String(length=20), nullable=True, server_default='deepseek'),
        sa.Column('embedding_dimension', sa.Integer(), nullable=True, server_default='768'),
        sa.Column('token_count', sa.Integer(), nullable=True),
        sa.Column('generation_time_ms', sa.Float(), nullable=True),
        sa.Column('parser_version', sa.String(length=20), nullable=True, server_default='v1.0'),
        sa.Column('generated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        
        sa.PrimaryKeyConstraint('id'),
        sa.ForeignKeyConstraint(['candidate_id'], ['candidates.id'], ondelete='CASCADE'),
        sa.UniqueConstraint('candidate_id', name='uq_resume_vector_candidate')
    )
    
    # Create indexes for resume_vectors
    op.create_index('idx_resume_vector_candidate', 'resume_vectors', ['candidate_id'], unique=False)
    op.create_index('idx_resume_vector_provider', 'resume_vectors', ['embedding_provider'], unique=False)
    op.create_index('idx_resume_vector_generated_at', 'resume_vectors', ['generated_at'], unique=False)
    
    # Alter columns to use vector type with 768 dimensions (DeepSeek)
    op.execute("ALTER TABLE resume_vectors ALTER COLUMN full_vector TYPE vector(768) USING full_vector::vector(768)")
    op.execute("ALTER TABLE resume_vectors ALTER COLUMN experience_vector TYPE vector(768) USING experience_vector::vector(768)")
    op.execute("ALTER TABLE resume_vectors ALTER COLUMN education_vector TYPE vector(768) USING education_vector::vector(768)")
    op.execute("ALTER TABLE resume_vectors ALTER COLUMN skills_vector TYPE vector(768) USING skills_vector::vector(768)")
    
    # Create HNSW indexes for fast similarity search
    op.execute("""
        CREATE INDEX idx_resume_full_vector_hnsw 
        ON resume_vectors 
        USING hnsw (full_vector vector_cosine_ops)
        WITH (m = 16, ef_construction = 64)
    """)
    
    op.execute("""
        CREATE INDEX idx_resume_skills_vector_hnsw 
        ON resume_vectors 
        USING hnsw (skills_vector vector_cosine_ops)
        WITH (m = 16, ef_construction = 64)
    """)
    
    # Create candidate_assessments table for five-dimensional scoring
    op.create_table(
        'candidate_assessments',
        sa.Column('id', sa.BigInteger(), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        
        # Relationship to candidates
        sa.Column('candidate_id', sa.BigInteger(), nullable=False),
        
        # Five-dimensional scores (0-100 scale)
        sa.Column('digital_literacy_score', sa.Float(), nullable=False, server_default='0.0'),
        sa.Column('industry_skills_score', sa.Float(), nullable=False, server_default='0.0'),
        sa.Column('position_skills_score', sa.Float(), nullable=False, server_default='0.0'),
        sa.Column('innovation_score', sa.Float(), nullable=False, server_default='0.0'),
        sa.Column('learning_potential_score', sa.Float(), nullable=False, server_default='0.0'),
        
        # Composite scores
        sa.Column('dci_score', sa.Float(), nullable=False, server_default='0.0'),  # Digital Capability Index
        sa.Column('overall_percentile', sa.Float(), nullable=True),  # Percentile ranking
        
        # Detailed assessment data
        sa.Column('assessment_details', postgresql.JSONB(astext_type=sa.Text()), nullable=True, server_default='{}'),
        sa.Column('strengths', postgresql.JSONB(astext_type=sa.Text()), nullable=True, server_default='[]'),
        sa.Column('improvement_areas', postgresql.JSONB(astext_type=sa.Text()), nullable=True, server_default='[]'),
        sa.Column('recommendations', postgresql.JSONB(astext_type=sa.Text()), nullable=True, server_default='[]'),
        
        # Assessment metadata
        sa.Column('assessment_version', sa.String(length=50), nullable=True, server_default='v1.0'),
        sa.Column('assessed_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('assessor_ai_model', sa.String(length=50), nullable=True, server_default='deepseek-chat'),
        sa.Column('assessment_confidence', sa.Float(), nullable=True, server_default='0.0'),
        
        sa.PrimaryKeyConstraint('id'),
        sa.ForeignKeyConstraint(['candidate_id'], ['candidates.id'], ondelete='CASCADE'),
        sa.UniqueConstraint('candidate_id', name='uq_assessment_candidate')
    )
    
    # Create indexes for candidate_assessments
    op.create_index('idx_assessment_candidate', 'candidate_assessments', ['candidate_id'], unique=False)
    op.create_index('idx_assessment_dci_score', 'candidate_assessments', ['dci_score'], unique=False)
    op.create_index('idx_assessment_percentile', 'candidate_assessments', ['overall_percentile'], unique=False)
    op.create_index('idx_assessment_assessed_at', 'candidate_assessments', ['assessed_at'], unique=False)
    op.create_index('idx_assessment_digital_literacy', 'candidate_assessments', ['digital_literacy_score'], unique=False)
    op.create_index('idx_assessment_industry_skills', 'candidate_assessments', ['industry_skills_score'], unique=False)
    
    # Add composite indexes for multi-dimensional queries
    op.create_index('idx_assessment_scores_composite', 'candidate_assessments', 
                   ['dci_score', 'overall_percentile', 'assessed_at'], unique=False)


def downgrade() -> None:
    # Drop indexes for candidate_assessments
    op.drop_index('idx_assessment_scores_composite', 'candidate_assessments')
    op.drop_index('idx_assessment_industry_skills', 'candidate_assessments')
    op.drop_index('idx_assessment_digital_literacy', 'candidate_assessments')
    op.drop_index('idx_assessment_assessed_at', 'candidate_assessments')
    op.drop_index('idx_assessment_percentile', 'candidate_assessments')
    op.drop_index('idx_assessment_dci_score', 'candidate_assessments')
    op.drop_index('idx_assessment_candidate', 'candidate_assessments')
    
    # Drop candidate_assessments table
    op.drop_table('candidate_assessments')
    
    # Drop HNSW indexes for resume_vectors
    op.drop_index('idx_resume_skills_vector_hnsw', 'resume_vectors')
    op.drop_index('idx_resume_full_vector_hnsw', 'resume_vectors')
    
    # Drop other indexes for resume_vectors
    op.drop_index('idx_resume_vector_generated_at', 'resume_vectors')
    op.drop_index('idx_resume_vector_provider', 'resume_vectors')
    op.drop_index('idx_resume_vector_candidate', 'resume_vectors')
    
    # Drop resume_vectors table
    op.drop_table('resume_vectors')