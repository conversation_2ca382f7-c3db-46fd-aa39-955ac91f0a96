"""
Core enums for the TalentForge Pro system
"""
from enum import Enum


class UserRole(str, Enum):
    """User role enumeration"""
    SUPER_ADMIN = "super_admin"      # 超级管理员
    ADMIN = "admin"                  # 管理员
    HR_MANAGER = "hr_manager"        # HR经理
    HR_SPECIALIST = "hr_specialist"  # HR专员
    INTERVIEWER = "interviewer"      # 面试官
    GUEST = "guest"                  # 访客


class DataPermission(str, Enum):
    """Data permission levels for candidates and other resources"""
    PRIVATE = "PRIVATE"    # 私有：仅创建者可见
    SHARED = "SHARED"      # 共享：创建者和指定用户可见
    TEAM = "TEAM"          # 团队：团队成员可见
    PUBLIC = "PUBLIC"      # 公开：所有用户可见


class EducationLevel(str, Enum):
    """Education level constants - stored in database"""
    HIGH_SCHOOL = "high_school"
    ASSOCIATE = "associate"      # 专科/大专
    BACHELOR = "bachelor"        # 本科
    MASTER = "master"           # 硕士
    DOCTORATE = "doctorate"     # 博士
    OTHER = "other"


class ExperienceRange(str, Enum):
    """Experience range constants for statistics"""
    ENTRY = "0-2"        # 0-2 years
    JUNIOR = "3-5"       # 3-5 years
    MID = "6-10"         # 6-10 years
    SENIOR = "11-15"     # 11-15 years
    EXPERT = "16+"       # 16+ years


class SalaryRange(str, Enum):
    """Salary range constants (in 10k RMB/year) for statistics"""
    RANGE_0_10 = "0-10"      # 0-100k RMB/year
    RANGE_10_20 = "10-20"    # 100k-200k RMB/year
    RANGE_20_30 = "20-30"    # 200k-300k RMB/year
    RANGE_30_50 = "30-50"    # 300k-500k RMB/year
    RANGE_50_PLUS = "50+"    # 500k+ RMB/year


class CandidateSource(str, Enum):
    """Candidate source constants - stored in database"""
    LINKEDIN = "linkedin"
    REFERRAL = "referral"
    JOB_BOARD = "job_board"
    COMPANY_WEBSITE = "company_website"
    RECRUITMENT_AGENCY = "recruitment_agency"
    CAMPUS = "campus"
    INTERNAL = "internal"
    OTHER = "other"


class Permission(str, Enum):
    """System permission enumeration"""
    # User management
    USERS_READ = "users:read"
    USERS_WRITE = "users:write"
    USERS_DELETE = "users:delete"
    
    # Role management
    ROLES_READ = "roles:read"
    ROLES_WRITE = "roles:write"
    ROLES_DELETE = "roles:delete"
    
    # Permission management
    PERMISSIONS_READ = "permissions:read"
    PERMISSIONS_WRITE = "permissions:write"
    PERMISSIONS_DELETE = "permissions:delete"
    
    # Candidate management
    CANDIDATES_READ = "candidates:read"
    CANDIDATES_WRITE = "candidates:write"
    CANDIDATES_DELETE = "candidates:delete"
    CANDIDATES_EXPORT = "candidates:export"
    
    # Position management
    POSITIONS_READ = "positions:read"
    POSITIONS_WRITE = "positions:write"
    POSITIONS_DELETE = "positions:delete"
    
    # AI services
    AI_MATCH = "ai:match"
    AI_ASSESS = "ai:assess"
    
    # Reports
    REPORTS_VIEW = "reports:view"
    REPORTS_EXPORT = "reports:export"
    
    # Monitoring
    MONITORING_VIEW = "monitoring:view"
    
    # System management
    SYSTEM_CONFIG = "system:config"
    SYSTEM_LOGS = "system:logs"


# Role-permission mapping
ROLE_PERMISSIONS = {
    UserRole.SUPER_ADMIN: [p.value for p in Permission],  # All permissions
    UserRole.ADMIN: [
        Permission.USERS_READ.value,
        Permission.USERS_WRITE.value,
        Permission.ROLES_READ.value,
        Permission.ROLES_WRITE.value,
        Permission.PERMISSIONS_READ.value,
        Permission.CANDIDATES_READ.value,
        Permission.CANDIDATES_WRITE.value,
        Permission.CANDIDATES_DELETE.value,
        Permission.POSITIONS_READ.value,
        Permission.POSITIONS_WRITE.value,
        Permission.POSITIONS_DELETE.value,
        Permission.AI_MATCH.value,
        Permission.AI_ASSESS.value,
        Permission.REPORTS_VIEW.value,
        Permission.REPORTS_EXPORT.value,
        Permission.MONITORING_VIEW.value,
    ],
    UserRole.HR_MANAGER: [
        Permission.ROLES_READ.value,
        Permission.PERMISSIONS_READ.value,
        Permission.CANDIDATES_READ.value,
        Permission.CANDIDATES_WRITE.value,
        Permission.CANDIDATES_EXPORT.value,
        Permission.POSITIONS_READ.value,
        Permission.POSITIONS_WRITE.value,
        Permission.AI_MATCH.value,
        Permission.AI_ASSESS.value,
        Permission.REPORTS_VIEW.value,
        Permission.REPORTS_EXPORT.value,
        Permission.MONITORING_VIEW.value,
    ],
    UserRole.HR_SPECIALIST: [
        Permission.CANDIDATES_READ.value,
        Permission.CANDIDATES_WRITE.value,
        Permission.POSITIONS_READ.value,
        Permission.AI_MATCH.value,
        Permission.AI_ASSESS.value,
        Permission.REPORTS_VIEW.value,
    ],
    UserRole.INTERVIEWER: [
        Permission.CANDIDATES_READ.value,
        Permission.POSITIONS_READ.value,
        Permission.AI_MATCH.value,
    ],
    UserRole.GUEST: [
        Permission.CANDIDATES_READ.value,
        Permission.POSITIONS_READ.value,
    ],
}