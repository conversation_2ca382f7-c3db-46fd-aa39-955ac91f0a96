"""auto_merge_heads

Revision ID: 26245aed3962
Revises: 004c96ecc58b
Create Date: 2025-08-11 03:58:10.366941

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '26245aed3962'
down_revision: Union[str, None] = '004c96ecc58b'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass