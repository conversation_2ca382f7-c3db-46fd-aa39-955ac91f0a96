# TalentForge Pro - Repository Context Analysis

## Project Overview

**TalentForge Pro** is a comprehensive intelligent talent assessment and job-candidate matching system that leverages a hybrid architecture combining traditional rule engines with Large Language Models (LLMs). The system provides end-to-end recruitment management with AI-powered resume analysis, position matching, and candidate evaluation.

### Key Value Proposition
- **Intelligent Matching**: Vector-based similarity search using pgvector with BGE-M3 embeddings
- **Multi-dimensional Assessment**: 5-factor evaluation system (Digital Literacy, Industry Skills, Job Skills, Innovation, Learning Potential)
- **Hybrid Architecture**: Rule-based + LLM approach for balanced performance and accuracy
- **Modern Stack**: Full containerized microservices with React/Next.js frontend

## Technology Stack Analysis

### Backend Architecture
- **Framework**: FastAPI 0.110+ (Python 3.12)
- **Database**: PostgreSQL 17 with pgvector 0.8.0 extension
- **ORM**: SQLAlchemy 2.0 with async support
- **Authentication**: JWT with refresh token mechanism
- **Task Queue**: Celery with Redis broker
- **Vector Search**: pgvector with HNSW indexing
- **File Storage**: MinIO (S3-compatible object storage)
- **ML Embeddings**: Ollama 0.11.4 with BGE-M3 (1024-dim vectors)

### Frontend Architecture
- **Framework**: Next.js 15.4.1 with React 19
- **Language**: TypeScript 5.5+ with strict type checking
- **UI Components**: shadcn/ui with Radix UI primitives
- **Styling**: Tailwind CSS 3.4.1 with custom design system
- **State Management**: Redux Toolkit + TanStack Query
- **Internationalization**: next-intl with React Context
- **Testing**: Jest + Playwright for E2E testing
- **Package Manager**: pnpm 10.13.1

### Infrastructure & DevOps
- **Containerization**: Docker Compose with multi-service orchestration
- **Reverse Proxy**: Nginx with unified proxy architecture (port 8088)
- **Monitoring**: Prometheus + optional Grafana stack
- **Development**: Hot reload enabled, volume-based code synchronization
- **Build System**: Makefile with environment-aware operations
- **Data Persistence**: Named volumes with ~/dev_data/ host mapping

### Key Dependencies

#### Backend Core
```toml
fastapi = "^0.110.0"           # Modern async web framework
sqlalchemy = "^2.0.0"          # Database ORM with async support
pydantic = "^2.7.0"           # Data validation and serialization
pgvector = "^0.2.5"           # PostgreSQL vector extension
ollama = "^0.3.0"             # Local LLM integration
celery = "^5.4.0"             # Distributed task queue
alembic = "^1.13.0"           # Database migrations
```

#### Frontend Core
```json
{
  "next": "15.4.1",                    // React framework with SSR
  "react": "^19.0.0",                 // Latest React with concurrent features
  "@tanstack/react-query": "^5.50.0", // Server state management
  "@reduxjs/toolkit": "^2.2.5",       // Client state management
  "tailwindcss": "3.4.1",             // Utility-first CSS framework
  "next-intl": "^4.3.4",              // Internationalization
  "zod": "^3.23.0"                    // Runtime type validation
}
```

## Project Structure Analysis

### Directory Organization
```
talent_forge_pro/
├── app/                    # Main application directory (Docker context)
│   ├── backend/           # FastAPI backend service
│   ├── frontend/          # Next.js frontend application
│   ├── configs/           # Configuration files (nginx, prometheus)
│   ├── scripts/           # Development and deployment scripts
│   └── docker-compose.yml # Service orchestration
├── docs/                  # Comprehensive documentation
├── examples/              # Code examples and patterns
├── PRPs/                  # Project Requirements Process documents
├── archive/               # Historical and cleanup records
└── Makefile               # Unified development commands
```

### Code Organization Patterns

#### Backend Layered Architecture
```
app/backend/app/
├── api/v1/               # API endpoints organized by domain
├── core/                 # Core system components (config, auth, etc.)
├── crud/                 # Database access layer
├── models/               # SQLAlchemy database models
├── schemas/              # Pydantic request/response models
├── services/             # Business logic layer
└── utils/                # Utility functions and helpers
```

#### Frontend Domain-Driven Structure
```
app/frontend/
├── app/                  # Next.js app directory structure
│   ├── (auth)/          # Authentication pages
│   ├── (dashboard)/     # Main application pages
│   └── i18n/            # Internationalization setup
├── components/          # Reusable UI components
├── lib/                 # Utilities and configurations
├── services/            # API service layer
├── stores/              # State management
└── types/               # TypeScript type definitions
```

## Development Workflow & Standards

### Git Workflow
- **Main Branch**: `master` (single branch development)
- **Commit Convention**: Conventional commits (`feat:`, `fix:`, `docs:`, etc.)
- **Recent Activity**: Active development on sidebar navigation and statistics features

### Development Environment
- **Unified Access**: http://localhost:8088 (recommended)
- **Hot Reload**: Enabled for both frontend and backend
- **Package Management**: Containerized with host volume caching
- **Database**: PostgreSQL with persistent data in ~/dev_data/

### Code Quality Standards

#### Backend Standards
- **Type Annotations**: Mandatory for all functions
- **Async/Await**: Consistent async programming patterns
- **Error Handling**: Structured exceptions with error codes
- **Testing**: pytest with >80% coverage requirement
- **API Design**: RESTful with consistent response formats

#### Frontend Standards
- **TypeScript**: Strict mode with no `any` types
- **Component Pattern**: Functional components with hooks
- **State Management**: Redux Toolkit + TanStack Query combination
- **UI Consistency**: shadcn/ui components with design system
- **Testing**: Jest + React Testing Library + Playwright

### Architecture Compliance Rules

#### Mandatory Structure Rules
- **Root Directory**: Clean architecture - only essential files allowed
- **Docker Context**: All services must reside in `app/` directory
- **Configuration**: Environment-specific configs in `app/configs/`
- **Documentation**: Technical docs in `docs/auto/`, reports in `docs/reports/`

#### Development Constraints
- **ID System**: Snowflake IDs (BigInteger) throughout the system
- **Authentication**: JWT with error code system (not hardcoded messages)
- **Internationalization**: All user-facing text must support i18n
- **API Consistency**: Unified pagination (`skip/limit`) and error response formats

## Integration Points & APIs

### Core API Endpoints
```
/api/v1/
├── auth/                 # Authentication & authorization
├── candidates/           # Candidate management
├── positions/            # Job position management
├── assessments/          # Multi-dimensional evaluations
├── matching/             # AI-powered candidate-position matching
├── vectors/              # Vector operations and similarity search
├── users/                # User management
├── admin/                # Administrative functions
└── public/               # Public-facing application endpoints
```

### Key Integration Services
- **Vector Search**: pgvector with cosine similarity for candidate-position matching
- **File Processing**: Resume parsing with PDF/DOCX support via MinIO storage
- **Background Jobs**: Celery tasks for ML processing and data enrichment
- **Embeddings**: Ollama service for generating semantic vectors from text

## Performance & Scalability Considerations

### Performance Targets
- **API Response**: <200ms for standard operations
- **First Paint**: <3s on 3G networks
- **ML Inference**: <200ms for vector operations
- **Database**: Optimized queries with proper indexing

### Scalability Architecture
- **Horizontal Scaling**: Containerized services ready for Kubernetes
- **Caching Strategy**: Redis for session and computational caching
- **Vector Operations**: HNSW indexing for O(log n) similarity searches
- **File Storage**: MinIO cluster-ready object storage

## Security & Compliance

### Security Measures
- **Authentication**: JWT with refresh token rotation
- **Authorization**: Role-based access control (RBAC)
- **Input Validation**: Pydantic schemas with comprehensive validation
- **SQL Injection**: SQLAlchemy ORM prevents direct SQL exposure
- **XSS Protection**: Next.js built-in protections + CSP headers

### Data Privacy
- **Permission System**: Granular access controls (`PRIVATE`, `SHARED`, `TEAM`, `PUBLIC`)
- **Sensitive Data**: Encrypted storage for PII and resume data
- **Audit Trail**: Comprehensive logging for compliance tracking

## Development Environment Setup

### Quick Start Commands
```bash
# Environment setup
make setup                    # First-time initialization
make up                       # Start all services (development mode)
make logs                     # View service logs
make health                   # Check system health
make down                     # Stop all services

# Development workflows  
make frontend-package-refresh # Update frontend dependencies
make backend-package-refresh  # Update backend dependencies
make db-migrate              # Run database migrations
```

### Key Development URLs
- **Main Application**: http://localhost:8088
- **API Documentation**: http://localhost:8088/docs  
- **Backend Direct**: http://localhost:8001
- **Frontend Direct**: http://localhost:3000
- **Database**: localhost:5432 (PostgreSQL)
- **Cache**: localhost:6379 (Redis)

## Testing Architecture

### Test Strategy
- **Unit Tests**: Backend services and utilities (>80% coverage)
- **Integration Tests**: Database operations and API endpoints  
- **E2E Tests**: Critical user journeys with Playwright
- **API Tests**: Contract testing with documented endpoints

### Test Environment
- **Backend**: pytest with asyncio support and test database
- **Frontend**: Jest + React Testing Library for components
- **E2E**: Playwright for cross-browser user journey testing
- **Mock Data**: Comprehensive mock services for development

## Current Development Focus

Based on recent commit history, the project is actively developing:

1. **Sidebar Navigation Enhancement** - Improving user experience and navigation
2. **Statistics & Analytics** - Adding comprehensive reporting features  
3. **Position Management** - Enhanced job position functionality
4. **UI/UX Improvements** - Modern component updates with drag-and-drop features

## Recommendations for New Development

### Prerequisites for New Features
1. **Schema Validation**: Ensure backend Pydantic schemas match frontend TypeScript types
2. **API Documentation**: Update Swagger docs for any new endpoints
3. **Internationalization**: All new text must support multiple languages
4. **Testing**: Unit tests required for backend services, E2E tests for user workflows
5. **Error Handling**: Use structured error codes, not hardcoded messages

### Integration Best Practices
1. **Vector Operations**: Leverage existing pgvector infrastructure for any similarity features
2. **File Handling**: Use established MinIO patterns for document storage
3. **Background Processing**: Utilize Celery for any long-running operations
4. **State Management**: Follow established Redux + TanStack Query patterns
5. **UI Components**: Use shadcn/ui components for consistency

This repository represents a mature, production-ready intelligent recruitment platform with comprehensive documentation, strict development standards, and a focus on scalable, maintainable code architecture.