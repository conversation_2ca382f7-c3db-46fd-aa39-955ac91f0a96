"""Add candidate_vectors table for BGE-M3 embeddings

Revision ID: 20250813_add_candidate_vectors
Revises: f63ed8676977  
Create Date: 2025-08-13 16:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '20250813_add_candidate_vectors'
down_revision = 'f63ed8676977'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Ensure pgvector extension exists
    op.execute("CREATE EXTENSION IF NOT EXISTS vector")
    
    # Create candidate_vectors table for BGE-M3 embeddings (1024 dimensions)
    op.create_table(
        'candidate_vectors',
        sa.Column('id', sa.BigInteger(), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
        
        # Foreign key to candidates table
        sa.Column('candidate_id', sa.BigInteger(), nullable=False),
        
        # Multi-granularity embeddings (1024 dimensions for BGE-M3)
        sa.Column('full_text_vector', postgresql.ARRAY(sa.Float(), dimensions=1), nullable=False),
        sa.Column('skills_vector', postgresql.ARRAY(sa.Float(), dimensions=1), nullable=True),
        sa.Column('experience_vector', postgresql.ARRAY(sa.Float(), dimensions=1), nullable=True),
        
        # Metadata
        sa.Column('embedding_model', sa.String(length=50), nullable=True, server_default='bge-m3'),
        sa.Column('embedding_provider', sa.String(length=20), nullable=True, server_default='ollama'),
        
        sa.PrimaryKeyConstraint('id'),
        sa.ForeignKeyConstraint(['candidate_id'], ['candidates.id'], ondelete='CASCADE'),
        sa.UniqueConstraint('candidate_id', name='uq_candidate_vectors_candidate_id')
    )
    
    # Convert to proper vector type with 1024 dimensions
    op.execute("ALTER TABLE candidate_vectors ALTER COLUMN full_text_vector TYPE vector(1024) USING full_text_vector::vector(1024)")
    op.execute("ALTER TABLE candidate_vectors ALTER COLUMN skills_vector TYPE vector(1024) USING skills_vector::vector(1024)")  
    op.execute("ALTER TABLE candidate_vectors ALTER COLUMN experience_vector TYPE vector(1024) USING experience_vector::vector(1024)")
    
    # Create HNSW indexes for fast similarity search
    op.execute("""
        CREATE INDEX idx_candidate_full_vector_hnsw 
        ON candidate_vectors 
        USING hnsw (full_text_vector vector_cosine_ops)
        WITH (m = 16, ef_construction = 64)
    """)
    
    op.execute("""
        CREATE INDEX idx_candidate_skills_vector_hnsw
        ON candidate_vectors 
        USING hnsw (skills_vector vector_cosine_ops)
        WITH (m = 16, ef_construction = 64)
    """)
    
    op.execute("""
        CREATE INDEX idx_candidate_exp_vector_hnsw
        ON candidate_vectors 
        USING hnsw (experience_vector vector_cosine_ops)
        WITH (m = 16, ef_construction = 64)
    """)
    
    # Create regular index for candidate_id
    op.create_index('idx_candidate_vectors_candidate_id', 'candidate_vectors', ['candidate_id'], unique=False)


def downgrade() -> None:
    # Drop indexes
    op.drop_index('idx_candidate_vectors_candidate_id', 'candidate_vectors')
    op.drop_index('idx_candidate_exp_vector_hnsw', 'candidate_vectors')
    op.drop_index('idx_candidate_skills_vector_hnsw', 'candidate_vectors')
    op.drop_index('idx_candidate_full_vector_hnsw', 'candidate_vectors')
    
    # Drop table
    op.drop_table('candidate_vectors')