"""auto_merge_heads

Revision ID: b51adae5546b
Revises: c742c52f7150
Create Date: 2025-08-13 01:58:43.348007

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'b51adae5546b'
down_revision: Union[str, None] = 'c742c52f7150'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass