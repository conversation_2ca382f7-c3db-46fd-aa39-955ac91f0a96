'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Checkbox } from '@/components/ui/checkbox';
import { Slider } from '@/components/ui/slider';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Sparkles, 
  ChevronRight, 
  ChevronLeft,
  Info,
  AlertCircle,
  CheckCircle,
  Loader2,
  FileT<PERSON>t,
  <PERSON>tings,
  Eye
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { aiQuestionnaireService, QuestionnaireTemplate } from '@/services/aiQuestionnaireService';

interface AIQuestionnaireWizardProps {
  onComplete?: (questionnaire: any) => void;
  onCancel?: () => void;
}

export function AIQuestionnaireWizard({ onComplete, onCancel }: AIQuestionnaireWizardProps) {
  const router = useRouter();
  const { toast } = useToast();
  const [currentStep, setCurrentStep] = useState(1);
  const [isGenerating, setIsGenerating] = useState(false);
  const [templates, setTemplates] = useState<QuestionnaireTemplate[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<string>('');
  const [generatedQuestionnaire, setGeneratedQuestionnaire] = useState<any>(null);

  // Form data
  const [formData, setFormData] = useState({
    position_type: '',
    dimensions: [] as string[],
    question_count: 20,
    industry: '烟草',
    custom_requirements: '',
    difficulty_level: 'medium' as 'easy' | 'medium' | 'hard',
  });

  // Load templates on mount
  useEffect(() => {
    loadTemplates();
  }, []);

  const loadTemplates = async () => {
    try {
      const data = await aiQuestionnaireService.getTemplates();
      setTemplates(data);
    } catch (error) {
      console.error('Failed to load templates:', error);
      toast({
        title: '加载模板失败',
        description: '无法加载问卷模板，请稍后重试',
        variant: 'destructive',
      });
    }
  };

  const handleTemplateSelect = (templateType: string) => {
    const template = templates.find(t => t.position_type === templateType);
    if (template) {
      setSelectedTemplate(templateType);
      setFormData(prev => ({
        ...prev,
        position_type: template.position_type,
        dimensions: template.dimensions,
      }));
    }
  };

  const handleDimensionToggle = (dimension: string) => {
    setFormData(prev => ({
      ...prev,
      dimensions: prev.dimensions.includes(dimension)
        ? prev.dimensions.filter(d => d !== dimension)
        : [...prev.dimensions, dimension],
    }));
  };

  const handleGenerate = async () => {
    setIsGenerating(true);
    try {
      const questionnaire = await aiQuestionnaireService.generateQuestionnaire(formData);
      setGeneratedQuestionnaire(questionnaire);
      toast({
        title: '生成成功',
        description: '问卷已成功生成，请预览内容',
      });
      setCurrentStep(4); // Go to preview step
    } catch (error) {
      console.error('Generation failed:', error);
      toast({
        title: '生成失败',
        description: '问卷生成失败，请检查设置后重试',
        variant: 'destructive',
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const handleSave = () => {
    if (generatedQuestionnaire && onComplete) {
      onComplete(generatedQuestionnaire);
    } else {
      router.push('/dashboard/questionnaires');
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold mb-4">选择岗位类型</h3>
              <div className="grid grid-cols-2 gap-4">
                {templates.map((template) => (
                  <Card
                    key={template.position_type}
                    className={`cursor-pointer transition-all ${
                      selectedTemplate === template.position_type
                        ? 'ring-2 ring-primary'
                        : 'hover:shadow-md'
                    }`}
                    onClick={() => handleTemplateSelect(template.position_type)}
                  >
                    <CardHeader>
                      <CardTitle className="text-base">{template.position_type}</CardTitle>
                      <CardDescription className="text-sm">
                        {template.description}
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="flex flex-wrap gap-1">
                        {template.focus_areas.slice(0, 3).map((area, idx) => (
                          <Badge key={idx} variant="secondary" className="text-xs">
                            {area}
                          </Badge>
                        ))}
                        {template.focus_areas.length > 3 && (
                          <Badge variant="secondary" className="text-xs">
                            +{template.focus_areas.length - 3}
                          </Badge>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>

            {selectedTemplate && (
              <Alert>
                <Info className="h-4 w-4" />
                <AlertDescription>
                  已选择: {selectedTemplate}。该模板包含 {formData.dimensions.length} 个评估维度。
                </AlertDescription>
              </Alert>
            )}
          </div>
        );

      case 2:
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold mb-4">评估维度配置</h3>
              <p className="text-sm text-muted-foreground mb-4">
                选择要包含在问卷中的评估维度（至少选择3个）
              </p>
              <div className="grid grid-cols-2 gap-3">
                {templates
                  .find(t => t.position_type === selectedTemplate)
                  ?.dimensions.map((dimension) => (
                    <div key={dimension} className="flex items-center space-x-2">
                      <Checkbox
                        id={dimension}
                        checked={formData.dimensions.includes(dimension)}
                        onCheckedChange={() => handleDimensionToggle(dimension)}
                      />
                      <Label
                        htmlFor={dimension}
                        className="text-sm font-normal cursor-pointer"
                      >
                        {dimension}
                      </Label>
                    </div>
                  ))}
              </div>
            </div>

            <div>
              <Label htmlFor="question_count">题目数量: {formData.question_count}</Label>
              <Slider
                id="question_count"
                min={10}
                max={50}
                step={5}
                value={[formData.question_count]}
                onValueChange={(value) =>
                  setFormData(prev => ({ ...prev, question_count: value[0] }))
                }
                className="mt-2"
              />
              <p className="text-xs text-muted-foreground mt-1">
                建议 15-25 题，确保评估全面且不过于冗长
              </p>
            </div>

            <div>
              <Label htmlFor="difficulty">难度级别</Label>
              <Select
                value={formData.difficulty_level}
                onValueChange={(value: 'easy' | 'medium' | 'hard') =>
                  setFormData(prev => ({ ...prev, difficulty_level: value }))
                }
              >
                <SelectTrigger id="difficulty">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="easy">简单 - 基础知识</SelectItem>
                  <SelectItem value="medium">中等 - 综合应用</SelectItem>
                  <SelectItem value="hard">困难 - 深度分析</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        );

      case 3:
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold mb-4">自定义要求</h3>
              <p className="text-sm text-muted-foreground mb-4">
                提供额外的要求或说明（可选）
              </p>
            </div>

            <div>
              <Label htmlFor="industry">行业背景</Label>
              <Input
                id="industry"
                value={formData.industry}
                onChange={(e) =>
                  setFormData(prev => ({ ...prev, industry: e.target.value }))
                }
                placeholder="例如：烟草、制造、金融"
              />
            </div>

            <div>
              <Label htmlFor="requirements">特殊要求</Label>
              <Textarea
                id="requirements"
                value={formData.custom_requirements}
                onChange={(e) =>
                  setFormData(prev => ({ ...prev, custom_requirements: e.target.value }))
                }
                placeholder="例如：重点考察实践经验、包含情景题、注重创新能力等"
                rows={4}
              />
            </div>

            <Card className="bg-muted/50">
              <CardHeader>
                <CardTitle className="text-base">生成预览</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">岗位类型:</span>
                  <span className="font-medium">{formData.position_type}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">评估维度:</span>
                  <span className="font-medium">{formData.dimensions.length} 个</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">题目数量:</span>
                  <span className="font-medium">{formData.question_count} 题</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">难度级别:</span>
                  <span className="font-medium">
                    {formData.difficulty_level === 'easy' ? '简单' :
                     formData.difficulty_level === 'medium' ? '中等' : '困难'}
                  </span>
                </div>
              </CardContent>
            </Card>
          </div>
        );

      case 4:
        return (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">问卷预览</h3>
              <Badge variant="success">
                <CheckCircle className="h-3 w-3 mr-1" />
                生成完成
              </Badge>
            </div>

            {generatedQuestionnaire ? (
              <Tabs defaultValue="overview" className="w-full">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="overview">概览</TabsTrigger>
                  <TabsTrigger value="questions">题目</TabsTrigger>
                  <TabsTrigger value="settings">设置</TabsTrigger>
                </TabsList>
                
                <TabsContent value="overview" className="space-y-4">
                  <Card>
                    <CardHeader>
                      <CardTitle>{generatedQuestionnaire.title}</CardTitle>
                      <CardDescription>
                        {generatedQuestionnaire.description}
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <p className="text-sm text-muted-foreground">题目总数</p>
                          <p className="text-lg font-semibold">
                            {generatedQuestionnaire.questions?.length || 0}
                          </p>
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground">预计时长</p>
                          <p className="text-lg font-semibold">
                            {Math.ceil((generatedQuestionnaire.questions?.length || 0) * 1.5)} 分钟
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="questions" className="space-y-4">
                  <div className="max-h-96 overflow-y-auto space-y-3">
                    {generatedQuestionnaire.questions?.map((question: any, index: number) => (
                      <Card key={question.id || index}>
                        <CardHeader>
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <p className="text-sm font-medium">
                                {index + 1}. {question.title}
                              </p>
                              <Badge variant="outline" className="mt-1">
                                {question.dimension}
                              </Badge>
                            </div>
                            <Badge variant="secondary">
                              {question.type === 'single_choice' ? '单选' :
                               question.type === 'multiple_choice' ? '多选' :
                               question.type === 'rating_scale' ? '评分' : '其他'}
                            </Badge>
                          </div>
                        </CardHeader>
                        {question.options && (
                          <CardContent>
                            <div className="space-y-1">
                              {question.options.map((option: any, optIndex: number) => (
                                <p key={optIndex} className="text-sm text-muted-foreground">
                                  {option.label}. {option.text}
                                </p>
                              ))}
                            </div>
                          </CardContent>
                        )}
                      </Card>
                    ))}
                  </div>
                </TabsContent>

                <TabsContent value="settings" className="space-y-4">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-base">评分设置</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>启用评分:</span>
                        <span className="font-medium">是</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>及格分数:</span>
                        <span className="font-medium">60分</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>显示分数:</span>
                        <span className="font-medium">完成后显示</span>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
              </Tabs>
            ) : (
              <div className="flex items-center justify-center h-64">
                <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
              </div>
            )}
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <div className="flex items-center space-x-2">
          <Sparkles className="h-5 w-5 text-primary" />
          <CardTitle>AI 智能问卷生成</CardTitle>
        </div>
        <CardDescription>
          使用 AI 快速生成专业的岗位评估问卷
        </CardDescription>
      </CardHeader>
      <CardContent>
        {/* Progress Steps */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            {[1, 2, 3, 4].map((step) => (
              <div
                key={step}
                className={`flex items-center ${
                  step < 4 ? 'flex-1' : ''
                }`}
              >
                <div
                  className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${
                    step <= currentStep
                      ? 'bg-primary text-primary-foreground border-primary'
                      : 'bg-background text-muted-foreground border-muted'
                  }`}
                >
                  {step < currentStep ? (
                    <CheckCircle className="h-4 w-4" />
                  ) : (
                    <span className="text-sm">{step}</span>
                  )}
                </div>
                {step < 4 && (
                  <div
                    className={`flex-1 h-0.5 mx-2 ${
                      step < currentStep ? 'bg-primary' : 'bg-muted'
                    }`}
                  />
                )}
              </div>
            ))}
          </div>
          <div className="flex justify-between mt-2">
            <span className="text-xs text-muted-foreground">选择模板</span>
            <span className="text-xs text-muted-foreground">配置维度</span>
            <span className="text-xs text-muted-foreground">自定义</span>
            <span className="text-xs text-muted-foreground">预览</span>
          </div>
        </div>

        {/* Step Content */}
        <div className="min-h-[400px]">{renderStepContent()}</div>

        {/* Navigation Buttons */}
        <div className="flex justify-between mt-8">
          <Button
            variant="outline"
            onClick={() => {
              if (currentStep > 1) {
                setCurrentStep(currentStep - 1);
              } else if (onCancel) {
                onCancel();
              }
            }}
            disabled={isGenerating}
          >
            <ChevronLeft className="h-4 w-4 mr-1" />
            {currentStep === 1 ? '取消' : '上一步'}
          </Button>

          {currentStep < 3 ? (
            <Button
              onClick={() => setCurrentStep(currentStep + 1)}
              disabled={
                (currentStep === 1 && !selectedTemplate) ||
                (currentStep === 2 && formData.dimensions.length < 3)
              }
            >
              下一步
              <ChevronRight className="h-4 w-4 ml-1" />
            </Button>
          ) : currentStep === 3 ? (
            <Button
              onClick={handleGenerate}
              disabled={isGenerating || formData.dimensions.length < 3}
            >
              {isGenerating ? (
                <>
                  <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                  生成中...
                </>
              ) : (
                <>
                  <Sparkles className="h-4 w-4 mr-1" />
                  生成问卷
                </>
              )}
            </Button>
          ) : (
            <Button onClick={handleSave}>
              <CheckCircle className="h-4 w-4 mr-1" />
              保存问卷
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
}