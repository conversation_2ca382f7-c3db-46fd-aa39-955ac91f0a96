"""auto_merge_heads

Revision ID: f63ed8676977
Revises: 2655b704e334
Create Date: 2025-08-13 02:28:14.831491

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'f63ed8676977'
down_revision: Union[str, None] = '2655b704e334'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass