"""
Admin API endpoints for managing application forms
"""
from typing import Any, List, Optional
from fastapi import APIRouter, Depends, Query, status
from sqlalchemy.ext.asyncio import AsyncSession
from app.api import deps
from app.core.database import get_db
from app.core.exceptions import not_found, conflict, forbidden, bad_request
from app.core.permissions import check_permission
from app.core.enums import Permission
from app.crud import application_form as form_crud
from app.models.user import User
from app.schemas.application_form import (
    ApplicationFormCreate,
    ApplicationFormUpdate,
    ApplicationFormResponse,
    ApplicationFormListResponse
)
from app.schemas.submission import ApplicationStatistics
import logging

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/", response_model=ApplicationFormResponse)
async def create_form(
    *,
    db: AsyncSession = Depends(get_db),
    form_in: ApplicationFormCreate,
    current_user: User = Depends(check_permission(Permission.CANDIDATES_WRITE))
) -> Any:
    """
    Create a new application form
    
    Requires CANDIDATES_WRITE permission.
    """
    try:
        form = await form_crud.create_with_validation(
            db,
            obj_in=form_in,
            created_by=current_user.id
        )
        
        logger.info(f"Application form created: {form.id} by user {current_user.id}")
        return form
        
    except ValueError as e:
        raise bad_request(str(e))
    except Exception as e:
        logger.error(f"Error creating form: {str(e)}")
        raise bad_request("FORM_CREATE_FAILED")


@router.get("/", response_model=ApplicationFormListResponse)
async def list_forms(
    db: AsyncSession = Depends(get_db),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    active_only: bool = Query(False),
    current_user: User = Depends(check_permission(Permission.CANDIDATES_WRITE))
) -> Any:
    """
    List all application forms
    
    Requires CANDIDATES_WRITE permission.
    """
    if active_only:
        forms = await form_crud.get_active_forms(db, skip=skip, limit=limit)
    else:
        forms = await form_crud.get_multi(db, skip=skip, limit=limit)
    
    total = await form_crud.count(db)
    
    return ApplicationFormListResponse(
        items=forms,
        total=total,
        skip=skip,
        limit=limit
    )


@router.get("/my-forms", response_model=ApplicationFormListResponse)
async def list_my_forms(
    db: AsyncSession = Depends(get_db),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    active_only: bool = Query(False),
    current_user: User = Depends(deps.get_current_active_user)
) -> Any:
    """
    List forms created by current user
    
    Available to all authenticated users.
    """
    forms = await form_crud.get_multi_by_creator(
        db,
        creator_id=current_user.id,
        skip=skip,
        limit=limit,
        active_only=active_only
    )
    
    # Count total for pagination
    all_forms = await form_crud.get_multi_by_creator(
        db,
        creator_id=current_user.id,
        skip=0,
        limit=10000,
        active_only=active_only
    )
    total = len(all_forms)
    
    return ApplicationFormListResponse(
        items=forms,
        total=total,
        skip=skip,
        limit=limit
    )


@router.get("/{form_id}", response_model=ApplicationFormResponse)
async def get_form(
    *,
    db: AsyncSession = Depends(get_db),
    form_id: str,
    current_user: User = Depends(check_permission(Permission.CANDIDATES_WRITE))
) -> Any:
    """
    Get application form by ID
    
    Requires CANDIDATES_WRITE permission.
    """
    form = await form_crud.get_with_stats(db, id=form_id)
    
    if not form:
        raise not_found("FORM_NOT_FOUND")
    
    return form


@router.get("/{form_id}/statistics", response_model=ApplicationStatistics)
async def get_form_statistics(
    *,
    db: AsyncSession = Depends(get_db),
    form_id: str,
    current_user: User = Depends(check_permission(Permission.CANDIDATES_WRITE))
) -> Any:
    """
    Get submission statistics for a form
    
    Requires CANDIDATES_WRITE permission.
    """
    # Verify form exists
    form = await form_crud.get(db, id=form_id)
    if not form:
        raise not_found("FORM_NOT_FOUND")
    
    # Get statistics
    from app.crud import submission as submission_crud
    stats = await submission_crud.get_statistics(db, form_id=form_id)
    
    return ApplicationStatistics(**stats)


@router.patch("/{form_id}", response_model=ApplicationFormResponse)
async def update_form(
    *,
    db: AsyncSession = Depends(get_db),
    form_id: str,
    form_in: ApplicationFormUpdate,
    current_user: User = Depends(check_permission(Permission.CANDIDATES_WRITE))
) -> Any:
    """
    Update application form
    
    Requires CANDIDATES_WRITE permission.
    """
    form = await form_crud.get(db, id=form_id)
    
    if not form:
        raise not_found("FORM_NOT_FOUND")
    
    # Only creator or superuser can update
    if form.created_by != current_user.id and not current_user.is_superuser:
        raise forbidden("NOT_FORM_OWNER")
    
    try:
        form = await form_crud.update_with_validation(
            db,
            db_obj=form,
            obj_in=form_in
        )
        
        logger.info(f"Form updated: {form_id} by user {current_user.id}")
        return form
        
    except ValueError as e:
        raise bad_request(str(e))
    except Exception as e:
        logger.error(f"Error updating form: {str(e)}")
        raise bad_request("FORM_UPDATE_FAILED")


@router.delete("/{form_id}")
async def deactivate_form(
    *,
    db: AsyncSession = Depends(get_db),
    form_id: str,
    current_user: User = Depends(check_permission(Permission.CANDIDATES_WRITE))
) -> Any:
    """
    Deactivate application form (soft delete)
    
    Requires CANDIDATES_WRITE permission.
    Forms with submissions cannot be deleted, only deactivated.
    """
    form = await form_crud.get(db, id=form_id)
    
    if not form:
        raise not_found("FORM_NOT_FOUND")
    
    # Only creator or superuser can deactivate
    if form.created_by != current_user.id and not current_user.is_superuser:
        raise forbidden("NOT_FORM_OWNER")
    
    # Check if form has submissions
    if form.submission_count > 0:
        # Deactivate instead of delete
        await form_crud.deactivate(db, id=form_id)
        logger.info(f"Form deactivated: {form_id} by user {current_user.id}")
        return {"message": "Form deactivated successfully"}
    else:
        # Can safely delete if no submissions
        await form_crud.remove(db, id=form_id)
        logger.info(f"Form deleted: {form_id} by user {current_user.id}")
        return {"message": "Form deleted successfully"}


@router.post("/{form_id}/duplicate", response_model=ApplicationFormResponse)
async def duplicate_form(
    *,
    db: AsyncSession = Depends(get_db),
    form_id: str,
    new_slug: str = Query(..., min_length=1, max_length=100, pattern="^[a-z0-9-]+$"),
    new_title: Optional[str] = None,
    current_user: User = Depends(check_permission(Permission.CANDIDATES_WRITE))
) -> Any:
    """
    Duplicate an existing form with a new slug
    
    Requires CANDIDATES_WRITE permission.
    Useful for creating similar forms or templates.
    """
    # Get original form
    original_form = await form_crud.get(db, id=form_id)
    if not original_form:
        raise not_found("FORM_NOT_FOUND")
    
    # Check if new slug is available
    existing = await form_crud.get_by_slug(db, slug=new_slug, active_only=False)
    if existing:
        raise conflict("SLUG_ALREADY_EXISTS")
    
    # Create duplicate
    form_create = ApplicationFormCreate(
        title=new_title or f"{original_form.title} (Copy)",
        slug=new_slug,
        description=original_form.description,
        fields=original_form.fields,
        questionnaire_id=original_form.questionnaire_id,
        is_active=False,  # Start as inactive
        requires_resume=original_form.requires_resume,
        auto_approve=original_form.auto_approve,
        success_message=original_form.success_message,
        redirect_url=original_form.redirect_url,
        notify_on_submission=original_form.notify_on_submission,
        notification_emails=original_form.notification_emails
    )
    
    try:
        new_form = await form_crud.create_with_validation(
            db,
            obj_in=form_create,
            created_by=current_user.id
        )
        
        logger.info(f"Form duplicated: {form_id} -> {new_form.id} by user {current_user.id}")
        return new_form
        
    except ValueError as e:
        raise bad_request(str(e))
    except Exception as e:
        logger.error(f"Error duplicating form: {str(e)}")
        raise bad_request("FORM_DUPLICATE_FAILED")