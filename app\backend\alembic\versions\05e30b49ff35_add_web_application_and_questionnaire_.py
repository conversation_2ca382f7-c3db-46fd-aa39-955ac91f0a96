"""Add web application and questionnaire models

Revision ID: 05e30b49ff35
Revises: 20250813_show_position_stats
Create Date: 2025-08-14 06:28:13.963425

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '05e30b49ff35'
down_revision: Union[str, None] = '20250813_show_position_stats'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Create questionnaires table first (no dependencies)
    op.create_table('questionnaires',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('title', sa.String(length=200), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('questions', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
    sa.Column('scoring_enabled', sa.<PERSON>(), nullable=True, server_default='false'),
    sa.Column('passing_score', sa.Float(), nullable=True),
    sa.Column('max_score', sa.Float(), nullable=True),
    sa.Column('time_limit_minutes', sa.Integer(), nullable=True),
    sa.Column('randomize_questions', sa.Boolean(), nullable=True, server_default='false'),
    sa.Column('show_progress', sa.Boolean(), nullable=True, server_default='true'),
    sa.Column('allow_back_navigation', sa.Boolean(), nullable=True, server_default='true'),
    sa.Column('completion_message', sa.Text(), nullable=True),
    sa.Column('show_score_on_completion', sa.Boolean(), nullable=True, server_default='false'),
    sa.Column('created_by', sa.BigInteger(), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=True, server_default='true'),
    sa.Column('response_count', sa.Integer(), nullable=True, server_default='0'),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['created_by'], ['users.id'], name=op.f('fk_questionnaires_created_by_users')),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_questionnaires'))
    )
    op.create_index('idx_questionnaire_active', 'questionnaires', ['is_active'], unique=False)
    op.create_index('idx_questionnaire_created_by', 'questionnaires', ['created_by'], unique=False)
    
    # Create application_forms table second (depends on questionnaires)
    op.create_table('application_forms',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('title', sa.String(length=200), nullable=False),
    sa.Column('slug', sa.String(length=100), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('fields', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
    sa.Column('questionnaire_id', sa.BigInteger(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True, server_default='true'),
    sa.Column('requires_resume', sa.Boolean(), nullable=True, server_default='false'),
    sa.Column('auto_approve', sa.Boolean(), nullable=True, server_default='false'),
    sa.Column('created_by', sa.BigInteger(), nullable=False),
    sa.Column('submission_count', sa.Integer(), nullable=True, server_default='0'),
    sa.Column('success_message', sa.Text(), nullable=True),
    sa.Column('redirect_url', sa.String(length=500), nullable=True),
    sa.Column('notify_on_submission', sa.Boolean(), nullable=True, server_default='false'),
    sa.Column('notification_emails', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['created_by'], ['users.id'], name=op.f('fk_application_forms_created_by_users')),
    sa.ForeignKeyConstraint(['questionnaire_id'], ['questionnaires.id'], name=op.f('fk_application_forms_questionnaire_id_questionnaires')),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_application_forms'))
    )
    op.create_index('idx_application_form_active', 'application_forms', ['is_active'], unique=False)
    op.create_index('idx_application_form_created_by', 'application_forms', ['created_by'], unique=False)
    op.create_index(op.f('ix_application_forms_slug'), 'application_forms', ['slug'], unique=True)
    
    # Create application_submissions table third (depends on application_forms and candidates)
    op.create_table('application_submissions',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('form_id', sa.BigInteger(), nullable=False),
    sa.Column('candidate_id', sa.BigInteger(), nullable=True),
    sa.Column('form_data', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
    sa.Column('resume_file_url', sa.String(length=500), nullable=True),
    sa.Column('resume_parsed_data', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('submitted_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('submission_ip', sa.String(length=45), nullable=True),
    sa.Column('user_agent', sa.String(length=500), nullable=True),
    sa.Column('is_draft', sa.Boolean(), nullable=True, server_default='false'),
    sa.Column('draft_saved_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('completed_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('validation_passed', sa.Boolean(), nullable=True, server_default='false'),
    sa.Column('validation_errors', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('confirmation_email_sent', sa.Boolean(), nullable=True, server_default='false'),
    sa.Column('confirmation_email_sent_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('referrer_url', sa.String(length=500), nullable=True),
    sa.Column('utm_source', sa.String(length=100), nullable=True),
    sa.Column('utm_medium', sa.String(length=100), nullable=True),
    sa.Column('utm_campaign', sa.String(length=100), nullable=True),
    sa.Column('captcha_token', sa.String(length=500), nullable=True),
    sa.Column('captcha_verified', sa.Boolean(), nullable=True, server_default='false'),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['candidate_id'], ['candidates.id'], name=op.f('fk_application_submissions_candidate_id_candidates')),
    sa.ForeignKeyConstraint(['form_id'], ['application_forms.id'], name=op.f('fk_application_submissions_form_id_application_forms')),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_application_submissions')),
    sa.UniqueConstraint('candidate_id', name=op.f('uq_application_submissions_candidate_id'))
    )
    op.create_index('idx_submission_candidate', 'application_submissions', ['candidate_id'], unique=False)
    op.create_index('idx_submission_draft', 'application_submissions', ['is_draft'], unique=False)
    op.create_index('idx_submission_form', 'application_submissions', ['form_id'], unique=False)
    op.create_index('idx_submission_ip', 'application_submissions', ['submission_ip'], unique=False)
    op.create_index('idx_submission_submitted_at', 'application_submissions', ['submitted_at'], unique=False)
    
    # Create questionnaire_responses table last (depends on questionnaires, candidates, and application_submissions)
    op.create_table('questionnaire_responses',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('questionnaire_id', sa.BigInteger(), nullable=False),
    sa.Column('candidate_id', sa.BigInteger(), nullable=False),
    sa.Column('submission_id', sa.BigInteger(), nullable=True),
    sa.Column('responses', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
    sa.Column('total_score', sa.Float(), nullable=True),
    sa.Column('passed', sa.Boolean(), nullable=True),
    sa.Column('score_breakdown', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('started_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('submitted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('time_taken_seconds', sa.Integer(), nullable=True),
    sa.Column('ip_address', sa.String(length=45), nullable=True),
    sa.Column('user_agent', sa.String(length=500), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['candidate_id'], ['candidates.id'], name=op.f('fk_questionnaire_responses_candidate_id_candidates')),
    sa.ForeignKeyConstraint(['questionnaire_id'], ['questionnaires.id'], name=op.f('fk_questionnaire_responses_questionnaire_id_questionnaires')),
    sa.ForeignKeyConstraint(['submission_id'], ['application_submissions.id'], name=op.f('fk_questionnaire_responses_submission_id_application_submissions')),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_questionnaire_responses'))
    )
    op.create_index('idx_questionnaire_response_candidate', 'questionnaire_responses', ['candidate_id'], unique=False)
    op.create_index('idx_questionnaire_response_questionnaire', 'questionnaire_responses', ['questionnaire_id'], unique=False)
    op.create_index('idx_questionnaire_response_submission', 'questionnaire_responses', ['submission_id'], unique=False)
    op.create_index('idx_questionnaire_response_submitted_at', 'questionnaire_responses', ['submitted_at'], unique=False)
    
    # Create enum type for approval status
    approval_status_enum = postgresql.ENUM('pending', 'approved', 'rejected', 'withdrawn', name='approvalstatus')
    approval_status_enum.create(op.get_bind())
    
    # Add approval-related columns to candidates table
    op.add_column('candidates', sa.Column('approval_status', approval_status_enum, nullable=True))
    op.add_column('candidates', sa.Column('reviewed_by', sa.BigInteger(), nullable=True))
    op.add_column('candidates', sa.Column('reviewed_at', sa.DateTime(timezone=True), nullable=True))
    op.add_column('candidates', sa.Column('review_notes', sa.Text(), nullable=True))
    op.create_index('idx_candidate_approval_status', 'candidates', ['approval_status'], unique=False)
    op.create_index('idx_candidate_reviewed_by', 'candidates', ['reviewed_by'], unique=False)
    op.create_foreign_key(op.f('fk_candidates_reviewed_by_users'), 'candidates', 'users', ['reviewed_by'], ['id'])


def downgrade() -> None:
    # Remove foreign key and indices from candidates
    op.drop_constraint(op.f('fk_candidates_reviewed_by_users'), 'candidates', type_='foreignkey')
    op.drop_index('idx_candidate_reviewed_by', table_name='candidates')
    op.drop_index('idx_candidate_approval_status', table_name='candidates')
    
    # Remove columns from candidates
    op.drop_column('candidates', 'review_notes')
    op.drop_column('candidates', 'reviewed_at')
    op.drop_column('candidates', 'reviewed_by')
    op.drop_column('candidates', 'approval_status')
    
    # Drop enum type
    approval_status_enum = postgresql.ENUM('pending', 'approved', 'rejected', 'withdrawn', name='approvalstatus')
    approval_status_enum.drop(op.get_bind())
    
    # Drop tables in reverse order
    op.drop_index('idx_questionnaire_response_submitted_at', table_name='questionnaire_responses')
    op.drop_index('idx_questionnaire_response_submission', table_name='questionnaire_responses')
    op.drop_index('idx_questionnaire_response_questionnaire', table_name='questionnaire_responses')
    op.drop_index('idx_questionnaire_response_candidate', table_name='questionnaire_responses')
    op.drop_table('questionnaire_responses')
    
    op.drop_index('idx_submission_submitted_at', table_name='application_submissions')
    op.drop_index('idx_submission_ip', table_name='application_submissions')
    op.drop_index('idx_submission_form', table_name='application_submissions')
    op.drop_index('idx_submission_draft', table_name='application_submissions')
    op.drop_index('idx_submission_candidate', table_name='application_submissions')
    op.drop_table('application_submissions')
    
    op.drop_index(op.f('ix_application_forms_slug'), table_name='application_forms')
    op.drop_index('idx_application_form_created_by', table_name='application_forms')
    op.drop_index('idx_application_form_active', table_name='application_forms')
    op.drop_table('application_forms')
    
    op.drop_index('idx_questionnaire_created_by', table_name='questionnaires')
    op.drop_index('idx_questionnaire_active', table_name='questionnaires')
    op.drop_table('questionnaires')