"""
Utility functions for the application
"""
import time
import threading
from typing import Optional
from app.core.config import settings


class SnowflakeIDGenerator:
    """
    Snowflake ID Generator
    
    Generates unique 64-bit IDs based on Twitter's Snowflake algorithm
    
    Structure:
    - 1 bit: Always 0 (positive number)
    - 41 bits: Timestamp (milliseconds since epoch)
    - 5 bits: Datacenter ID
    - 5 bits: Machine ID
    - 12 bits: Sequence number
    """
    
    def __init__(
        self,
        datacenter_id: int = 1,
        machine_id: int = 1,
        epoch: int = 1609459200000  # 2021-01-01 00:00:00 UTC
    ):
        self.datacenter_id = datacenter_id
        self.machine_id = machine_id
        self.epoch = epoch
        self.sequence = 0
        self.last_timestamp = -1
        self.lock = threading.Lock()
        
        # Bit lengths
        self.datacenter_id_bits = 5
        self.machine_id_bits = 5
        self.sequence_bits = 12
        
        # Max values
        self.max_datacenter_id = (1 << self.datacenter_id_bits) - 1
        self.max_machine_id = (1 << self.machine_id_bits) - 1
        self.max_sequence = (1 << self.sequence_bits) - 1
        
        # Bit shifts
        self.machine_id_shift = self.sequence_bits
        self.datacenter_id_shift = self.sequence_bits + self.machine_id_bits
        self.timestamp_shift = self.sequence_bits + self.machine_id_bits + self.datacenter_id_bits
        
        # Validate IDs
        if datacenter_id > self.max_datacenter_id or datacenter_id < 0:
            raise ValueError(f"Datacenter ID must be between 0 and {self.max_datacenter_id}")
        if machine_id > self.max_machine_id or machine_id < 0:
            raise ValueError(f"Machine ID must be between 0 and {self.max_machine_id}")
    
    def _current_timestamp(self) -> int:
        """Get current timestamp in milliseconds"""
        return int(time.time() * 1000)
    
    def _wait_next_millis(self, last_timestamp: int) -> int:
        """Wait until next millisecond"""
        timestamp = self._current_timestamp()
        while timestamp <= last_timestamp:
            timestamp = self._current_timestamp()
        return timestamp
    
    def generate(self) -> int:
        """Generate next ID"""
        with self.lock:
            timestamp = self._current_timestamp()
            
            if timestamp < self.last_timestamp:
                raise Exception("Clock moved backwards. Refusing to generate ID.")
            
            if timestamp == self.last_timestamp:
                self.sequence = (self.sequence + 1) & self.max_sequence
                if self.sequence == 0:
                    timestamp = self._wait_next_millis(self.last_timestamp)
            else:
                self.sequence = 0
            
            self.last_timestamp = timestamp
            
            # Generate ID
            id_value = (
                ((timestamp - self.epoch) << self.timestamp_shift) |
                (self.datacenter_id << self.datacenter_id_shift) |
                (self.machine_id << self.machine_id_shift) |
                self.sequence
            )
            
            return id_value
    
    def parse_id(self, snowflake_id: int) -> dict:
        """Parse Snowflake ID to extract components"""
        timestamp = ((snowflake_id >> self.timestamp_shift) + self.epoch) / 1000
        datacenter_id = (snowflake_id >> self.datacenter_id_shift) & self.max_datacenter_id
        machine_id = (snowflake_id >> self.machine_id_shift) & self.max_machine_id
        sequence = snowflake_id & self.max_sequence
        
        return {
            "timestamp": timestamp,
            "datacenter_id": datacenter_id,
            "machine_id": machine_id,
            "sequence": sequence,
            "datetime": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(timestamp))
        }


# Global generator instance
_generator: Optional[SnowflakeIDGenerator] = None


def get_snowflake_generator() -> SnowflakeIDGenerator:
    """Get or create global Snowflake ID generator"""
    global _generator
    if _generator is None:
        _generator = SnowflakeIDGenerator(
            datacenter_id=settings.SNOWFLAKE_DATACENTER_ID,
            machine_id=settings.SNOWFLAKE_MACHINE_ID
        )
    return _generator


def generate_snowflake_id() -> int:
    """Generate a new Snowflake ID"""
    generator = get_snowflake_generator()
    return generator.generate()


def parse_snowflake_id(snowflake_id: int) -> dict:
    """Parse a Snowflake ID to extract its components"""
    generator = get_snowflake_generator()
    return generator.parse_id(snowflake_id)