# Execute BASE PRP with Agent Support

Implement a feature using the PRP file with intelligent agent orchestration.

## PRP File: $ARGUMENTS

## Execution Process with Agent Enhancement

1. **Agent-Powered Load & Analysis**
   - Use `prp-execution-coordinator` as the primary orchestrator
   - The coordinator will automatically:
     - Read and analyze the specified PRP file
     - Identify complexity and required specialized agents
     - Coordinate with other agents throughout the 6-step workflow
   - Additional context gathering through web searches and codebase exploration

2. **ULTRATHINK with Strategic Planning**
   - Automatically invoke `strategic-planning-analyst` for complex PRPs
   - Create comprehensive implementation strategy
   - Break down into TodoWrite tasks with proper dependencies
   - Identify and document implementation patterns from existing code
   - Generate detailed execution roadmap

3. **Intelligent Implementation**
   - Use `prp-implementation-engineer` for code generation
   - Parallel execution where possible with multiple specialized agents
   - Follow established patterns and conventions
   - Implement with quality gates at each step

4. **Continuous Validation**
   - Deploy `validation-lifecycle-manager` for comprehensive testing
   - Run validation loops with automatic fix attempts
   - Progressive validation: syntax → types → lint → security → tests
   - Ensure ≥80% test coverage

5. **Quality Assurance & Completion**
   - Verify all PRP requirements are met
   - Run final validation suite
   - Generate completion report with metrics
   - Re-read PRP to ensure nothing was missed

6. **Error Recovery & Iteration**
   - Automatic retry logic for failed validations
   - Pattern-based error fixing from PRP error patterns
   - Escalation to specialized agents for complex issues

## Agent Orchestration Strategy

### For Sprint PRPs (infrastructure, auth, etc.):
```
/task description="Execute PRP: $ARGUMENTS" prompt="
Use prp-execution-coordinator to implement $ARGUMENTS following the complete 6-step workflow:
1. Load and analyze PRP with context gathering
2. Strategic planning with ULTRATHINK
3. Implementation with best practices
4. Validation with automatic fixes
5. Compliance verification
6. Error recovery as needed

The coordinator should automatically engage:
- strategic-planning-analyst for complex analysis
- prp-implementation-engineer for code implementation
- validation-lifecycle-manager for testing and validation

Ensure all success criteria in the PRP are met before completion.
" subagent_type="prp-execution-coordinator"
```

### For Complex Feature PRPs:
```
Think harder about the implementation and use parallel agents for:
- Architecture decisions (strategic-planning-analyst)
- Frontend components (prp-implementation-engineer with UI focus)
- Backend services (prp-implementation-engineer with API focus)
- Comprehensive testing (validation-lifecycle-manager)
```

### For Simple PRPs:
```
Direct implementation with prp-implementation-engineer, followed by validation-lifecycle-manager
```

## Automatic Agent Selection Logic

The system will automatically select appropriate agents based on PRP characteristics:

1. **Complexity Score > 0.8**: Full orchestration with all agents
2. **Multiple Domains**: Parallel specialized agents
3. **Critical Features**: Enhanced validation with validation-lifecycle-manager
4. **Infrastructure**: Deep analysis with strategic-planning-analyst
5. **UI Components**: Enhanced with UI-focused implementation

## Enhanced Validation Loop

- **Level 1**: Syntax and type checking
- **Level 2**: Linting and code quality
- **Level 3**: Security scanning
- **Level 4**: Unit and integration tests
- **Level 5**: E2E testing and performance
- **Level 6**: Documentation completeness
- **Level 7**: Integration validation
- **Level 8**: Final compliance check

## Benefits of Agent Enhancement

1. **Parallel Processing**: Multiple agents work simultaneously
2. **Specialized Expertise**: Each agent brings domain knowledge
3. **Automatic Coordination**: No manual agent management needed
4. **Error Recovery**: Intelligent retry and fix mechanisms
5. **Quality Assurance**: Comprehensive validation at every step

Note: The enhanced execute-prp command now leverages the full power of the agent system for more reliable and efficient PRP execution.