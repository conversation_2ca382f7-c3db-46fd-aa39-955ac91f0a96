"""auto_merge_heads

Revision ID: c742c52f7150
Revises: 6ee471bc5ba5
Create Date: 2025-08-13 01:53:22.018836

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'c742c52f7150'
down_revision: Union[str, None] = '6ee471bc5ba5'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    pass