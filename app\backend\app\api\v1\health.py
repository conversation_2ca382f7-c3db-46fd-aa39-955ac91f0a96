"""
TalentForge Pro - 统一健康检查端点
监控所有服务状态，提供详细的健康信息
"""

import asyncio
import time
from typing import Dict, Any, Optional
from datetime import datetime, timezone

from fastapi import APIRouter, Depends, status
from fastapi.responses import JSONResponse
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text
import redis.asyncio as redis
import httpx
from minio import Minio
from minio.error import S3Error

from app.core.database import get_db
from app.core.config import settings

router = APIRouter()


class HealthStatus:
    """健康状态枚举"""
    HEALTHY = "healthy"
    UNHEALTHY = "unhealthy"
    DEGRADED = "degraded"


class ServiceHealth:
    """服务健康检查结果"""
    def __init__(self, name: str, status: str, message: str = "", 
                 response_time: Optional[float] = None, 
                 details: Optional[Dict[str, Any]] = None):
        self.name = name
        self.status = status
        self.message = message
        self.response_time = response_time
        self.details = details or {}
        
    def to_dict(self) -> Dict[str, Any]:
        result = {
            "name": self.name,
            "status": self.status,
            "message": self.message
        }
        if self.response_time is not None:
            result["response_time_ms"] = round(self.response_time * 1000, 2)
        if self.details:
            result["details"] = self.details
        return result


class HealthChecker:
    """统一健康检查器"""
    
    @staticmethod
    async def check_database(db: AsyncSession) -> ServiceHealth:
        """检查PostgreSQL数据库健康状态"""
        start_time = time.time()
        try:
            # 执行简单查询测试连接
            result = await db.execute(text("SELECT 1"))
            await db.commit()
            
            # 获取数据库版本和连接信息
            version_result = await db.execute(text("SELECT version()"))
            version = version_result.scalar()
            
            # 检查pgvector扩展
            vector_result = await db.execute(
                text("SELECT extversion FROM pg_extension WHERE extname = 'vector'")
            )
            vector_version = vector_result.scalar()
            
            response_time = time.time() - start_time
            
            details = {
                "version": version.split()[0] if version else "unknown",
                "pgvector_version": vector_version or "not installed"
            }
            
            return ServiceHealth(
                name="PostgreSQL",
                status=HealthStatus.HEALTHY,
                message="Database connection successful",
                response_time=response_time,
                details=details
            )
        except Exception as e:
            response_time = time.time() - start_time
            return ServiceHealth(
                name="PostgreSQL",
                status=HealthStatus.UNHEALTHY,
                message=f"Database connection failed: {str(e)}",
                response_time=response_time
            )
    
    @staticmethod
    async def check_redis() -> ServiceHealth:
        """检查Redis健康状态"""
        start_time = time.time()
        try:
            # 创建Redis连接
            redis_url = settings.REDIS_URL
            if settings.REDIS_PASSWORD:
                redis_url = redis_url.replace("redis://", f"redis://:{settings.REDIS_PASSWORD}@")
            client = await redis.from_url(redis_url)
            
            # Ping测试
            await client.ping()
            
            # 获取Redis信息
            info = await client.info()
            
            # 测试写入和读取
            test_key = "health_check_test"
            test_value = str(datetime.now(timezone.utc))
            await client.set(test_key, test_value, ex=10)
            retrieved = await client.get(test_key)
            
            await client.close()
            
            response_time = time.time() - start_time
            
            details = {
                "version": info.get("redis_version", "unknown"),
                "used_memory": info.get("used_memory_human", "unknown"),
                "connected_clients": info.get("connected_clients", 0),
                "test_passed": retrieved == test_value.encode()
            }
            
            return ServiceHealth(
                name="Redis",
                status=HealthStatus.HEALTHY,
                message="Redis connection successful",
                response_time=response_time,
                details=details
            )
        except Exception as e:
            response_time = time.time() - start_time
            return ServiceHealth(
                name="Redis",
                status=HealthStatus.UNHEALTHY,
                message=f"Redis connection failed: {str(e)}",
                response_time=response_time
            )
    
    @staticmethod
    async def check_minio() -> ServiceHealth:
        """检查MinIO健康状态"""
        start_time = time.time()
        try:
            # 创建MinIO客户端
            client = Minio(
                settings.MINIO_ENDPOINT,
                access_key=settings.MINIO_ACCESS_KEY,
                secret_key=settings.MINIO_SECRET_KEY,
                secure=settings.MINIO_USE_SSL
            )
            
            # 列出存储桶测试连接
            buckets = list(client.list_buckets())
            bucket_names = [b.name for b in buckets]
            
            # 检查必需的存储桶
            required_buckets = [
                settings.MINIO_BUCKET_RESUMES,
                settings.MINIO_BUCKET_JDS,
                settings.MINIO_BUCKET
            ]
            
            missing_buckets = [b for b in required_buckets if b not in bucket_names]
            
            response_time = time.time() - start_time
            
            status = HealthStatus.HEALTHY
            message = "MinIO connection successful"
            
            if missing_buckets:
                status = HealthStatus.DEGRADED
                message = f"Missing buckets: {', '.join(missing_buckets)}"
            
            details = {
                "buckets": bucket_names,
                "required_buckets": required_buckets,
                "missing_buckets": missing_buckets
            }
            
            return ServiceHealth(
                name="MinIO",
                status=status,
                message=message,
                response_time=response_time,
                details=details
            )
        except Exception as e:
            response_time = time.time() - start_time
            return ServiceHealth(
                name="MinIO",
                status=HealthStatus.UNHEALTHY,
                message=f"MinIO connection failed: {str(e)}",
                response_time=response_time
            )
    
    @staticmethod
    async def check_ollama() -> ServiceHealth:
        """检查Ollama健康状态"""
        start_time = time.time()
        try:
            async with httpx.AsyncClient() as client:
                # 检查Ollama API
                ollama_host = getattr(settings, 'OLLAMA_HOST', 'http://ollama:11434')
                response = await client.get(
                    f"{ollama_host}/api/tags",
                    timeout=5.0
                )
                
                if response.status_code == 200:
                    data = response.json()
                    models = data.get("models", [])
                    model_names = [m.get("name", "") for m in models]
                    
                    # 检查所需模型
                    required_model = getattr(settings, 'OLLAMA_MODEL', 'bge-m3')
                    has_required = any(required_model in name for name in model_names)
                    
                    response_time = time.time() - start_time
                    
                    status = HealthStatus.HEALTHY
                    message = "Ollama service available"
                    
                    if not has_required:
                        status = HealthStatus.DEGRADED
                        message = f"Required model '{required_model}' not found"
                    
                    details = {
                        "available_models": model_names,
                        "required_model": required_model,
                        "has_required_model": has_required
                    }
                    
                    return ServiceHealth(
                        name="Ollama",
                        status=status,
                        message=message,
                        response_time=response_time,
                        details=details
                    )
                else:
                    raise Exception(f"Ollama API returned status {response.status_code}")
                    
        except Exception as e:
            response_time = time.time() - start_time
            return ServiceHealth(
                name="Ollama",
                status=HealthStatus.UNHEALTHY,
                message=f"Ollama service unavailable: {str(e)}",
                response_time=response_time
            )
    
    @staticmethod
    async def check_celery() -> ServiceHealth:
        """检查Celery健康状态"""
        start_time = time.time()
        try:
            # Try to import Celery app if it exists
            try:
                from app.celery.app import celery_app
            except ImportError:
                # Celery module not configured yet
                return ServiceHealth(
                    name="Celery",
                    status=HealthStatus.DEGRADED,
                    message="Celery module not configured",
                    response_time=time.time() - start_time
                )
            
            # 获取活动的worker
            inspect = celery_app.control.inspect()
            stats = inspect.stats()
            active_queues = inspect.active_queues()
            
            if stats and active_queues:
                worker_count = len(stats)
                total_tasks = sum(len(tasks) for tasks in (inspect.active() or {}).values())
                
                response_time = time.time() - start_time
                
                details = {
                    "worker_count": worker_count,
                    "active_tasks": total_tasks,
                    "queues": list(active_queues.keys()) if active_queues else []
                }
                
                return ServiceHealth(
                    name="Celery",
                    status=HealthStatus.HEALTHY,
                    message=f"{worker_count} workers active",
                    response_time=response_time,
                    details=details
                )
            else:
                return ServiceHealth(
                    name="Celery",
                    status=HealthStatus.UNHEALTHY,
                    message="No active workers found",
                    response_time=time.time() - start_time
                )
                
        except Exception as e:
            response_time = time.time() - start_time
            return ServiceHealth(
                name="Celery",
                status=HealthStatus.UNHEALTHY,
                message=f"Celery check failed: {str(e)}",
                response_time=response_time
            )


@router.get("/health", tags=["Health"])
async def health_check():
    """
    基础健康检查端点
    返回简单的健康状态，用于负载均衡器探测
    """
    return {"status": "healthy", "timestamp": datetime.now(timezone.utc).isoformat()}


@router.get("/health/live", tags=["Health"])
async def liveness_probe():
    """
    存活探测端点
    检查应用是否正在运行
    """
    return {
        "status": "alive",
        "service": "TalentForge Pro API",
        "version": settings.VERSION,
        "timestamp": datetime.now(timezone.utc).isoformat()
    }


@router.get("/health/ready", tags=["Health"])
async def readiness_probe(db: AsyncSession = Depends(get_db)):
    """
    就绪探测端点
    检查应用是否准备好接收流量
    """
    # 快速检查关键依赖
    checks = []
    
    # 检查数据库
    try:
        await db.execute(text("SELECT 1"))
        checks.append({"database": "ready"})
    except:
        return JSONResponse(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            content={"status": "not_ready", "failed_checks": ["database"]}
        )
    
    # 检查Redis
    try:
        redis_url = settings.REDIS_URL
        if settings.REDIS_PASSWORD:
            redis_url = redis_url.replace("redis://", f"redis://:{settings.REDIS_PASSWORD}@")
        client = await redis.from_url(redis_url)
        await client.ping()
        await client.close()
        checks.append({"redis": "ready"})
    except:
        return JSONResponse(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            content={"status": "not_ready", "failed_checks": ["redis"]}
        )
    
    return {
        "status": "ready",
        "checks": checks,
        "timestamp": datetime.now(timezone.utc).isoformat()
    }


@router.get("/health/detailed", tags=["Health"])
async def detailed_health_check(db: AsyncSession = Depends(get_db)):
    """
    详细健康检查端点
    返回所有服务的详细健康状态
    """
    start_time = time.time()
    checker = HealthChecker()
    
    # 并发检查所有服务
    tasks = [
        checker.check_database(db),
        checker.check_redis(),
        checker.check_minio(),
        checker.check_ollama(),
        checker.check_celery()
    ]
    
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    # 处理结果
    services = []
    overall_status = HealthStatus.HEALTHY
    unhealthy_services = []
    degraded_services = []
    
    for result in results:
        if isinstance(result, Exception):
            # 如果检查本身失败
            service = ServiceHealth(
                name="Unknown",
                status=HealthStatus.UNHEALTHY,
                message=f"Health check failed: {str(result)}"
            )
        else:
            service = result
        
        services.append(service.to_dict())
        
        if service.status == HealthStatus.UNHEALTHY:
            overall_status = HealthStatus.UNHEALTHY
            unhealthy_services.append(service.name)
        elif service.status == HealthStatus.DEGRADED:
            if overall_status != HealthStatus.UNHEALTHY:
                overall_status = HealthStatus.DEGRADED
            degraded_services.append(service.name)
    
    total_time = time.time() - start_time
    
    response = {
        "status": overall_status,
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "total_check_time_ms": round(total_time * 1000, 2),
        "services": services,
        "summary": {
            "total_services": len(services),
            "healthy_services": len([s for s in services if s["status"] == HealthStatus.HEALTHY]),
            "degraded_services": degraded_services,
            "unhealthy_services": unhealthy_services
        },
        "environment": settings.ENVIRONMENT,
        "version": settings.VERSION
    }
    
    # 根据整体状态设置HTTP状态码
    if overall_status == HealthStatus.UNHEALTHY:
        return JSONResponse(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            content=response
        )
    elif overall_status == HealthStatus.DEGRADED:
        return JSONResponse(
            status_code=status.HTTP_207_MULTI_STATUS,
            content=response
        )
    else:
        return response


@router.get("/health/metrics", tags=["Health"])
async def health_metrics():
    """
    健康指标端点
    返回Prometheus格式的指标（如果启用）
    """
    if not getattr(settings, 'ENABLE_METRICS', False):
        return JSONResponse(
            status_code=status.HTTP_501_NOT_IMPLEMENTED,
            content={"error": "Metrics not enabled"}
        )
    
    # TODO: 集成Prometheus指标
    metrics = []
    metrics.append("# HELP talentforge_health_status Service health status (1=healthy, 0=unhealthy)")
    metrics.append("# TYPE talentforge_health_status gauge")
    
    # 这里可以添加更多指标
    
    return "\n".join(metrics)